<?php
// Fix headers already sent by using output buffering
ob_start();

session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database Configuration
$host = 'localhost';
$db_name = 'truc_tos';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$db_name;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Enhanced database structure with all new requirements
    
    // Enhanced users table with password reset and status
    $pdo->exec("CREATE TABLE IF NOT EXISTS users (
        user_id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        phone VARCHAR(15) NOT NULL,
        company_name VARCHAR(100),
        company_registration VARCHAR(50),
        vat_number VARCHAR(20),
        physical_address TEXT,
        billing_address TEXT,
        user_type ENUM('customer', 'dealer', 'admin') DEFAULT 'customer',
        status ENUM('pending','active','suspended','rejected') DEFAULT 'pending',
        registered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_login TIMESTAMP NULL,
        profile_image VARCHAR(255),
        email_verified BOOLEAN DEFAULT FALSE,
        phone_verified BOOLEAN DEFAULT FALSE,
        password_reset_token VARCHAR(255) NULL,
        password_reset_expires TIMESTAMP NULL,
        listing_limit INT DEFAULT 10,
        premium_until DATE NULL,
        failed_login_attempts INT DEFAULT 0,
        locked_until TIMESTAMP NULL,
        INDEX idx_user_type (user_type),
        INDEX idx_status (status),
        INDEX idx_email (email),
        INDEX idx_reset_token (password_reset_token)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // Enhanced categories with listing types
    $pdo->exec("CREATE TABLE IF NOT EXISTS categories (
        category_id INT AUTO_INCREMENT PRIMARY KEY,
        category_key VARCHAR(50) UNIQUE NOT NULL,
        category_name VARCHAR(100) NOT NULL,
        parent_category VARCHAR(50) NULL,
        listing_type ENUM('sale', 'rent-to-own', 'hire', 'auction') DEFAULT 'sale',
        icon VARCHAR(100) NOT NULL DEFAULT 'fas fa-tag',
        listing_label VARCHAR(100) NOT NULL,
        mileage_label VARCHAR(50) DEFAULT 'Mileage (KM)',
        engine_label VARCHAR(50) DEFAULT 'Engine Type',
        show_transmission BOOLEAN DEFAULT TRUE,
        show_fuel_type BOOLEAN DEFAULT TRUE,
        show_year BOOLEAN DEFAULT TRUE,
        show_hours BOOLEAN DEFAULT FALSE,
        transmission_options TEXT,
        fuel_options TEXT,
        additional_fields TEXT,
        category_order INT DEFAULT 0,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_listing_type (listing_type),
        INDEX idx_parent_category (parent_category),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // Subcategories
    $pdo->exec("CREATE TABLE IF NOT EXISTS subcategories (
        subcategory_id INT AUTO_INCREMENT PRIMARY KEY,
        category_id INT NOT NULL,
        subcategory_key VARCHAR(50) NOT NULL,
        subcategory_name VARCHAR(100) NOT NULL,
        subcategory_order INT DEFAULT 0,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES categories(category_id) ON DELETE CASCADE,
        UNIQUE KEY unique_subcategory (category_id, subcategory_key),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // Enhanced makes system
    $pdo->exec("CREATE TABLE IF NOT EXISTS category_makes (
        make_id INT AUTO_INCREMENT PRIMARY KEY,
        category_id INT NOT NULL,
        make_name VARCHAR(100) NOT NULL,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES categories(category_id) ON DELETE CASCADE,
        UNIQUE KEY unique_make (category_id, make_name),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // Models system
    $pdo->exec("CREATE TABLE IF NOT EXISTS category_models (
        model_id INT AUTO_INCREMENT PRIMARY KEY,
        make_id INT NOT NULL,
        model_name VARCHAR(100) NOT NULL,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (make_id) REFERENCES category_makes(make_id) ON DELETE CASCADE,
        UNIQUE KEY unique_model (make_id, model_name),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // Variants system
    $pdo->exec("CREATE TABLE IF NOT EXISTS category_variants (
        variant_id INT AUTO_INCREMENT PRIMARY KEY,
        model_id INT NOT NULL,
        variant_name VARCHAR(100) NOT NULL,
        variant_description TEXT,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (model_id) REFERENCES category_models(model_id) ON DELETE CASCADE,
        UNIQUE KEY unique_variant (model_id, variant_name),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // System years
    $pdo->exec("CREATE TABLE IF NOT EXISTS system_years (
        year_id INT AUTO_INCREMENT PRIMARY KEY,
        year_value INT NOT NULL UNIQUE,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_year (year_value),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // Sales team management
    $pdo->exec("CREATE TABLE IF NOT EXISTS dealer_sales_team (
        team_id INT AUTO_INCREMENT PRIMARY KEY,
        dealer_id INT NOT NULL,
        name VARCHAR(100) NOT NULL,
        position VARCHAR(100),
        phone VARCHAR(15),
        email VARCHAR(100),
        whatsapp VARCHAR(15),
        photo VARCHAR(255),
        facebook_url VARCHAR(255),
        twitter_url VARCHAR(255),
        instagram_url VARCHAR(255),
        linkedin_url VARCHAR(255),
        youtube_url VARCHAR(255),
        tiktok_url VARCHAR(255),
        website_url VARCHAR(255),
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (dealer_id) REFERENCES users(user_id) ON DELETE CASCADE,
        INDEX idx_dealer (dealer_id),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // Dealer branches
    $pdo->exec("CREATE TABLE IF NOT EXISTS dealer_branches (
        branch_id INT AUTO_INCREMENT PRIMARY KEY,
        dealer_id INT NOT NULL,
        branch_name VARCHAR(100) NOT NULL,
        address TEXT NOT NULL,
        city VARCHAR(50) NOT NULL,
        region VARCHAR(50) NOT NULL,
        postal_code VARCHAR(10),
        phone VARCHAR(15),
        email VARCHAR(100),
        is_main_branch BOOLEAN DEFAULT FALSE,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (dealer_id) REFERENCES users(user_id) ON DELETE CASCADE,
        INDEX idx_dealer (dealer_id),
        INDEX idx_region (region),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // Enhanced vehicles table with all new fields
    $pdo->exec("CREATE TABLE IF NOT EXISTS vehicles (
        vehicle_id INT AUTO_INCREMENT PRIMARY KEY,
        dealer_id INT NOT NULL,
        category VARCHAR(50) NOT NULL,
        subcategory VARCHAR(50),
        listing_type ENUM('sale', 'rent-to-own', 'hire', 'auction') DEFAULT 'sale',
        condition_type ENUM('new', 'used', 'refurbished') DEFAULT 'used',
        make VARCHAR(50) NOT NULL,
        model VARCHAR(50) NOT NULL,
        variant VARCHAR(100),
        year INT,
        price DECIMAL(12,2) NOT NULL,
        mileage INT DEFAULT 0,
        hours_used INT DEFAULT 0,
        engine_type VARCHAR(50),
        engine_capacity VARCHAR(20),
        horsepower INT,
        transmission VARCHAR(50),
        fuel_type VARCHAR(50),
        color VARCHAR(30),
        vin_number VARCHAR(50),
        registration_number VARCHAR(20),
        region VARCHAR(50) NOT NULL,
        city VARCHAR(50) NOT NULL,
        branch_id INT NULL,
        condition_rating ENUM('excellent', 'very_good', 'good', 'fair', 'poor') DEFAULT 'good',
        no_accidents BOOLEAN DEFAULT FALSE,
        warranty BOOLEAN DEFAULT FALSE,
        warranty_details TEXT,
        finance_available BOOLEAN DEFAULT FALSE,
        trade_in BOOLEAN DEFAULT FALSE,
        service_history BOOLEAN DEFAULT FALSE,
        roadworthy BOOLEAN DEFAULT FALSE,
        description TEXT,
        features TEXT,
        youtube_video_url VARCHAR(255),
        daily_rate DECIMAL(8,2) NULL,
        weekly_rate DECIMAL(10,2) NULL,
        monthly_rate DECIMAL(12,2) NULL,
        auction_start_date DATETIME NULL,
        auction_end_date DATETIME NULL,
        reserve_price DECIMAL(12,2) NULL,
        current_bid DECIMAL(12,2) NULL,
        featured BOOLEAN DEFAULT FALSE,
        featured_until DATE NULL,
        premium_listing BOOLEAN DEFAULT FALSE,
        premium_until DATE NULL,
        status ENUM('available', 'sold', 'reserved', 'draft', 'pending_approval') DEFAULT 'pending_approval',
        views INT DEFAULT 0,
        leads_count INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        approved_at TIMESTAMP NULL,
        FOREIGN KEY (dealer_id) REFERENCES users(user_id) ON DELETE CASCADE,
        FOREIGN KEY (branch_id) REFERENCES dealer_branches(branch_id) ON DELETE SET NULL,
        INDEX idx_category (category),
        INDEX idx_listing_type (listing_type),
        INDEX idx_condition_type (condition_type),
        INDEX idx_status (status),
        INDEX idx_region (region),
        INDEX idx_dealer (dealer_id),
        INDEX idx_featured (featured),
        INDEX idx_premium (premium_listing),
        INDEX idx_created_at (created_at),
        INDEX idx_price (price),
        INDEX idx_year (year),
        FULLTEXT idx_search (make, model, description, features)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // Enhanced vehicle images with ordering
    $pdo->exec("CREATE TABLE IF NOT EXISTS vehicle_images (
        image_id INT AUTO_INCREMENT PRIMARY KEY,
        vehicle_id INT NOT NULL,
        image_path VARCHAR(255) NOT NULL,
        image_name VARCHAR(255),
        file_size INT,
        image_order INT DEFAULT 0,
        is_primary BOOLEAN DEFAULT FALSE,
        uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (vehicle_id) REFERENCES vehicles(vehicle_id) ON DELETE CASCADE,
        INDEX idx_vehicle (vehicle_id),
        INDEX idx_primary (is_primary),
        INDEX idx_order (image_order)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // Enhanced vehicle videos
    $pdo->exec("CREATE TABLE IF NOT EXISTS vehicle_videos (
        video_id INT AUTO_INCREMENT PRIMARY KEY,
        vehicle_id INT NOT NULL,
        video_path VARCHAR(255) NOT NULL,
        video_title VARCHAR(100),
        file_size BIGINT,
        duration_seconds INT,
        uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (vehicle_id) REFERENCES vehicles(vehicle_id) ON DELETE CASCADE,
        INDEX idx_vehicle (vehicle_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // Enhanced vehicle documents
    $pdo->exec("CREATE TABLE IF NOT EXISTS vehicle_documents (
        document_id INT AUTO_INCREMENT PRIMARY KEY,
        vehicle_id INT NOT NULL,
        document_path VARCHAR(255) NOT NULL,
        document_name VARCHAR(100) NOT NULL,
        document_type ENUM('pdf') NOT NULL DEFAULT 'pdf',
        file_size INT,
        uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (vehicle_id) REFERENCES vehicles(vehicle_id) ON DELETE CASCADE,
        INDEX idx_vehicle (vehicle_id),
        INDEX idx_type (document_type)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // Premium backgrounds for adverts
    $pdo->exec("CREATE TABLE IF NOT EXISTS premium_backgrounds (
        bg_id INT AUTO_INCREMENT PRIMARY KEY,
        image_path VARCHAR(255) NOT NULL,
        title VARCHAR(100),
        description TEXT,
        advertiser_name VARCHAR(100),
        advertiser_contact VARCHAR(100),
        advertiser_url VARCHAR(255),
        start_date DATE,
        end_date DATE,
        click_count INT DEFAULT 0,
        status ENUM('active', 'inactive', 'expired') DEFAULT 'active',
        priority INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_status (status),
        INDEX idx_dates (start_date, end_date)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // Auction bids
    $pdo->exec("CREATE TABLE IF NOT EXISTS auction_bids (
        bid_id INT AUTO_INCREMENT PRIMARY KEY,
        vehicle_id INT NOT NULL,
        bidder_name VARCHAR(100) NOT NULL,
        bidder_email VARCHAR(100) NOT NULL,
        bidder_phone VARCHAR(15) NOT NULL,
        bid_amount DECIMAL(12,2) NOT NULL,
        bid_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        status ENUM('active', 'outbid', 'winning', 'withdrawn') DEFAULT 'active',
        FOREIGN KEY (vehicle_id) REFERENCES vehicles(vehicle_id) ON DELETE CASCADE,
        INDEX idx_vehicle (vehicle_id),
        INDEX idx_bid_time (bid_time),
        INDEX idx_amount (bid_amount)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // Hire bookings
    $pdo->exec("CREATE TABLE IF NOT EXISTS hire_bookings (
        booking_id INT AUTO_INCREMENT PRIMARY KEY,
        vehicle_id INT NOT NULL,
        customer_name VARCHAR(100) NOT NULL,
        email VARCHAR(100) NOT NULL,
        phone VARCHAR(15) NOT NULL,
        start_date DATE NOT NULL,
        end_date DATE NOT NULL,
        pickup_location VARCHAR(255),
        daily_rate DECIMAL(8,2),
        total_cost DECIMAL(12,2),
        status ENUM('pending', 'confirmed', 'active', 'completed', 'cancelled') DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (vehicle_id) REFERENCES vehicles(vehicle_id) ON DELETE CASCADE,
        INDEX idx_vehicle (vehicle_id),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // Enhanced inquiries
    $pdo->exec("CREATE TABLE IF NOT EXISTS inquiries (
        inquiry_id INT AUTO_INCREMENT PRIMARY KEY,
        vehicle_id INT NOT NULL,
        dealer_id INT NOT NULL,
        inquiry_type ENUM('general', 'finance', 'trade_in', 'inspection', 'test_drive') DEFAULT 'general',
        full_name VARCHAR(100) NOT NULL,
        email VARCHAR(100) NOT NULL,
        phone VARCHAR(15) NOT NULL,
        message TEXT NOT NULL,
        preferred_contact ENUM('phone', 'email', 'whatsapp') DEFAULT 'phone',
        status ENUM('new', 'contacted', 'in_progress', 'closed') DEFAULT 'new',
        priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
        follow_up_date DATE NULL,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        responded_at TIMESTAMP NULL,
        FOREIGN KEY (vehicle_id) REFERENCES vehicles(vehicle_id) ON DELETE CASCADE,
        FOREIGN KEY (dealer_id) REFERENCES users(user_id) ON DELETE CASCADE,
        INDEX idx_vehicle (vehicle_id),
        INDEX idx_dealer (dealer_id),
        INDEX idx_status (status),
        INDEX idx_type (inquiry_type)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // Check if default admin exists
    $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE user_type = 'admin'");
    $admin_count = $stmt->fetchColumn();
    
    // Update any existing pending_approval vehicles to available
    try {
        $pdo->exec("UPDATE vehicles SET status = 'available' WHERE status = 'pending_approval'");
    } catch (PDOException $e) {
        // Table may not exist yet or column may not have pending_approval
    }
    
    if ($admin_count == 0) {
        // Create default admin user
        $stmt = $pdo->prepare("INSERT INTO users (username, email, password, phone, company_name, user_type, status) VALUES (?, ?, ?, ?, ?, 'admin', 'active')");
        $stmt->execute([
            'admin',
            'a@za',
            password_hash('000000', PASSWORD_DEFAULT),
            '0000000000',
            'TrucksONSale Admin'
        ]);
    }
    
    // Check if default categories exist, if not create them
    $stmt = $pdo->query("SELECT COUNT(*) FROM categories");
    $category_count = $stmt->fetchColumn();
    
    if ($category_count == 0) {
        // Create only sale categories first, then others
        $base_categories = [
            ['trucks', 'Trucks', 'fas fa-truck', 'List Your Truck'],
            ['trailers', 'Trailers', 'fas fa-trailer', 'List Your Trailer'],
            ['buses', 'Buses', 'fas fa-bus', 'List Your Bus'],
            ['commercial_vehicles', 'Commercial Vehicles', 'fas fa-van-shuttle', 'List Your Commercial Vehicle'],
            ['farm_equipment', 'Farm Equipment', 'fas fa-tractor', 'List Your Farm Equipment'],
            ['heavy_machinery', 'Heavy Machinery', 'fas fa-cogs', 'List Your Heavy Machinery'],
            ['animal_farming_equipment', 'Animal Farming Equipment', 'fas fa-cow', 'List Your Animal Farming Equipment']
        ];
        
        $listing_types = ['sale', 'rent-to-own', 'hire', 'auction'];
        $order = 1;
        
        foreach ($listing_types as $listing_type) {
            foreach ($base_categories as $category) {
                $prefix = ($listing_type === 'sale') ? '' : $listing_type . '_';
                $suffix = ($listing_type === 'sale') ? '' : ' (' . ucwords(str_replace('-', ' ', $listing_type)) . ')';
                
                $stmt = $pdo->prepare("INSERT INTO categories (category_key, category_name, listing_type, icon, listing_label, category_order, show_hours) VALUES (?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute([
                    $prefix . $category[0],
                    $category[1] . $suffix,
                    $listing_type,
                    $category[2],
                    str_replace('List Your', ($listing_type === 'sale' ? 'List Your' : ucwords(str_replace('-', ' ', $listing_type)) . ':'), $category[3]),
                    $order,
                    in_array($category[0], ['heavy_machinery', 'farm_equipment']) ? 1 : 0
                ]);
                $order++;
            }
        }
        
        // Insert sample makes and models for ALL categories
        foreach ($base_categories as $index => $category) {
            $category_id = $index + 1; // Get the first category for each type (sale)
            
            if ($category[0] === 'trucks') {
                $sample_makes = [
                    'Mercedes Benz' => ['Actros', 'Atego', 'Axor', 'Antos', 'Arocs'],
                    'Volvo' => ['FH', 'FM', 'FE', 'FL', 'VNL'],
                    'Scania' => ['R Series', 'S Series', 'P Series', 'G Series'],
                    'MAN' => ['TGX', 'TGS', 'TGM', 'TGL'],
                    'DAF' => ['XF', 'CF', 'LF', 'XG'],
                    'Isuzu' => ['NPR', 'NQR', 'FTR', 'FVR', 'GXR'],
                    'Iveco' => ['Stralis', 'Trakker', 'Eurocargo', 'Daily']
                ];
            } elseif ($category[0] === 'trailers') {
                $sample_makes = [
                    'Afrit' => ['Side Tipper', 'Tri-Axle', 'Interlink'],
                    'SA Truck Bodies' => ['Flatdeck', 'Curtain Side', 'Dropside'],
                    'Henred' => ['Fruehauf', 'Goliath', 'Maxitrans']
                ];
            } elseif ($category[0] === 'buses') {
                $sample_makes = [
                    'Mercedes Benz' => ['Sprinter', 'Vario', 'Marco Polo'],
                    'Iveco' => ['Daily Minibus', 'Crossway', 'Evadys'],
                    'Volkswagen' => ['Crafter', 'Amarok']
                ];
            } elseif ($category[0] === 'farm_equipment') {
                $sample_makes = [
                    'John Deere' => ['6R Series', '7R Series', '8R Series'],
                    'Case IH' => ['Magnum', 'Puma', 'Maxxum'],
                    'New Holland' => ['T6', 'T7', 'T8']
                ];
            } elseif ($category[0] === 'heavy_machinery') {
                $sample_makes = [
                    'Caterpillar' => ['320D', '330D', '336D'],
                    'Komatsu' => ['PC200', 'PC300', 'PC400'],
                    'JCB' => ['3CX', '4CX', 'JS130']
                ];
            } else {
                // Default makes for other categories
                $sample_makes = [
                    'Brand A' => ['Model 1', 'Model 2', 'Model 3'],
                    'Brand B' => ['Model X', 'Model Y', 'Model Z']
                ];
            }
            
            foreach ($sample_makes as $make_name => $models) {
                $stmt = $pdo->prepare("INSERT INTO category_makes (category_id, make_name) VALUES (?, ?)");
                $stmt->execute([$category_id, $make_name]);
                $make_id = $pdo->lastInsertId();
                
                foreach ($models as $model_name) {
                    $stmt = $pdo->prepare("INSERT INTO category_models (make_id, model_name) VALUES (?, ?)");
                    $stmt->execute([$make_id, $model_name]);
                    
                    // Add variants for first model of each make
                    if ($model_name === reset($models)) {
                        $model_id = $pdo->lastInsertId();
                        $variants = ['Standard', 'Premium', 'Deluxe'];
                        foreach ($variants as $variant) {
                            $stmt = $pdo->prepare("INSERT INTO category_variants (model_id, variant_name) VALUES (?, ?)");
                            $stmt->execute([$model_id, $variant]);
                        }
                    }
                }
            }
        }
        
        // Add some subcategories
        $stmt = $pdo->prepare("INSERT INTO subcategories (category_id, subcategory_key, subcategory_name) VALUES 
            (1, 'light_duty', 'Light Duty Trucks'),
            (1, 'heavy_duty', 'Heavy Duty Trucks'),
            (1, 'medium_duty', 'Medium Duty Trucks'),
            (2, 'flatbed', 'Flatbed Trailers'),
            (2, 'enclosed', 'Enclosed Trailers'),
            (2, 'refrigerated', 'Refrigerated Trailers'),
            (3, 'passenger', 'Passenger Buses'),
            (3, 'school', 'School Buses'),
            (3, 'charter', 'Charter Buses')");
        $stmt->execute();
    }
    
    // Insert system years if not exist
    $years_check = $pdo->query("SELECT COUNT(*) as count FROM system_years");
    $years_exist = $years_check->fetch()['count'] > 0;
    
    if (!$years_exist) {
        $current_year = date('Y');
        for ($year = 1970; $year <= $current_year + 1; $year++) {
            $pdo->exec("INSERT INTO system_years (year_value) VALUES ($year)");
        }
    }
    
    // Create uploads directory
    if (!file_exists('uploads')) {
        mkdir('uploads', 0755, true);
    }
    
} catch(PDOException $e) {
    die("Database Connection Error: " . $e->getMessage());
}

// Enhanced Helper Functions

// Password validation
function validatePassword($password) {
    return preg_match('/^(?=.*[A-Za-z])(?=.*\d)(?=.*[@$!%*#?&])[A-Za-z\d@$!%*#?&]{8,}$/', $password);
}

// Generate CAPTCHA
function generateCaptcha() {
    $num1 = rand(1, 20);
    $num2 = rand(1, 20);
    $operation = rand(0, 1) ? '+' : '-';
    
    if ($operation === '-' && $num1 < $num2) {
        $temp = $num1;
        $num1 = $num2;
        $num2 = $temp;
    }
    
    $question = "$num1 $operation $num2";
    $answer = $operation === '+' ? $num1 + $num2 : $num1 - $num2;
    
    $_SESSION['captcha_answer'] = $answer;
    return $question;
}

// Handle POST requests
$action = $_POST['action'] ?? $_GET['action'] ?? 'home';
$message = '';
$error = '';

if ($_POST) {
    try {
        switch ($_POST['action'] ?? $action) {
            case 'forgot_password':
                $email = trim($_POST['email']);
                
                if ($email) {
                    $stmt = $pdo->prepare("SELECT user_id FROM users WHERE email = ?");
                    $stmt->execute([$email]);
                    
                    if ($stmt->fetch()) {
                        $reset_token = bin2hex(random_bytes(32));
                        $expires = date('Y-m-d H:i:s', strtotime('+1 hour'));
                        
                        $stmt = $pdo->prepare("UPDATE users SET password_reset_token = ?, password_reset_expires = ? WHERE email = ?");
                        $stmt->execute([$reset_token, $expires, $email]);
                        
                        $message = "Password reset link has been sent to your email address.";
                    } else {
                        $error = "Email address not found.";
                    }
                } else {
                    $error = "Please enter your email address.";
                }
                break;
                
            case 'reset_password':
                $token = $_POST['token'];
                $new_password = $_POST['new_password'];
                $confirm_password = $_POST['confirm_password'];
                
                if ($new_password !== $confirm_password) {
                    $error = "Passwords do not match.";
                } elseif (!validatePassword($new_password)) {
                    $error = "Password must be at least 8 characters long and contain at least one letter, one number, and one special character.";
                } else {
                    $stmt = $pdo->prepare("SELECT user_id FROM users WHERE password_reset_token = ? AND password_reset_expires > NOW()");
                    $stmt->execute([$token]);
                    
                    if ($stmt->fetch()) {
                        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                        $stmt = $pdo->prepare("UPDATE users SET password = ?, password_reset_token = NULL, password_reset_expires = NULL WHERE password_reset_token = ?");
                        $stmt->execute([$hashed_password, $token]);
                        
                        $message = "Password has been reset successfully. You can now login.";
                    } else {
                        $error = "Invalid or expired reset token.";
                    }
                }
                break;
                
            case 'login':
                $stmt = $pdo->prepare("SELECT user_id, username, email, password, user_type, status, failed_login_attempts, locked_until FROM users WHERE email = ?");
                $stmt->execute([$_POST['email']]);
                $user = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($user) {
                    // Check if account is locked
                    if ($user['locked_until'] && strtotime($user['locked_until']) > time()) {
                        $error = "Account is temporarily locked due to too many failed attempts.";
                        break;
                    }
                    
                    if (password_verify($_POST['password'], $user['password'])) {
                        if ($user['user_type'] == 'dealer' && $user['status'] != 'active') {
                            $messages = [
                                'pending' => 'Account pending admin approval',
                                'suspended' => 'Account has been suspended',
                                'rejected' => 'Account has been rejected'
                            ];
                            $error = $messages[$user['status']] ?? 'Account not active';
                        } else {
                            // Reset failed attempts on successful login
                            $pdo->prepare("UPDATE users SET failed_login_attempts = 0, locked_until = NULL, last_login = NOW() WHERE user_id = ?")->execute([$user['user_id']]);
                            
                            $_SESSION['user_id'] = $user['user_id'];
                            $_SESSION['username'] = $user['username'];
                            $_SESSION['user_type'] = $user['user_type'];
                            $_SESSION['status'] = $user['status'];
                            
                            ob_end_clean(); // Clear output buffer before redirect
                            if ($user['user_type'] == 'admin') {
                                header("Location: admin.php");
                            } else {
                                header("Location: dealer.php");
                            }
                            exit;
                        }
                    } else {
                        // Increment failed attempts
                        $failed_attempts = $user['failed_login_attempts'] + 1;
                        $locked_until = null;
                        
                        if ($failed_attempts >= 5) {
                            $locked_until = date('Y-m-d H:i:s', strtotime('+30 minutes'));
                        }
                        
                        $pdo->prepare("UPDATE users SET failed_login_attempts = ?, locked_until = ? WHERE user_id = ?")->execute([$failed_attempts, $locked_until, $user['user_id']]);
                        
                        $error = "Invalid email or password.";
                        if ($failed_attempts >= 5) {
                            $error .= " Account locked for 30 minutes.";
                        }
                    }
                } else {
                    $error = "Invalid email or password.";
                }
                break;

            case 'register':
                $captcha_answer = (int)$_POST['captcha_answer'];
                
                if (!isset($_SESSION['captcha_answer']) || $captcha_answer !== $_SESSION['captcha_answer']) {
                    $error = "Invalid CAPTCHA answer.";
                    break;
                }
                
                if (!validatePassword($_POST['password'])) {
                    $error = "Password must be at least 8 characters long and contain at least one letter, one number, and one special character.";
                    break;
                }
                
                $stmt = $pdo->prepare("SELECT user_id FROM users WHERE email = ? OR username = ?");
                $stmt->execute([$_POST['email'], $_POST['username']]);
                if ($stmt->fetch()) {
                    $error = "Email or username already exists";
                } else {
                    $stmt = $pdo->prepare("INSERT INTO users (username, email, password, phone, company_name, user_type, status) VALUES (?, ?, ?, ?, ?, 'dealer', 'pending')");
                    $stmt->execute([
                        $_POST['username'],
                        $_POST['email'],
                        password_hash($_POST['password'], PASSWORD_DEFAULT),
                        $_POST['phone'],
                        $_POST['company_name']
                    ]);
                    $message = "Registration successful! Waiting for admin approval.";
                }
                unset($_SESSION['captcha_answer']);
                break;
        }
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Handle logout
if ($action == 'logout') {
    session_destroy();
    ob_end_clean();
    header("Location: login.php");
    exit;
}

// Check if user is already logged in
if (isset($_SESSION['user_id'])) {
    if ($_SESSION['user_type'] == 'admin') {
        ob_end_clean();
        header("Location: admin.php");
        exit;
    } else {
        ob_end_clean();
        header("Location: dealer.php");
        exit;
    }
}

// Generate CAPTCHA for forms
$captcha_question = generateCaptcha();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TrucksONSale - Login & Registration</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-navy: #0050B8;
            --primary-navy-light: #e3f2fd;
            --primary-navy-dark: #003d95;
            --primary-red: #DC2626;
            --primary-red-light: #FEE2E2;
            --success-green: #059669;
            --warning-orange: #F97316;
            --dark-gray: #111827;
            --medium-gray: #6B7280;
            --light-gray: #F9FAFB;
            --white: #ffffff;
            --shadow-light: 0 2px 8px rgba(0,0,0,0.08);
            --shadow-medium: 0 4px 16px rgba(0,0,0,0.12);
            --shadow-heavy: 0 8px 32px rgba(0,0,0,0.16);
            --border-radius: 12px;
            --border-radius-sm: 8px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        * {
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: var(--dark-gray);
            line-height: 1.6;
            margin: 0;
            padding: 0;
        }
        
        /* Enhanced Buttons with Navy Theme */
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-navy), var(--primary-navy-dark));
            border: none;
            color: white;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            border-radius: var(--border-radius-sm);
            transition: var(--transition);
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-navy-dark), var(--primary-navy));
            transform: translateY(-1px);
            box-shadow: var(--shadow-medium);
            color: white;
        }
        
        .btn-outline-primary {
            border: 2px solid var(--primary-navy);
            color: var(--primary-navy);
            background: transparent;
            font-weight: 600;
        }
        
        .btn-outline-primary:hover {
            background: var(--primary-navy);
            color: white;
            transform: translateY(-1px);
        }
        
        /* Enhanced Cards */
        .card {
            border: none;
            box-shadow: var(--shadow-light);
            border-radius: var(--border-radius);
            overflow: hidden;
            transition: var(--transition);
            background: var(--white);
            margin-bottom: 1.5rem;
        }
        
        .card:hover {
            box-shadow: var(--shadow-medium);
            transform: translateY(-2px);
        }
        
        .card-header {
            background: linear-gradient(135deg, var(--primary-navy), var(--primary-navy-dark));
            color: white;
            border: none;
            padding: 1.25rem 1.5rem;
            font-weight: 600;
        }
        
        /* Enhanced Forms */
        .form-control, .form-select {
            border: 2px solid #e9ecef;
            border-radius: var(--border-radius-sm);
            padding: 0.75rem 1rem;
            transition: var(--transition);
            font-size: 1rem;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-navy);
            box-shadow: 0 0 0 0.2rem rgba(0, 80, 184, 0.25);
            outline: none;
        }
        
        .form-label {
            font-weight: 600;
            color: var(--dark-gray);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        /* CAPTCHA Styling */
        .captcha-container {
            background: var(--light-gray);
            border: 2px solid #e5e7eb;
            border-radius: var(--border-radius-sm);
            padding: 1rem;
            text-align: center;
            margin-bottom: 1rem;
        }
        
        .captcha-question {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--primary-navy);
            margin-bottom: 0.5rem;
        }
        
        /* Enhanced Alert Messages */
        .alert {
            border: none;
            border-radius: var(--border-radius);
            padding: 1rem 1.5rem;
            margin-bottom: 1.5rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
            border-left: 4px solid var(--success-green);
        }
        
        .alert-danger {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
            border-left: 4px solid var(--primary-red);
        }
        
        .alert-info {
            background: linear-gradient(135deg, var(--primary-navy-light), #bbdefb);
            color: #0c4a6e;
            border-left: 4px solid var(--primary-navy);
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .card-body {
                padding: 1rem;
            }
            
            .btn {
                padding: 0.5rem 1rem;
                font-size: 0.875rem;
            }
        }
        
        /* Animation Classes */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .slide-in-left {
            animation: slideInLeft 0.5s ease-out;
        }
        
        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-30px); }
            to { opacity: 1; transform: translateX(0); }
        }
    </style>
</head>
<body>

<div class="container mt-4">
    <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show fade-in">
            <i class="fas fa-check-circle"></i>
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show fade-in">
            <i class="fas fa-exclamation-circle"></i>
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php
    switch ($action) {
        case 'home':
        case 'login':
        default:
            ?>
            <div class="login-container" style="min-height: 100vh; display: flex; align-items: center; justify-content: center;">
                <div class="row justify-content-center w-100">
                    <div class="col-md-6 col-lg-4">
                        <div class="text-center mb-4 fade-in">
                            <div style="font-size: 2.5rem; font-weight: 700; color: var(--primary-navy); margin-bottom: 0.5rem;">
                                <i class="fas fa-truck"></i> TrucksONSale
                            </div>
                            <p class="text-muted">Professional Complete Trading Platform</p>
                        </div>
                        <div class="card slide-in-left">
                            <div class="card-header text-center">
                                <h4><i class="fas fa-sign-in-alt me-2"></i>Dealer Login</h4>
                            </div>
                            <div class="card-body p-4">
                                <form method="POST" action="login.php">
                                    <input type="hidden" name="action" value="login">
                                    <div class="mb-3">
                                        <label class="form-label"><i class="fas fa-envelope"></i>Email</label>
                                        <input type="email" class="form-control" name="email" required placeholder="Enter your email">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label"><i class="fas fa-lock"></i>Password</label>
                                        <input type="password" class="form-control" name="password" required placeholder="Enter your password">
                                    </div>
                                    <button type="submit" class="btn btn-primary w-100 mb-3">
                                        <i class="fas fa-sign-in-alt"></i>Login
                                    </button>
                                    <div class="text-center mb-3">
                                        <a href="?action=forgot_password" class="text-decoration-none">
                                            <i class="fas fa-key me-1"></i>Forgot Password?
                                        </a>
                                    </div>
                                    <div class="text-center">
                                        <a href="?action=register" class="btn btn-outline-primary">
                                            <i class="fas fa-user-plus"></i>Register as Dealer
                                        </a>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php
            break;

        case 'forgot_password':
            ?>
            <div class="login-container" style="min-height: 100vh; display: flex; align-items: center; justify-content: center;">
                <div class="row justify-content-center w-100">
                    <div class="col-md-6 col-lg-4">
                        <div class="card fade-in">
                            <div class="card-header text-center">
                                <h4><i class="fas fa-key me-2"></i>Reset Password</h4>
                            </div>
                            <div class="card-body p-4">
                                <form method="POST" action="login.php">
                                    <input type="hidden" name="action" value="forgot_password">
                                    <div class="mb-3">
                                        <label class="form-label"><i class="fas fa-envelope"></i>Email Address</label>
                                        <input type="email" class="form-control" name="email" required placeholder="Enter your email address">
                                        <small class="text-muted">We'll send you a reset link if this email is registered.</small>
                                    </div>
                                    <button type="submit" class="btn btn-primary w-100 mb-3">
                                        <i class="fas fa-paper-plane"></i>Send Reset Link
                                    </button>
                                    <div class="text-center">
                                        <a href="login.php" class="btn btn-outline-primary">
                                            <i class="fas fa-arrow-left"></i>Back to Login
                                        </a>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php
            break;

        case 'register':
            ?>
            <div class="login-container" style="min-height: 100vh; display: flex; align-items: center; justify-content: center; padding: 2rem 0;">
                <div class="row justify-content-center w-100">
                    <div class="col-md-8 col-lg-6">
                        <div class="card fade-in">
                            <div class="card-header text-center">
                                <h4><i class="fas fa-user-plus me-2"></i>Dealer Registration</h4>
                            </div>
                            <div class="card-body p-4">
                                <form method="POST" action="login.php">
                                    <input type="hidden" name="action" value="register">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label"><i class="fas fa-user"></i>Username</label>
                                                <input type="text" class="form-control" name="username" required placeholder="Choose a username">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label"><i class="fas fa-envelope"></i>Email</label>
                                                <input type="email" class="form-control" name="email" required placeholder="Enter your email">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label"><i class="fas fa-lock"></i>Password</label>
                                                <input type="password" class="form-control" name="password" required placeholder="Enter password">
                                                <small class="text-muted">Must be 8+ characters with letter, number & special character</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label"><i class="fas fa-phone"></i>Phone</label>
                                                <input type="tel" class="form-control" name="phone" required placeholder="Enter phone number">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label"><i class="fas fa-building"></i>Company Name</label>
                                        <input type="text" class="form-control" name="company_name" required placeholder="Enter company name">
                                    </div>
                                    
                                    <!-- CAPTCHA -->
                                    <div class="mb-3">
                                        <div class="captcha-container">
                                            <div class="captcha-question">What is <?= $captcha_question ?>?</div>
                                            <div style="font-size: 0.9rem; color: var(--medium-gray);">Please solve to continue</div>
                                        </div>
                                        <input type="number" class="form-control" name="captcha_answer" required placeholder="Enter answer">
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary w-100 mb-3">
                                        <i class="fas fa-user-plus"></i>Register Account
                                    </button>
                                    <div class="text-center">
                                        <a href="login.php" class="btn btn-outline-primary">
                                            <i class="fas fa-arrow-left"></i>Back to Login
                                        </a>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php
            break;
    }
    ?>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<script>
// Add visual feedback and animations
document.addEventListener('DOMContentLoaded', function() {
    // Animate elements on page load
    const fadeElements = document.querySelectorAll('.fade-in');
    fadeElements.forEach((element, index) => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            element.style.transition = 'all 0.6s ease';
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }, index * 100);
    });

    const slideElements = document.querySelectorAll('.slide-in-left');
    slideElements.forEach((element, index) => {
        element.style.opacity = '0';
        element.style.transform = 'translateX(-30px)';
        
        setTimeout(() => {
            element.style.transition = 'all 0.6s ease';
            element.style.opacity = '1';
            element.style.transform = 'translateX(0)';
        }, index * 150);
    });
    
    console.log('🚛 TrucksONSale Login System Ready!');
});
</script>

</body>
</html>

<?php
// Clean output buffer and flush
ob_end_flush();
?>