<?php
// =============================================
// TRUCKSONSALE MARKETPLACE - COMPLETE DATABASE INTEGRATION
// INTEGRATED WITH LIVE DATABASE STRUCTURE + PREMIUM ADS
// =============================================

session_start();

// Database connection using your live database credentials
try {
    $db = new mysqli('localhost', 'root', '', 'truc_tos');
    if ($db->connect_error) {
        throw new Exception("Database connection failed: " . $db->connect_error);
    }
    $db->set_charset("utf8mb4");
} catch (Exception $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Enhanced Categories Data from Database
function getCategoriesData($db) {
    try {
        $stmt = $db->query("SELECT * FROM categories WHERE status = 'active' ORDER BY category_order, category_name");
        $categories = $stmt->fetch_all(MYSQLI_ASSOC);
        
        $categories_data = [];
        
        foreach ($categories as $category) {
            $cat_key = strtolower($category['category_key']);
            
            // Get subcategories
            $stmt = $db->prepare("
                SELECT LOWER(subcategory_key) as sub_key, subcategory_name 
                FROM subcategories 
                WHERE category_id = ? AND status = 'active' 
                ORDER BY subcategory_order, subcategory_name
            ");
            $stmt->bind_param("i", $category['category_id']);
            $stmt->execute();
            $result = $stmt->get_result();
            $subcategories = [];
            while ($row = $result->fetch_assoc()) {
                $subcategories[$row['sub_key']] = $row['subcategory_name'];
            }
            
            // Get makes
            $stmt = $db->prepare("
                SELECT make_name 
                FROM category_makes 
                WHERE category_id = ? AND status = 'active' 
                ORDER BY make_name
            ");
            $stmt->bind_param("i", $category['category_id']);
            $stmt->execute();
            $result = $stmt->get_result();
            $makes = [];
            while ($row = $result->fetch_assoc()) {
                $makes[] = $row['make_name'];
            }
            
            $categories_data[$cat_key] = [
                'id' => $category['category_id'],
                'name' => $category['category_name'],
                'icon' => $category['icon'],
                'listing_label' => $category['listing_label'],
                'listing_type' => $category['listing_type'],
                'makes' => $makes,
                'subcategories' => $subcategories
            ];
        }
        
        return $categories_data;
        
    } catch (Exception $e) {
        return [];
    }
}

// Premium Ads Functions - CORRECTED
function getPremiumBannerAds($db) {
    try {
        $stmt = $db->query("
            SELECT * FROM premium_ads 
            WHERE ad_type = 'banner' AND status = 'active'
            AND (start_date IS NULL OR start_date <= CURDATE())
            AND (end_date IS NULL OR end_date >= CURDATE())
            ORDER BY display_order, created_at DESC
        ");
        return $stmt->fetch_all(MYSQLI_ASSOC);
    } catch (Exception $e) {
        return [];
    }
}

function getPremiumBoxAds($db) {
    try {
        $stmt = $db->query("
            SELECT * FROM premium_ads 
            WHERE ad_type = 'box' AND status = 'active'
            AND (start_date IS NULL OR start_date <= CURDATE())
            AND (end_date IS NULL OR end_date >= CURDATE())
            ORDER BY display_order, created_at DESC
        ");
        return $stmt->fetch_all(MYSQLI_ASSOC);
    } catch (Exception $e) {
        return [];
    }
}

function getDealerFeaturedLimit($dealer_id, $db) {
    try {
        $stmt = $db->prepare("SELECT featured_limit FROM users WHERE user_id = ? AND user_type = 'dealer'");
        $stmt->bind_param("i", $dealer_id);
        $stmt->execute();
        $result = $stmt->get_result();
        if ($row = $result->fetch_assoc()) {
            return $row['featured_limit'] ?? 2; // Default to 2
        }
        return 2;
    } catch (Exception $e) {
        return 2;
    }
}

// Enhanced Helper Functions
function get_vehicle_count($category = null, $subcategory = null, $region = null, $listing_type = null) {
    global $db;
    
    $query = "SELECT COUNT(*) FROM vehicles v JOIN users u ON v.dealer_id = u.user_id WHERE v.status = 'available' AND u.status = 'active' AND v.category != 'premium_ads'";
    
    if ($category) {
        $query .= " AND v.category = '" . $db->real_escape_string($category) . "'";
    }
    if ($subcategory) {
        $query .= " AND v.subcategory = '" . $db->real_escape_string($subcategory) . "'";
    }
    if ($region) {
        $query .= " AND v.region = '" . $db->real_escape_string($region) . "'";
    }
    if ($listing_type) {
        $query .= " AND v.listing_type = '" . $db->real_escape_string($listing_type) . "'";
    }
    
    $result = $db->query($query);
    return $result ? $result->fetch_row()[0] : 0;
}

function get_active_auctions_count() {
    global $db;
    $query = "SELECT COUNT(*) FROM vehicles WHERE listing_type = 'auction' AND status = 'available' AND auction_end_date > NOW() AND category != 'premium_ads'";
    $result = $db->query($query);
    return $result ? $result->fetch_row()[0] : 0;
}

function get_hire_vehicles_count() {
    global $db;
    $query = "SELECT COUNT(*) FROM vehicles WHERE listing_type = 'hire' AND status = 'available' AND category != 'premium_ads'";
    $result = $db->query($query);
    return $result ? $result->fetch_row()[0] : 0;
}

// Handle form submissions
if ($_POST) {
    if (isset($_POST['submit_inquiry'])) {
        $vehicle_id = (int)$_POST['vehicle_id'];
        $inquiry_type = $_POST['inquiry_type'] ?? 'general';
        $full_name = trim($_POST['full_name']);
        $email = trim($_POST['email']);
        $phone = trim($_POST['phone']);
        $message = trim($_POST['message']);
        $preferred_contact = $_POST['preferred_contact'] ?? 'phone';
        
        if ($vehicle_id && $full_name && $email && $phone && $message) {
            // Get dealer_id from vehicle
            $vehicle_stmt = $db->prepare("SELECT dealer_id FROM vehicles WHERE vehicle_id = ?");
            $vehicle_stmt->bind_param("i", $vehicle_id);
            $vehicle_stmt->execute();
            $vehicle_result = $vehicle_stmt->get_result();
            
            if ($vehicle_result->num_rows > 0) {
                $vehicle_data = $vehicle_result->fetch_assoc();
                $dealer_id = $vehicle_data['dealer_id'];
                
                $stmt = $db->prepare("INSERT INTO inquiries (vehicle_id, dealer_id, inquiry_type, full_name, email, phone, message, preferred_contact) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
                $stmt->bind_param("iissssss", $vehicle_id, $dealer_id, $inquiry_type, $full_name, $email, $phone, $message, $preferred_contact);
                
                if ($stmt->execute()) {
                    $inquiry_success = true;
                } else {
                    $inquiry_error = "Failed to submit inquiry. Please try again.";
                }
            } else {
                $inquiry_error = "Vehicle not found.";
            }
        } else {
            $inquiry_error = "Please fill in all required fields.";
        }
    }
    
    if (isset($_POST['submit_bid'])) {
        $vehicle_id = (int)$_POST['vehicle_id'];
        $bidder_name = trim($_POST['bidder_name']);
        $bidder_email = trim($_POST['bidder_email']);
        $bidder_phone = trim($_POST['bidder_phone']);
        $bid_amount = (float)$_POST['bid_amount'];
        
        if ($vehicle_id && $bidder_name && $bidder_email && $bidder_phone && $bid_amount > 0) {
            // Check if auction is still active
            $auction_stmt = $db->prepare("SELECT current_bid, auction_end_date FROM vehicles WHERE vehicle_id = ? AND listing_type = 'auction' AND status = 'available' AND auction_end_date > NOW()");
            $auction_stmt->bind_param("i", $vehicle_id);
            $auction_stmt->execute();
            $auction_result = $auction_stmt->get_result();
            
            if ($auction_result->num_rows > 0) {
                $auction_data = $auction_result->fetch_assoc();
                $current_bid = $auction_data['current_bid'] ?? 0;
                
                if ($bid_amount > $current_bid) {
                    // Update vehicle with new current bid
                    $update_stmt = $db->prepare("UPDATE vehicles SET current_bid = ? WHERE vehicle_id = ?");
                    $update_stmt->bind_param("di", $bid_amount, $vehicle_id);
                    
                    // Insert bid record
                    $bid_stmt = $db->prepare("INSERT INTO auction_bids (vehicle_id, bidder_name, bidder_email, bidder_phone, bid_amount, status) VALUES (?, ?, ?, ?, ?, 'active')");
                    $bid_stmt->bind_param("isssd", $vehicle_id, $bidder_name, $bidder_email, $bidder_phone, $bid_amount);
                    
                    // Mark previous bids as outbid
                    $outbid_stmt = $db->prepare("UPDATE auction_bids SET status = 'outbid' WHERE vehicle_id = ? AND bid_amount < ?");
                    $outbid_stmt->bind_param("id", $vehicle_id, $bid_amount);
                    
                    if ($update_stmt->execute() && $bid_stmt->execute() && $outbid_stmt->execute()) {
                        $bid_success = true;
                    } else {
                        $bid_error = "Failed to place bid. Please try again.";
                    }
                } else {
                    $bid_error = "Bid must be higher than current bid of R " . number_format($current_bid);
                }
            } else {
                $bid_error = "Auction not found or has ended.";
            }
        } else {
            $bid_error = "Please fill in all required fields with valid data.";
        }
    }
    
    if (isset($_POST['submit_hire'])) {
        $vehicle_id = (int)$_POST['vehicle_id'];
        $customer_name = trim($_POST['customer_name']);
        $email = trim($_POST['email']);
        $phone = trim($_POST['phone']);
        $start_date = $_POST['start_date'];
        $end_date = $_POST['end_date'];
        $pickup_location = trim($_POST['pickup_location']);
        
        if ($vehicle_id && $customer_name && $email && $phone && $start_date && $end_date) {
            // Get vehicle daily rate
            $vehicle_stmt = $db->prepare("SELECT daily_rate FROM vehicles WHERE vehicle_id = ? AND listing_type = 'hire' AND status = 'available'");
            $vehicle_stmt->bind_param("i", $vehicle_id);
            $vehicle_stmt->execute();
            $vehicle_result = $vehicle_stmt->get_result();
            
            if ($vehicle_result->num_rows > 0) {
                $vehicle_data = $vehicle_result->fetch_assoc();
                $daily_rate = $vehicle_data['daily_rate'] ?? 0;
                
                // Calculate total cost
                $start = new DateTime($start_date);
                $end = new DateTime($end_date);
                $days = $start->diff($end)->days + 1;
                $total_cost = $days * $daily_rate;
                
                $stmt = $db->prepare("INSERT INTO hire_bookings (vehicle_id, customer_name, email, phone, start_date, end_date, pickup_location, daily_rate, total_cost, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending')");
                $stmt->bind_param("isssssdd", $vehicle_id, $customer_name, $email, $phone, $start_date, $end_date, $pickup_location, $daily_rate, $total_cost);
                
                if ($stmt->execute()) {
                    $hire_success = true;
                } else {
                    $hire_error = "Failed to submit hire booking.";
                }
            } else {
                $hire_error = "Vehicle not available for hire.";
            }
        } else {
            $hire_error = "Please fill in all required fields.";
        }
    }
}

// Handle AJAX requests
if (isset($_GET['ajax'])) {
    header('Content-Type: application/json');
    
    if ($_GET['ajax'] === 'subcategories') {
        $category = $_GET['category'] ?? '';
        $subcats = [];
        if ($category && isset($categories_data[strtolower($category)])) {
            $subcats = $categories_data[strtolower($category)]['subcategories'];
        }
        echo json_encode($subcats);
        exit;
    }
    
    if ($_GET['ajax'] === 'makes') {
        $category = $_GET['category'] ?? '';
        $makes = [];
        if ($category && isset($categories_data[strtolower($category)])) {
            $makes = $categories_data[strtolower($category)]['makes'];
        }
        echo json_encode($makes);
        exit;
    }
    
    if ($_GET['ajax'] === 'banner_ads') {
        $banner_ads = getPremiumBannerAds($db);
        echo json_encode($banner_ads);
        exit;
    }
    
    if ($_GET['ajax'] === 'box_ads') {
        $box_ads = getPremiumBoxAds($db);
        echo json_encode($box_ads);
        exit;
    }
    
    if ($_GET['ajax'] === 'vehicles') {
        $category = $_GET['category'] ?? '';
        $subcategory = $_GET['subcategory'] ?? '';
        $region = $_GET['region'] ?? '';
        $query = $_GET['query'] ?? '';
        $make = $_GET['make'] ?? '';
        $min_price = $_GET['min_price'] ?? '';
        $max_price = $_GET['max_price'] ?? '';
        $min_year = $_GET['min_year'] ?? '';
        $max_year = $_GET['max_year'] ?? '';
        $type = $_GET['type'] ?? 'all';
        $listing_type = $_GET['listing_type'] ?? '';
        
        $sql = "SELECT v.*, u.company_name, u.username, u.phone,
                (SELECT image_path FROM vehicle_images WHERE vehicle_id = v.vehicle_id AND is_primary = 1 LIMIT 1) as primary_image,
                (SELECT COUNT(*) FROM vehicle_images WHERE vehicle_id = v.vehicle_id) as image_count
                FROM vehicles v
                JOIN users u ON v.dealer_id = u.user_id
                WHERE v.status = 'available' AND u.status = 'active' AND v.category != 'premium_ads'";
        
        if ($category) $sql .= " AND v.category = '" . $db->real_escape_string($category) . "'";
        if ($subcategory) $sql .= " AND v.subcategory = '" . $db->real_escape_string($subcategory) . "'";
        if ($region) $sql .= " AND v.region = '" . $db->real_escape_string($region) . "'";
        if ($make) $sql .= " AND v.make = '" . $db->real_escape_string($make) . "'";
        if ($listing_type) $sql .= " AND v.listing_type = '" . $db->real_escape_string($listing_type) . "'";
        if ($min_price) $sql .= " AND v.price >= " . (int)$min_price;
        if ($max_price) $sql .= " AND v.price <= " . (int)$max_price;
        if ($min_year) $sql .= " AND v.year >= " . (int)$min_year;
        if ($max_year) $sql .= " AND v.year <= " . (int)$max_year;
        if ($query) {
            $query = $db->real_escape_string($query);
            $sql .= " AND (v.make LIKE '%$query%' OR v.model LIKE '%$query%' OR v.description LIKE '%$query%')";
        }
        
        if ($type === 'featured') {
            $sql .= " AND v.featured = 1 AND (v.featured_until IS NULL OR v.featured_until >= CURDATE()) ORDER BY RAND() LIMIT 12";
        } elseif ($type === 'recent') {
            $sql .= " ORDER BY v.created_at DESC LIMIT 12";
        } elseif ($type === 'auctions') {
            $sql .= " AND v.listing_type = 'auction' AND v.auction_end_date > NOW() ORDER BY v.auction_end_date ASC LIMIT 20";
        } elseif ($type === 'hire') {
            $sql .= " AND v.listing_type = 'hire' ORDER BY v.daily_rate ASC LIMIT 20";
        } else {
            $sql .= " ORDER BY v.featured DESC, v.created_at DESC LIMIT 50";
        }
        
        $result = $db->query($sql);
        $vehicles = [];
        while ($row = $result->fetch_assoc()) {
            $vehicles[] = $row;
        }
        
        echo json_encode([
            'vehicles' => $vehicles,
            'count' => count($vehicles)
        ]);
        exit;
    }
    
    if ($_GET['ajax'] === 'auctions') {
        $sql = "SELECT v.*, u.company_name, u.username,
                (SELECT image_path FROM vehicle_images WHERE vehicle_id = v.vehicle_id AND is_primary = 1 LIMIT 1) as primary_image,
                (SELECT COUNT(*) FROM auction_bids WHERE vehicle_id = v.vehicle_id) as bid_count
                FROM vehicles v
                JOIN users u ON v.dealer_id = u.user_id
                WHERE v.listing_type = 'auction' AND v.status = 'available' AND v.auction_end_date > NOW() AND v.category != 'premium_ads'
                ORDER BY v.auction_end_date ASC LIMIT 20";
        
        $result = $db->query($sql);
        $auctions = [];
        while ($row = $result->fetch_assoc()) {
            $auctions[] = $row;
        }
        
        echo json_encode([
            'auctions' => $auctions,
            'count' => count($auctions)
        ]);
        exit;
    }
}

// Get page parameters
$page = $_GET['page'] ?? 'home';
$vehicle_id = $_GET['vehicle_id'] ?? null;
$search_category = $_GET['category'] ?? null;
$search_subcategory = $_GET['subcategory'] ?? null;
$search_region = $_GET['region'] ?? null;
$search_query = $_GET['query'] ?? null;

// Get enhanced vehicle details if viewing specific vehicle
$vehicle_details = null;
$vehicle_images = null;
$vehicle_videos = null;
$vehicle_documents = null;

if ($vehicle_id) {
    // Update views count
    $db->query("UPDATE vehicles SET views = views + 1 WHERE vehicle_id = " . (int)$vehicle_id);
    
    $stmt = $db->prepare("
        SELECT v.*, u.username, u.company_name, u.phone, u.email,
               c.category_name, c.icon as category_icon, c.listing_label,
               s.subcategory_name
        FROM vehicles v
        JOIN users u ON v.dealer_id = u.user_id
        LEFT JOIN categories c ON v.category = c.category_key
        LEFT JOIN subcategories s ON (s.category_id = c.category_id AND v.subcategory = s.subcategory_key)
        WHERE v.vehicle_id = ? AND v.status = 'available' AND u.status = 'active'
    ");
    $stmt->bind_param("i", $vehicle_id);
    $stmt->execute();
    $vehicle_details = $stmt->get_result()->fetch_assoc();
    
    if ($vehicle_details) {
        $vehicle_images = $db->query("SELECT * FROM vehicle_images WHERE vehicle_id = $vehicle_id ORDER BY is_primary DESC, uploaded_at ASC");
        $vehicle_videos = $db->query("SELECT * FROM vehicle_videos WHERE vehicle_id = $vehicle_id ORDER BY uploaded_at ASC");
        $vehicle_documents = $db->query("SELECT * FROM vehicle_documents WHERE vehicle_id = $vehicle_id ORDER BY uploaded_at ASC");
    }
}

// Get enhanced categories data
$categories_data = getCategoriesData($db);

// Get premium ads
$banner_ads = getPremiumBannerAds($db);
$box_ads = getPremiumBoxAds($db);

// Get total vehicle count
$total_vehicles = get_vehicle_count();
$auction_count = get_active_auctions_count();
$hire_count = get_hire_vehicles_count();

// Get categories with counts
$category_counts = [];
foreach ($categories_data as $key => $data) {
    $category_counts[$key] = get_vehicle_count($key);
}

// Get random background for hero section
function getRandomBackground($db) {
    $result = $db->query("SELECT image_path FROM premium_backgrounds WHERE status = 'active' ORDER BY RAND() LIMIT 1");
    if ($result && $result->num_rows > 0) {
        return $result->fetch_assoc()['image_path'];
    }
    return 'https://images.unsplash.com/photo-1601584115197-04ecc0da31d7?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80';
}

$hero_background = getRandomBackground($db);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TrucksONSale - South Africa's Premier Vehicle Marketplace</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary: #dc2626;
            --primary-light: #fee2e2;
            --primary-dark: #b91c1c;
            --secondary: #1e40af;
            --accent: #f97316;
            --dark: #111827;
            --light: #f8fafc;
            --gray: #6b7280;
            --success: #059669;
            --border-radius: 8px;
            --shadow: 0 1px 3px rgba(0,0,0,0.1);
            --shadow-lg: 0 10px 25px rgba(0,0,0,0.15);
            --transition: all 0.2s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.5;
            color: var(--dark);
            background: #fff;
            font-size: 14px; /* Normal size */
        }

        /* Preloader */
        #preloader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.3s ease;
        }

        .preloader-content {
            text-align: center;
        }

        .preloader-logo {
            font-size: 2rem;
            font-weight: 700;
            color: var(--dark);
            margin-bottom: 1rem;
        }

        .preloader-logo .text-danger {
            color: var(--primary) !important;
        }

        .preloader-spinner {
            width: 32px;
            height: 32px;
            border: 3px solid #f3f4f6;
            border-top: 3px solid var(--primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Navigation */
        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            padding: 0.5rem 0;
            font-size: 14px;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--dark) !important;
        }

        .navbar-brand .text-danger {
            color: var(--primary) !important;
        }

        .nav-link {
            font-weight: 500;
            color: var(--dark) !important;
            padding: 0.5rem 1rem !important;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-size: 14px;
        }

        .nav-link:hover {
            background: var(--primary-light);
            color: var(--primary) !important;
        }

        /* Premium Banner Ad Section - DRAMATICALLY INCREASED SIZE */
        .banner-ad-section {
            margin-top: 70px;
            height: 200px; /* MUCH LARGER - was 120px */
            background: var(--light);
            border-bottom: 1px solid #e5e7eb;
            overflow: hidden;
            position: relative;
            z-index: 5;
        }

        /* No gap between banner and content on non-home pages */
        .banner-ad-section.no-gap {
            border-bottom: none;
            margin-bottom: 0;
        }

        .banner-ad-container {
            height: 100%;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .banner-ad {
            width: 100%;
            height: 100%;
            display: none;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 1;
        }

        .banner-ad.active {
            display: flex !important;
            opacity: 1;
        }

        .banner-ad img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
        }

        .banner-ad > div {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .banner-ad-placeholder {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            width: 100%;
            background: linear-gradient(45deg, var(--primary), var(--primary-light));
            color: white;
            font-weight: 600;
            font-size: 18px; /* LARGER TEXT */
            text-align: center;
            padding: 1rem;
            border: 2px solid var(--primary);
        }

        /* Hero Section */
        .hero-section {
            background: linear-gradient(rgba(17, 24, 39, 0.6), rgba(17, 24, 39, 0.4)), 
                        url('<?= $hero_background ?>');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            padding: 3rem 0 2rem;
            min-height: 50vh;
            display: flex;
            align-items: center;
        }

        .hero-title {
            font-size: 2.2rem;
            font-weight: 700;
            line-height: 1.2;
            margin-bottom: 1rem;
            color: white;
        }

        .hero-subtitle {
            font-size: 1.1rem;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 1.5rem;
            font-weight: 400;
        }

        .hero-buttons {
            margin-bottom: 1.5rem;
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .hero-btn {
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            border-radius: var(--border-radius);
            transition: var(--transition);
            text-decoration: none;
            font-size: 14px;
        }

        .hero-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(130px, 1fr));
            gap: 1.5rem;
            max-width: 650px;
        }

        .hero-stat {
            text-align: center;
            color: white;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: var(--border-radius);
            backdrop-filter: blur(10px);
        }

        .hero-stat-number {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary);
            display: block;
            margin-bottom: 0.5rem;
        }

        .hero-stat-label {
            font-size: 12px;
            opacity: 0.9;
            font-weight: 500;
        }

        /* Search Section */
        .search-section {
            margin-top: -1.5rem;
            position: relative;
            z-index: 10;
            padding: 0 0 2rem;
        }

        .search-card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            padding: 1.5rem;
            margin: 0 1rem;
        }

        .search-tabs {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1.5rem;
            border-bottom: 2px solid #f3f4f6;
        }

        .search-tab {
            padding: 0.75rem 1rem;
            background: none;
            border: none;
            font-weight: 600;
            color: var(--gray);
            cursor: pointer;
            transition: var(--transition);
            border-radius: var(--border-radius) var(--border-radius) 0 0;
            font-size: 13px;
        }

        .search-tab.active {
            color: var(--primary);
            background: var(--primary-light);
        }

        .search-content {
            display: none;
        }

        .search-content.active {
            display: block;
        }

        .form-control, .form-select {
            border: 1px solid #e5e7eb;
            border-radius: var(--border-radius);
            padding: 0.6rem 0.9rem;
            font-weight: 400;
            transition: var(--transition);
            font-size: 13px;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
        }

        .form-label {
            font-weight: 600;
            color: var(--dark);
            margin-bottom: 0.4rem;
            font-size: 12px;
        }

        .btn-search {
            background: var(--primary);
            border: none;
            padding: 0.6rem 1.2rem;
            font-weight: 600;
            border-radius: var(--border-radius);
            transition: var(--transition);
            color: white;
            font-size: 13px;
        }

        .btn-search:hover {
            background: var(--primary-dark);
            color: white;
        }

        /* Vehicle Sections */
        .vehicles-section {
            padding: 3rem 0;
            background: var(--light);
        }

        .section-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .section-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.75rem;
            color: var(--dark);
        }

        .section-subtitle {
            font-size: 1rem;
            color: var(--gray);
            font-weight: 400;
        }

        .section-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .section-label {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--dark);
        }

        .scroll-controls {
            display: flex;
            gap: 0.5rem;
        }

        .scroll-btn {
            width: 36px;
            height: 36px;
            border: 1px solid var(--primary);
            background: white;
            color: var(--primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }

        .scroll-btn:hover {
            background: var(--primary);
            color: white;
        }

        /* Enhanced Vehicle Cards */
        .vehicle-card {
            background: white;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow);
            transition: var(--transition);
            height: 100%;
            cursor: pointer;
            margin-bottom: 1.5rem;
        }

        .vehicle-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .vehicle-image {
            position: relative;
            overflow: hidden;
            height: 180px;
        }

        .vehicle-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .vehicle-card:hover .vehicle-image img {
            transform: scale(1.03);
        }

        .vehicle-badge {
            position: absolute;
            top: 0.75rem;
            right: 0.75rem;
            background: var(--primary);
            color: white;
            padding: 0.3rem 0.7rem;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 600;
        }

        .auction-badge {
            background: #f59e0b !important;
        }

        .hire-badge {
            background: #10b981 !important;
        }

        .rent-to-own-badge {
            background: #8b5cf6 !important;
        }

        .vehicle-price-overlay {
            position: absolute;
            top: 0.75rem;
            left: 0.75rem;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 0.5rem 0.8rem;
            border-radius: var(--border-radius);
            font-weight: 700;
            font-size: 1rem;
        }

        .vehicle-image-count {
            position: absolute;
            bottom: 0.75rem;
            right: 0.75rem;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 0.3rem 0.6rem;
            border-radius: var(--border-radius);
            font-size: 12px;
        }

        .vehicle-content {
            padding: 1rem;
        }

        .vehicle-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--dark);
        }

        .vehicle-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 0.75rem;
            margin-bottom: 1rem;
        }

        .vehicle-detail {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 12px;
            color: var(--gray);
            background: #f8fafc;
            padding: 0.5rem;
            border-radius: var(--border-radius);
            font-weight: 500;
        }

        .vehicle-detail i {
            color: var(--primary);
        }

        .vehicle-features {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }

        .vehicle-feature {
            background: var(--primary-light);
            color: var(--primary);
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 600;
        }

        .vehicle-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 1rem;
            border-top: 1px solid #f3f4f6;
        }

        .dealer-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .dealer-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--primary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 12px;
        }

        .dealer-name {
            font-weight: 600;
            color: var(--dark);
            font-size: 13px;
        }

        .dealer-verified {
            font-size: 11px;
            color: var(--success);
            display: flex;
            align-items: center;
            gap: 0.3rem;
        }

        .btn-view-details {
            background: var(--primary);
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius);
            font-weight: 600;
            font-size: 12px;
            transition: var(--transition);
            text-decoration: none;
        }

        /* NEW VEHICLE CARD STYLES - EXACT FORMAT REQUESTED */
        .featured-badge {
            background: var(--primary);
            color: white;
            padding: 0.4rem 0.8rem;
            border-radius: var(--border-radius);
            font-size: 11px;
            font-weight: 700;
            text-transform: uppercase;
            margin-bottom: 0.75rem;
            display: inline-block;
        }

        .dealer-name-display {
            font-weight: 600;
            color: var(--dark);
            font-size: 14px;
            margin-bottom: 1rem;
            text-align: center;
        }

        .btn-view-details-full {
            background: var(--primary);
            border: none;
            color: white;
            padding: 0.6rem 1.5rem;
            border-radius: var(--border-radius);
            font-weight: 600;
            font-size: 13px;
            transition: var(--transition);
            text-decoration: none;
            display: block;
            text-align: center;
            width: 100%;
        }

        .btn-view-details-full:hover {
            background: var(--primary-dark);
            color: white;
            text-decoration: none;
        }

        /* Horizontal Scrolling - EXACTLY 4 CARDS VISIBLE ON PC */
        .horizontal-scroll {
            display: flex;
            gap: 1.5rem;
            overflow: hidden;
            padding: 1rem 0;
            position: relative;
        }

        .slide-container {
            display: flex;
            gap: 1.5rem;
            transition: transform 0.5s ease;
        }

        /* EXACTLY 4 CARDS ON PC - FIXED WIDTH */
        @media (min-width: 992px) {
            .horizontal-scroll .vehicle-card {
                min-width: calc(25% - 1.125rem); /* EXACTLY 25% for 4 cards */
                max-width: calc(25% - 1.125rem);
                flex: 0 0 calc(25% - 1.125rem);
            }
        }

        @media (min-width: 768px) and (max-width: 991px) {
            .horizontal-scroll .vehicle-card {
                min-width: calc(33.333% - 1rem); /* 3 cards on tablets */
                max-width: calc(33.333% - 1rem);
                flex: 0 0 calc(33.333% - 1rem);
            }
        }

        @media (max-width: 767px) {
            .horizontal-scroll .vehicle-card {
                min-width: calc(50% - 0.75rem); /* 2 cards on mobile */
                max-width: calc(50% - 0.75rem);
                flex: 0 0 calc(50% - 0.75rem);
            }
        }

        /* Two at a time for recent and box ads */
        .dual-slide .slide-container {
            width: auto;
        }

        @media (min-width: 992px) {
            .dual-slide .vehicle-card {
                min-width: calc(25% - 1.125rem); /* EXACTLY 25% for 4 cards */
                max-width: calc(25% - 1.125rem);
                flex: 0 0 calc(25% - 1.125rem);
            }
        }

        /* Box Ads Section */
        .box-ads-section {
            padding: 2rem 0;
            background: white;
            border-top: 1px solid #e5e7eb;
            border-bottom: 1px solid #e5e7eb;
        }

        .box-ad-card {
            background: white;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow);
            transition: var(--transition);
            cursor: pointer;
            min-width: calc(50% - 0.75rem);
            flex-shrink: 0;
        }

        .box-ad-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .box-ad-image {
            height: 180px;
            overflow: hidden;
        }

        .box-ad-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .box-ad-content {
            padding: 1rem;
            text-align: center;
        }

        .box-ad-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--dark);
        }

        .box-ad-dealer {
            font-size: 0.9rem;
            color: var(--gray);
            font-weight: 500;
        }

        /* Categories Section */
        .categories-section {
            background: white;
            padding: 3rem 0;
        }

        .category-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
        }

        .category-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 1.5rem;
            text-align: center;
            transition: var(--transition);
            box-shadow: var(--shadow);
            text-decoration: none;
            color: inherit;
        }

        .category-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
            color: inherit;
            text-decoration: none;
        }

        .category-icon {
            width: 70px;
            height: 70px;
            background: var(--primary-light);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            transition: var(--transition);
        }

        .category-icon i {
            font-size: 1.8rem;
            color: var(--primary);
        }

        .category-card:hover .category-icon {
            background: var(--primary);
        }

        .category-card:hover .category-icon i {
            color: white;
        }

        .category-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--dark);
        }

        .category-count {
            color: var(--gray);
            font-weight: 500;
            font-size: 13px;
        }

        /* Auction Timer */
        .auction-timer {
            background: rgba(245, 158, 11, 0.1);
            color: #f59e0b;
            padding: 0.5rem;
            text-align: center;
            font-weight: 600;
            font-size: 13px;
        }

        .bid-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #f3f4f6;
        }

        .current-bid {
            font-weight: 700;
            color: var(--primary);
            font-size: 1.1rem;
        }

        .btn-bid {
            background: #f59e0b;
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius);
            font-weight: 600;
            font-size: 12px;
            transition: var(--transition);
        }

        /* Page-Specific Styles */
        .page-header {
            background: var(--dark);
            color: white;
            padding: 3rem 0 2rem;
            margin-top: 270px; /* MUCH LARGER - was 190px to account for 200px banner */
            text-align: center;
        }

        .page-header.no-gap {
            margin-top: 0;
            padding-top: 2.5rem;
        }

        .page-title {
            font-size: 2.2rem;
            font-weight: 700;
            margin-bottom: 0.75rem;
        }

        .page-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .content-card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .form-section h4 {
            color: var(--dark);
            font-weight: 600;
            margin-bottom: 1.5rem;
            font-size: 1.2rem;
        }

        .btn-submit {
            background: var(--primary);
            border: none;
            color: white;
            padding: 1rem 2rem;
            border-radius: var(--border-radius);
            font-weight: 600;
            transition: var(--transition);
            width: 100%;
            font-size: 14px;
        }

        .btn-submit:hover {
            background: var(--primary-dark);
            color: white;
        }

        /* Alert Messages */
        .alert {
            border-radius: var(--border-radius);
            border: none;
            padding: 1rem 1.5rem;
            margin-bottom: 1.5rem;
            font-weight: 500;
            font-size: 13px;
        }

        .alert-success {
            background: #d1fae5;
            color: #065f46;
        }

        .alert-danger {
            background: #fee2e2;
            color: #991b1b;
        }

        /* Loading States */
        .loading {
            text-align: center;
            padding: 3rem;
            color: var(--gray);
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #f3f4f6;
            border-top: 3px solid var(--primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1.5rem;
        }

        /* Footer */
        .footer {
            background: var(--dark);
            color: white;
            padding: 3rem 0 1.5rem;
            margin-top: 4rem;
        }

        .footer-brand {
            font-size: 1.8rem;
            font-weight: 700;
            color: white;
            margin-bottom: 1rem;
        }

        .footer-brand .text-danger {
            color: var(--primary) !important;
        }

        .footer-description {
            font-size: 13px;
            line-height: 1.6;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 1.5rem;
        }

        .footer-section h5 {
            color: var(--primary);
            font-weight: 600;
            margin-bottom: 1rem;
            font-size: 1rem;
        }

        .footer-link {
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            transition: var(--transition);
            padding: 0.3rem 0;
            display: inline-block;
            font-size: 13px;
        }

        .footer-link:hover {
            color: white;
            text-decoration: none;
        }

        .social-links {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
        }

        .social-link {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            transition: var(--transition);
            text-decoration: none;
        }

        .social-link:hover {
            background: var(--primary);
            color: white;
        }

        .footer-bottom {
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            margin-top: 2rem;
            padding-top: 1.5rem;
            text-align: center;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            body {
                font-size: 13px;
            }

            .navbar-brand {
                font-size: 1.3rem;
            }

            .nav-link {
                padding: 0.4rem 0.8rem !important;
                font-size: 13px;
            }

            .hero-title {
                font-size: 1.8rem;
            }
            
            .hero-subtitle {
                font-size: 0.9rem;
            }

            .hero-stat {
                padding: 0.75rem;
            }

            .hero-stat-number {
                font-size: 1.5rem;
            }

            .hero-stat-label {
                font-size: 11px;
            }
            
            .banner-ad-section {
                height: 150px; /* MUCH LARGER - was 100px */
                margin-top: 60px;
            }

            .banner-ad-placeholder {
                font-size: 14px; /* Larger text */
                padding: 0.75rem;
            }
            
            .search-card {
                padding: 1rem;
                margin: 0 0.5rem;
            }

            .search-tab {
                padding: 0.5rem 0.8rem;
                font-size: 12px;
            }

            .form-control, .form-select {
                padding: 0.5rem 0.75rem;
                font-size: 12px;
            }

            .form-label {
                font-size: 11px;
                margin-bottom: 0.3rem;
            }

            .btn-search {
                padding: 0.5rem 1rem;
                font-size: 12px;
            }
            
            .horizontal-scroll .vehicle-card {
                min-width: 200px;
            }

            .dual-slide .vehicle-card {
                min-width: calc(100% - 1rem);
            }

            .vehicle-image {
                height: 150px;
            }

            .vehicle-title {
                font-size: 1rem;
                margin-bottom: 0.75rem;
            }

            .vehicle-content {
                padding: 0.75rem;
            }

            .vehicle-badge {
                font-size: 10px;
                padding: 0.2rem 0.5rem;
            }

            .vehicle-price-overlay {
                font-size: 0.9rem;
                padding: 0.4rem 0.6rem;
            }

            .dealer-name {
                font-size: 12px;
            }

            .btn-view-details {
                padding: 0.4rem 0.8rem;
                font-size: 11px;
            }

            .section-label {
                font-size: 1rem;
            }

            .section-subtitle {
                font-size: 0.9rem;
            }

            .scroll-btn {
                width: 32px;
                height: 32px;
            }

            .box-ad-image {
                height: 140px;
            }

            .box-ad-content {
                padding: 0.75rem;
            }

            .box-ad-title {
                font-size: 1rem;
                margin-bottom: 0.4rem;
            }

            .box-ad-dealer {
                font-size: 0.8rem;
            }
            
            .category-grid {
                grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
                gap: 1rem;
            }

            .category-card {
                padding: 1.25rem;
            }

            .category-icon {
                width: 60px;
                height: 60px;
                margin-bottom: 1rem;
            }

            .category-icon i {
                font-size: 1.5rem;
            }

            .category-title {
                font-size: 1rem;
                margin-bottom: 0.4rem;
            }

            .category-count {
                font-size: 12px;
            }

            .footer-brand {
                font-size: 1.5rem;
            }

            .footer-description {
                font-size: 12px;
            }

            .footer-section h5 {
                font-size: 0.9rem;
            }

            .footer-link {
                font-size: 12px;
            }

            .social-link {
                width: 36px;
                height: 36px;
                font-size: 1.1rem;
            }

            .content-card {
                padding: 1.5rem;
                margin-bottom: 1.5rem;
            }

            .vehicle-detail {
                font-size: 11px;
                padding: 0.4rem;
                gap: 0.4rem;
            }

            .vehicle-feature {
                font-size: 10px;
                padding: 0.2rem 0.6rem;
            }

            .alert {
                padding: 0.75rem 1rem;
                font-size: 12px;
            }

            .modal-body {
                padding: 1.5rem;
            }

            .modal-title {
                font-size: 1.1rem;
            }

            .btn-submit {
                font-size: 13px;
                padding: 0.8rem 1.5rem;
            }
        }

        @media (max-width: 576px) {
            .hero-title {
                font-size: 1.5rem;
            }

            .hero-buttons {
                flex-direction: column;
                gap: 0.75rem;
            }

            .hero-btn {
                font-size: 13px;
                padding: 0.6rem 1.2rem;
            }

            .hero-stats {
                grid-template-columns: repeat(2, 1fr);
                gap: 1rem;
            }
            
            .horizontal-scroll .vehicle-card {
                min-width: 90vw;
            }
            
            .category-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .category-card {
                padding: 1rem;
            }
            
            .dual-slide .vehicle-card {
                min-width: 90vw;
            }

            .search-content .row {
                gap: 0.75rem;
            }

            .search-content .col-md-2,
            .search-content .col-md-3 {
                margin-bottom: 1rem;
            }

            .vehicle-details {
                grid-template-columns: repeat(2, 1fr);
                gap: 0.5rem;
            }

            .row.g-2 {
                gap: 0.75rem;
            }

            .modal-dialog {
                margin: 0.75rem;
            }

            .banner-ad-section {
                height: 120px; /* MUCH LARGER - was 80px */
            }

            .banner-ad-placeholder {
                font-size: 13px; /* Larger text */
                text-align: center;
            }
        }

        @media (max-width: 400px) {
            .hero-title {
                font-size: 1.3rem;
            }

            .hero-subtitle {
                font-size: 0.8rem;
            }

            .banner-ad-section {
                height: 100px; /* MUCH LARGER - was 70px */
            }

            .vehicle-card {
                margin-bottom: 1rem;
            }

            .search-card {
                margin: 0 0.25rem;
                padding: 0.75rem;
            }
        }
    </style>
</head>
<body>
    <!-- Preloader -->
    <div id="preloader">
        <div class="preloader-content">
            <div class="preloader-logo">
                Trucks<span class="text-danger">ON</span>Sale
            </div>
            <div class="preloader-spinner"></div>
            <p>Loading marketplace...</p>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand" href="?">
                Trucks<span class="text-danger">ON</span>Sale
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="?">
                            <i class="bi bi-house me-1"></i>Home
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-grid me-1"></i>Categories
                        </a>
                        <ul class="dropdown-menu">
                            <?php foreach ($categories_data as $key => $category): ?>
                                <li><a class="dropdown-item" href="?category=<?= $key ?>">
                                    <i class="<?= $category['icon'] ?> me-2"></i><?= $category['name'] ?>
                                </a></li>
                            <?php endforeach; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="?page=vehicles">
                                <i class="bi bi-grid me-2"></i>View All Vehicles
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="?page=auctions">
                            <i class="bi bi-hammer me-1"></i>Auctions <span class="badge bg-warning text-dark"><?= $auction_count ?></span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="?page=hire">
                            <i class="bi bi-calendar-check me-1"></i>Hire <span class="badge bg-success"><?= $hire_count ?></span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="?page=rent-to-own">
                            <i class="bi bi-arrow-repeat me-1"></i>Rent-to-Own
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="listing.php">
                            <i class="bi bi-box-arrow-in-right me-1"></i>Login
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Premium Banner Ad Section -->
    <section class="banner-ad-section <?= ($page !== 'home' && !$vehicle_details) ? 'no-gap' : '' ?>">
        <div class="container-fluid">
            <div class="banner-ad-container" id="bannerAdContainer">
                <?php if (!empty($banner_ads)): ?>
                    <?php foreach ($banner_ads as $index => $ad): ?>
                        <div class="banner-ad <?= $index === 0 ? 'active' : '' ?>" onclick="<?= $ad['link_url'] ? "window.open('" . htmlspecialchars($ad['link_url']) . "', '_blank')" : '' ?>">
                            <?php if ($ad['image_url'] && file_exists($ad['image_url'])): ?>
                                <img src="<?= htmlspecialchars($ad['image_url']) ?>" alt="<?= htmlspecialchars($ad['title']) ?>">
                            <?php else: ?>
                                <div style="background: var(--primary); color: white; padding: 1rem; text-align: center; font-weight: 600; height: 100%; display: flex; align-items: center; justify-content: center;">
                                    <?= htmlspecialchars($ad['title']) ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="banner-ad active">
                        <div class="banner-ad-placeholder">
                            <i class="bi bi-image me-2"></i>Premium Banner Advertisement Space - Admin Only
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <?php if ($page === 'auctions'): ?>
    <!-- Auctions Page -->
    <div class="page-header no-gap">
        <div class="container">
            <h1 class="page-title">Live Auctions</h1>
            <p class="page-subtitle">Bid on premium commercial vehicles from verified dealers</p>
            <div class="mt-3">
                <span class="badge bg-warning text-dark fs-6 px-3 py-2">
                    <i class="bi bi-hammer me-2"></i><?= $auction_count ?> Active Auctions
                </span>
            </div>
        </div>
    </div>

    <section class="py-4">
        <div class="container">
            <?php if (isset($bid_success)): ?>
                <div class="alert alert-success">
                    <i class="bi bi-check-circle me-2"></i>Your bid has been placed successfully!
                </div>
            <?php endif; ?>
            
            <?php if (isset($bid_error)): ?>
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-circle me-2"></i><?= $bid_error ?>
                </div>
            <?php endif; ?>
            
            <div class="row g-4" id="auctionGrid">
                <div class="loading">
                    <div class="loading-spinner"></div>
                    <p class="mt-2">Loading auctions...</p>
                </div>
            </div>
        </div>
    </section>

    <?php elseif ($page === 'hire'): ?>
    <!-- Hire Page -->
    <div class="page-header no-gap">
        <div class="container">
            <h1 class="page-title">Vehicle Hire</h1>
            <p class="page-subtitle">Rent commercial vehicles for short or long-term projects</p>
            <div class="mt-3">
                <span class="badge bg-success fs-6 px-3 py-2">
                    <i class="bi bi-calendar-check me-2"></i><?= $hire_count ?> Available for Hire
                </span>
            </div>
        </div>
    </div>

    <section class="py-4">
        <div class="container">
            <?php if (isset($hire_success)): ?>
                <div class="alert alert-success">
                    <i class="bi bi-check-circle me-2"></i>Your hire booking has been submitted successfully!
                </div>
            <?php endif; ?>
            
            <?php if (isset($hire_error)): ?>
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-circle me-2"></i><?= $hire_error ?>
                </div>
            <?php endif; ?>
            
            <div class="row g-4" id="hireGrid">
                <div class="loading">
                    <div class="loading-spinner"></div>
                    <p class="mt-2">Loading hire vehicles...</p>
                </div>
            </div>
        </div>
    </section>

    <?php elseif ($page === 'rent-to-own'): ?>
    <!-- Rent-to-Own Page -->
    <div class="page-header no-gap">
        <div class="container">
            <h1 class="page-title">Rent-to-Own Vehicles</h1>
            <p class="page-subtitle">Flexible ownership options with manageable monthly payments</p>
        </div>
    </div>

    <section class="py-4">
        <div class="container">
            <div class="row g-4" id="rentToOwnGrid">
                <div class="loading">
                    <div class="loading-spinner"></div>
                    <p class="mt-2">Loading rent-to-own vehicles...</p>
                </div>
            </div>
        </div>
    </section>

    <?php elseif ($vehicle_details): ?>
    <!-- Vehicle Detail Page -->
    <div style="margin-top: 0; padding: 2rem 0;">
        <div class="container">
            <div class="row">
                <div class="col-lg-8">
                    <!-- Vehicle Gallery -->
                    <div style="border-radius: var(--border-radius); overflow: hidden; box-shadow: var(--shadow-lg); margin-bottom: 2rem;">
                        <?php if ($vehicle_images && $vehicle_images->num_rows > 0): ?>
                            <div id="vehicleCarousel" class="carousel slide" data-bs-ride="carousel">
                                <div class="carousel-inner">
                                    <?php $first = true; $vehicle_images->data_seek(0); ?>
                                    <?php while ($image = $vehicle_images->fetch_assoc()): ?>
                                        <div class="carousel-item <?= $first ? 'active' : '' ?>">
                                            <img src="<?= htmlspecialchars($image['image_path']) ?>" 
                                                 class="d-block w-100" 
                                                 style="height: 400px; object-fit: cover;"
                                                 alt="<?= htmlspecialchars($vehicle_details['make'] . ' ' . $vehicle_details['model']) ?>">
                                        </div>
                                        <?php $first = false; ?>
                                    <?php endwhile; ?>
                                </div>
                                <?php if ($vehicle_images->num_rows > 1): ?>
                                <button class="carousel-control-prev" type="button" data-bs-target="#vehicleCarousel" data-bs-slide="prev">
                                    <span class="carousel-control-prev-icon"></span>
                                </button>
                                <button class="carousel-control-next" type="button" data-bs-target="#vehicleCarousel" data-bs-slide="next">
                                    <span class="carousel-control-next-icon"></span>
                                </button>
                                <?php endif; ?>
                            </div>
                        <?php else: ?>
                            <div class="bg-light d-flex align-items-center justify-content-center" style="height: 400px;">
                                <div class="text-center">
                                    <i class="bi bi-image text-muted" style="font-size: 3rem;"></i>
                                    <p class="text-muted mt-2">No images available</p>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Vehicle Information -->
                    <div class="content-card">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <h1 style="font-size: 1.8rem; font-weight: 700; margin-bottom: 1rem;">
                                <?= htmlspecialchars($vehicle_details['make']) ?> <?= htmlspecialchars($vehicle_details['model']) ?>
                                <?php if ($vehicle_details['year']): ?>
                                    <span class="text-muted">(<?= $vehicle_details['year'] ?>)</span>
                                <?php endif; ?>
                            </h1>
                            <span class="badge bg-<?= $vehicle_details['listing_type'] == 'auction' ? 'warning' : ($vehicle_details['listing_type'] == 'hire' ? 'success' : ($vehicle_details['listing_type'] == 'rent-to-own' ? 'info' : 'primary')) ?> fs-6">
                                <?= ucfirst(str_replace('-', ' ', $vehicle_details['listing_type'])) ?>
                            </span>
                        </div>
                        
                        <div class="d-flex gap-2 mb-3 flex-wrap">
                            <?php if ($vehicle_details['featured']): ?>
                                <span class="badge bg-warning">Featured</span>
                            <?php endif; ?>
                            <?php if ($vehicle_details['no_accidents']): ?>
                                <span class="badge bg-success">No Accidents</span>
                            <?php endif; ?>
                            <?php if ($vehicle_details['warranty']): ?>
                                <span class="badge bg-info">Warranty</span>
                            <?php endif; ?>
                            <?php if ($vehicle_details['service_history']): ?>
                                <span class="badge bg-secondary">Service History</span>
                            <?php endif; ?>
                            <?php if ($vehicle_details['roadworthy']): ?>
                                <span class="badge bg-success">Roadworthy</span>
                            <?php endif; ?>
                        </div>

                        <!-- Vehicle Specifications -->
                        <h5 class="fw-bold mb-3">
                            <i class="bi bi-gear me-2 text-danger"></i>Specifications
                        </h5>
                        <div class="row g-2 mb-4">
                            <?php if ($vehicle_details['mileage']): ?>
                            <div class="col-6 col-md-4">
                                <div class="text-center p-2 bg-light rounded">
                                    <i class="bi bi-speedometer2 text-danger mb-1"></i>
                                    <div class="fw-bold small"><?= number_format($vehicle_details['mileage']) ?> km</div>
                                    <small class="text-muted">Mileage</small>
                                </div>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($vehicle_details['hours_used']): ?>
                            <div class="col-6 col-md-4">
                                <div class="text-center p-2 bg-light rounded">
                                    <i class="bi bi-clock text-danger mb-1"></i>
                                    <div class="fw-bold small"><?= number_format($vehicle_details['hours_used']) ?> hrs</div>
                                    <small class="text-muted">Hours Used</small>
                                </div>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($vehicle_details['year']): ?>
                            <div class="col-6 col-md-4">
                                <div class="text-center p-2 bg-light rounded">
                                    <i class="bi bi-calendar text-danger mb-1"></i>
                                    <div class="fw-bold small"><?= $vehicle_details['year'] ?></div>
                                    <small class="text-muted">Year</small>
                                </div>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($vehicle_details['fuel_type']): ?>
                            <div class="col-6 col-md-4">
                                <div class="text-center p-2 bg-light rounded">
                                    <i class="bi bi-fuel-pump text-danger mb-1"></i>
                                    <div class="fw-bold small"><?= ucfirst($vehicle_details['fuel_type']) ?></div>
                                    <small class="text-muted">Fuel Type</small>
                                </div>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($vehicle_details['transmission']): ?>
                            <div class="col-6 col-md-4">
                                <div class="text-center p-2 bg-light rounded">
                                    <i class="bi bi-gear text-danger mb-1"></i>
                                    <div class="fw-bold small"><?= ucfirst($vehicle_details['transmission']) ?></div>
                                    <small class="text-muted">Transmission</small>
                                </div>
                            </div>
                            <?php endif; ?>
                            
                            <div class="col-6 col-md-4">
                                <div class="text-center p-2 bg-light rounded">
                                    <i class="bi bi-geo-alt text-danger mb-1"></i>
                                    <div class="fw-bold small"><?= htmlspecialchars($vehicle_details['city']) ?></div>
                                    <small class="text-muted">Location</small>
                                </div>
                            </div>
                        </div>

                        <!-- Special Features for Auction -->
                        <?php if ($vehicle_details['listing_type'] == 'auction'): ?>
                        <div class="alert alert-warning">
                            <h6 class="fw-bold mb-2"><i class="bi bi-hammer me-2"></i>Auction Details</h6>
                            <?php if ($vehicle_details['auction_end_date']): ?>
                                <div class="mb-2">
                                    <strong>Auction Ends:</strong> <?= date('M j, Y g:i A', strtotime($vehicle_details['auction_end_date'])) ?>
                                </div>
                            <?php endif; ?>
                            <div class="mb-2">
                                <strong>Current Bid:</strong> R <?= number_format($vehicle_details['current_bid'] ?? 0) ?>
                            </div>
                            <?php if ($vehicle_details['reserve_price']): ?>
                                <div>
                                    <strong>Reserve Price:</strong> R <?= number_format($vehicle_details['reserve_price']) ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>

                        <!-- Special Features for Hire -->
                        <?php if ($vehicle_details['listing_type'] == 'hire'): ?>
                        <div class="alert alert-success">
                            <h6 class="fw-bold mb-2"><i class="bi bi-calendar-check me-2"></i>Hire Rates</h6>
                            <?php if ($vehicle_details['daily_rate']): ?>
                                <div class="mb-1">
                                    <strong>Daily Rate:</strong> R <?= number_format($vehicle_details['daily_rate']) ?>
                                </div>
                            <?php endif; ?>
                            <?php if ($vehicle_details['weekly_rate']): ?>
                                <div class="mb-1">
                                    <strong>Weekly Rate:</strong> R <?= number_format($vehicle_details['weekly_rate']) ?>
                                </div>
                            <?php endif; ?>
                            <?php if ($vehicle_details['monthly_rate']): ?>
                                <div>
                                    <strong>Monthly Rate:</strong> R <?= number_format($vehicle_details['monthly_rate']) ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>

                        <!-- Description -->
                        <?php if ($vehicle_details['description']): ?>
                        <h5 class="fw-bold mb-2">
                            <i class="bi bi-card-text me-2 text-danger"></i>Description
                        </h5>
                        <p class="text-muted mb-3"><?= nl2br(htmlspecialchars($vehicle_details['description'])) ?></p>
                        <?php endif; ?>

                        <!-- Features -->
                        <?php if ($vehicle_details['features']): ?>
                        <h5 class="fw-bold mb-2">
                            <i class="bi bi-check2-square me-2 text-danger"></i>Features
                        </h5>
                        <p class="text-muted mb-3"><?= nl2br(htmlspecialchars($vehicle_details['features'])) ?></p>
                        <?php endif; ?>

                        <!-- Videos -->
                        <?php if ($vehicle_videos && $vehicle_videos->num_rows > 0): ?>
                        <h5 class="fw-bold mb-3">
                            <i class="bi bi-play-circle me-2 text-danger"></i>Videos
                        </h5>
                        <div class="row g-3 mb-4">
                            <?php while ($video = $vehicle_videos->fetch_assoc()): ?>
                                <div class="col-md-6">
                                    <div class="video-thumbnail" style="position: relative; cursor: pointer;" onclick="playVideo('<?= htmlspecialchars($video['video_path']) ?>')">
                                        <div class="bg-dark d-flex align-items-center justify-content-center" style="height: 200px; border-radius: var(--border-radius);">
                                            <i class="bi bi-play-circle text-white" style="font-size: 3rem;"></i>
                                        </div>
                                        <div class="position-absolute bottom-0 start-0 end-0 p-3 bg-gradient text-white" style="border-radius: 0 0 var(--border-radius) var(--border-radius);">
                                            <h6 class="mb-0"><?= htmlspecialchars($video['video_title'] ?? 'Vehicle Video') ?></h6>
                                        </div>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        </div>
                        <?php endif; ?>

                        <!-- Documents -->
                        <?php if ($vehicle_documents && $vehicle_documents->num_rows > 0): ?>
                        <h5 class="fw-bold mb-3">
                            <i class="bi bi-file-earmark-pdf me-2 text-danger"></i>Documents
                        </h5>
                        <div class="row g-2 mb-4">
                            <?php while ($document = $vehicle_documents->fetch_assoc()): ?>
                                <div class="col-md-6">
                                    <a href="<?= htmlspecialchars($document['document_path']) ?>" target="_blank" 
                                       class="d-flex align-items-center p-3 bg-light rounded text-decoration-none">
                                        <i class="bi bi-file-earmark-pdf text-danger me-3" style="font-size: 1.5rem;"></i>
                                        <div>
                                            <div class="fw-bold text-dark"><?= htmlspecialchars($document['document_name']) ?></div>
                                            <small class="text-muted"><?= strtoupper($document['document_type']) ?></small>
                                        </div>
                                    </a>
                                </div>
                            <?php endwhile; ?>
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- Inquiry Form -->
                    <div class="content-card">
                        <div class="form-section">
                            <h4><i class="bi bi-envelope-heart text-danger me-2"></i>Make an Inquiry</h4>
                            
                            <?php if (isset($inquiry_success)): ?>
                                <div class="alert alert-success">
                                    <i class="bi bi-check-circle me-2"></i>Your inquiry has been sent successfully!
                                </div>
                            <?php endif; ?>
                            
                            <?php if (isset($inquiry_error)): ?>
                                <div class="alert alert-danger">
                                    <i class="bi bi-exclamation-circle me-2"></i><?= $inquiry_error ?>
                                </div>
                            <?php endif; ?>
                            
                            <form method="POST" action="">
                                <input type="hidden" name="vehicle_id" value="<?= $vehicle_details['vehicle_id'] ?>">
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Inquiry Type</label>
                                        <select class="form-select" name="inquiry_type" required>
                                            <option value="general">General Information</option>
                                            <option value="finance">Finance Options</option>
                                            <option value="trade_in">Trade-in Value</option>
                                            <option value="inspection">Schedule Inspection</option>
                                            <option value="test_drive">Test Drive</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Preferred Contact</label>
                                        <select class="form-select" name="preferred_contact">
                                            <option value="phone">Phone Call</option>
                                            <option value="email">Email</option>
                                            <option value="whatsapp">WhatsApp</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Full Name</label>
                                        <input type="text" class="form-control" name="full_name" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Email Address</label>
                                        <input type="email" class="form-control" name="email" required>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control" name="phone" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Your Message</label>
                                    <textarea class="form-control" name="message" required rows="4"><?= "I am interested in this " . $vehicle_details['year'] . " " . $vehicle_details['make'] . " " . $vehicle_details['model'] . ". Please contact me with more information." ?></textarea>
                                </div>
                                
                                <button type="submit" name="submit_inquiry" class="btn-submit">
                                    <i class="bi bi-send me-2"></i>Send Inquiry
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <!-- Price Section -->
                    <div class="content-card sticky-top" style="top: 90px;">
                        <div class="text-center p-3 bg-danger text-white rounded mb-3">
                            <div style="font-size: 2rem; font-weight: 700;">
                                <?php if ($vehicle_details['listing_type'] == 'auction'): ?>
                                    Current Bid: R <?= number_format($vehicle_details['current_bid'] ?? 0) ?>
                                <?php elseif ($vehicle_details['listing_type'] == 'hire'): ?>
                                    From R <?= number_format($vehicle_details['daily_rate'] ?? 0) ?>/day
                                <?php else: ?>
                                    R <?= number_format($vehicle_details['price'], 0) ?>
                                <?php endif; ?>
                            </div>
                            <div>
                                <?php if ($vehicle_details['listing_type'] == 'auction'): ?>
                                    Reserve: R <?= number_format($vehicle_details['reserve_price'] ?? 0) ?>
                                <?php elseif ($vehicle_details['listing_type'] == 'hire'): ?>
                                    Daily Rate
                                <?php else: ?>
                                    Excluding VAT & Delivery
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="text-center mb-3">
                            <div style="width: 60px; height: 60px;" class="dealer-avatar mx-auto mb-2 d-flex align-items-center justify-content-center bg-danger text-white rounded-circle fw-bold">
                                <?= strtoupper(substr($vehicle_details['company_name'] ?: $vehicle_details['username'], 0, 2)) ?>
                            </div>
                            <h6 class="fw-bold"><?= htmlspecialchars($vehicle_details['company_name'] ?: $vehicle_details['username']) ?></h6>
                            <div class="text-success small">
                                <i class="bi bi-patch-check-fill me-1"></i>Verified Dealer
                            </div>
                        </div>

                        <div class="d-grid gap-2">
                            <?php if ($vehicle_details['phone']): ?>
                            <a href="tel:<?= $vehicle_details['phone'] ?>" class="btn btn-danger">
                                <i class="bi bi-telephone me-2"></i>Call Dealer
                            </a>
                            <?php endif; ?>
                            
                            <?php if ($vehicle_details['listing_type'] == 'auction'): ?>
                            <button class="btn btn-warning" onclick="showBidModal(<?= $vehicle_details['vehicle_id'] ?>, <?= $vehicle_details['current_bid'] ?? 0 ?>)">
                                <i class="bi bi-hammer me-2"></i>Place Bid
                            </button>
                            <?php elseif ($vehicle_details['listing_type'] == 'hire'): ?>
                            <button class="btn btn-success" onclick="showHireModal(<?= $vehicle_details['vehicle_id'] ?>)">
                                <i class="bi bi-calendar-check me-2"></i>Book Hire
                            </button>
                            <?php endif; ?>
                            
                            <?php if ($vehicle_details['finance_available']): ?>
                            <button class="btn btn-info">
                                <i class="bi bi-credit-card me-2"></i>Finance Options
                            </button>
                            <?php endif; ?>
                        </div>

                        <div class="mt-3 p-3 bg-light rounded">
                            <div class="small">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="bi bi-shield-check text-success me-2"></i>
                                    <span>Verified Dealer</span>
                                </div>
                                <div class="d-flex align-items-center mb-2">
                                    <i class="bi bi-geo-alt text-primary me-2"></i>
                                    <span><?= htmlspecialchars($vehicle_details['city']) ?>, <?= htmlspecialchars($vehicle_details['region']) ?></span>
                                </div>
                                <div class="d-flex align-items-center mb-2">
                                    <i class="bi bi-calendar text-info me-2"></i>
                                    <span>Listed <?= date('M j, Y', strtotime($vehicle_details['created_at'])) ?></span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-eye text-secondary me-2"></i>
                                    <span><?= number_format($vehicle_details['views']) ?> views</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php else: ?>
    <!-- Main Website Content -->
    
    <?php if (!$search_category && !$search_region && !$search_query && $page === 'home'): ?>
    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-12">
                    <div class="text-center">
                        <h1 class="hero-title">Find Your Perfect Commercial Vehicle</h1>
                        <div class="hero-buttons">
                            <a href="#search-section" class="hero-btn btn btn-light">
                                <i class="bi bi-search me-2"></i>Browse Vehicles
                            </a>
                            <a href="?page=vehicles" class="hero-btn btn btn-outline-light">
                                <i class="bi bi-grid me-2"></i>View All Vehicles
                            </a>
                        </div>
                        <div class="hero-stats">
                            <div class="hero-stat">
                                <span class="hero-stat-number"><?= number_format($total_vehicles) ?></span>
                                <div class="hero-stat-label">Vehicles Available</div>
                            </div>
                            <div class="hero-stat">
                                <span class="hero-stat-number"><?= number_format($auction_count) ?></span>
                                <div class="hero-stat-label">Live Auctions</div>
                            </div>
                            <div class="hero-stat">
                                <span class="hero-stat-number"><?= number_format($hire_count) ?></span>
                                <div class="hero-stat-label">Hire Available</div>
                            </div>
                            <div class="hero-stat">
                                <span class="hero-stat-number">500+</span>
                                <div class="hero-stat-label">Verified Dealers</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Search Section -->
    <section class="search-section" id="search-section">
        <div class="container">
            <div class="search-card">
                <div class="search-tabs">
                    <button class="search-tab active" data-tab="quick">
                        <i class="bi bi-lightning me-1"></i>Quick Search
                    </button>
                    <button class="search-tab" data-tab="advanced">
                        <i class="bi bi-sliders me-1"></i>Advanced Filters
                    </button>
                </div>
                
                <form id="searchForm" method="GET" action="">
                    <input type="hidden" name="page" value="<?= $page === 'home' ? 'vehicles' : $page ?>">
                    
                    <div id="quickSearch" class="search-content active">
                        <div class="row g-2">
                            <div class="col-md-2">
                                <label class="form-label">Category</label>
                                <select class="form-select" name="category" id="categorySelect">
                                    <option value="">All Categories</option>
                                    <?php foreach ($categories_data as $key => $category): ?>
                                        <option value="<?= $key ?>" <?= $search_category == $key ? 'selected' : '' ?>>
                                            <?= $category['name'] ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Type</label>
                                <select class="form-select" name="subcategory" id="subcategorySelect">
                                    <option value="">All Types</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Make</label>
                                <select class="form-select" name="make" id="makeSelect">
                                    <option value="">All Makes</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Listing Type</label>
                                <select class="form-select" name="listing_type">
                                    <option value="">All Types</option>
                                    <option value="sale">For Sale</option>
                                    <option value="auction">Auction</option>
                                    <option value="hire">Hire</option>
                                    <option value="rent-to-own">Rent-to-Own</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Region</label>
                                <select class="form-select" name="region">
                                    <option value="">All Regions</option>
                                    <option value="Gauteng" <?= $search_region == 'Gauteng' ? 'selected' : '' ?>>Gauteng</option>
                                    <option value="Western Cape" <?= $search_region == 'Western Cape' ? 'selected' : '' ?>>Western Cape</option>
                                    <option value="KwaZulu-Natal" <?= $search_region == 'KwaZulu-Natal' ? 'selected' : '' ?>>KwaZulu-Natal</option>
                                    <option value="Eastern Cape" <?= $search_region == 'Eastern Cape' ? 'selected' : '' ?>>Eastern Cape</option>
                                    <option value="Free State" <?= $search_region == 'Free State' ? 'selected' : '' ?>>Free State</option>
                                    <option value="Limpopo" <?= $search_region == 'Limpopo' ? 'selected' : '' ?>>Limpopo</option>
                                    <option value="Mpumalanga" <?= $search_region == 'Mpumalanga' ? 'selected' : '' ?>>Mpumalanga</option>
                                    <option value="North West" <?= $search_region == 'North West' ? 'selected' : '' ?>>North West</option>
                                    <option value="Northern Cape" <?= $search_region == 'Northern Cape' ? 'selected' : '' ?>>Northern Cape</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Search</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" name="query" 
                                           placeholder="Make, model..." value="<?= htmlspecialchars($search_query) ?>">
                                    <button type="submit" class="btn btn-search">
                                        <i class="bi bi-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div id="advancedSearch" class="search-content">
                        <div class="row g-2">
                            <div class="col-md-3">
                                <label class="form-label">Category</label>
                                <select class="form-select" name="category">
                                    <option value="">All Categories</option>
                                    <?php foreach ($categories_data as $key => $category): ?>
                                        <option value="<?= $key ?>" <?= $search_category == $key ? 'selected' : '' ?>>
                                            <?= $category['name'] ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Listing Type</label>
                                <select class="form-select" name="listing_type">
                                    <option value="">All Types</option>
                                    <option value="sale">For Sale</option>
                                    <option value="auction">Auction</option>
                                    <option value="hire">Hire</option>
                                    <option value="rent-to-own">Rent-to-Own</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Region</label>
                                <select class="form-select" name="region">
                                    <option value="">All Regions</option>
                                    <option value="Gauteng">Gauteng</option>
                                    <option value="Western Cape">Western Cape</option>
                                    <option value="KwaZulu-Natal">KwaZulu-Natal</option>
                                    <option value="Eastern Cape">Eastern Cape</option>
                                    <option value="Free State">Free State</option>
                                    <option value="Limpopo">Limpopo</option>
                                    <option value="Mpumalanga">Mpumalanga</option>
                                    <option value="North West">North West</option>
                                    <option value="Northern Cape">Northern Cape</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Search Keywords</label>
                                <input type="text" class="form-control" name="query" 
                                       placeholder="Make, model, keywords..." value="<?= htmlspecialchars($search_query) ?>">
                            </div>
                        </div>
                        
                        <div class="row g-2 mt-1">
                            <div class="col-md-6">
                                <label class="form-label">Price Range (R)</label>
                                <div class="row g-2">
                                    <div class="col">
                                        <input type="number" class="form-control" name="min_price" placeholder="Min Price">
                                    </div>
                                    <div class="col">
                                        <input type="number" class="form-control" name="max_price" placeholder="Max Price">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Year Range</label>
                                <div class="row g-2">
                                    <div class="col">
                                        <input type="number" class="form-control" name="min_year" placeholder="Min Year">
                                    </div>
                                    <div class="col">
                                        <input type="number" class="form-control" name="max_year" placeholder="Max Year">
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-center mt-2">
                            <button type="submit" class="btn btn-search">
                                <i class="bi bi-search me-2"></i>Search Vehicles
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </section>

    <!-- Box Ads Section -->
    <?php if (!empty($box_ads) && !$search_category && !$search_region && !$search_query && $page === 'home'): ?>
    <section class="box-ads-section">
        <div class="container">
            <div class="section-controls">
                <div>
                    <h4 class="section-label">Premium Advertisements</h4>
                    <p class="section-subtitle mb-0" style="font-size: 0.9rem;">Featured business solutions</p>
                </div>
                <div class="scroll-controls">
                    <button class="scroll-btn" onclick="scrollSection('boxAds', 'left')">
                        <i class="bi bi-chevron-left"></i>
                    </button>
                    <button class="scroll-btn" onclick="scrollSection('boxAds', 'right')">
                        <i class="bi bi-chevron-right"></i>
                    </button>
                </div>
            </div>
            
            <div class="horizontal-scroll dual-slide" id="boxAds">
                <div class="slide-container" id="boxAdsContainer">
                    <?php foreach ($box_ads as $ad): ?>
                        <div class="box-ad-card" onclick="<?= $ad['link_url'] ? "window.open('" . htmlspecialchars($ad['link_url']) . "', '_blank')" : '' ?>">
                            <div class="box-ad-image">
                                <?php if ($ad['image_url'] && file_exists($ad['image_url'])): ?>
                                    <img src="<?= htmlspecialchars($ad['image_url']) ?>" alt="<?= htmlspecialchars($ad['title']) ?>">
                                <?php else: ?>
                                    <div style="background: var(--primary); color: white; height: 100%; display: flex; align-items: center; justify-content: center; font-weight: 600;">
                                        No Image
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="box-ad-content">
                                <h5 class="box-ad-title"><?= htmlspecialchars($ad['title']) ?></h5>
                                <p class="box-ad-dealer">Premium Advertisement</p>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Vehicles Sections -->
    <section class="vehicles-section">
        <div class="container">
            <?php if (!$search_category && !$search_region && !$search_query && $page === 'home'): ?>
                
                <!-- Featured Vehicles -->
                <div class="section-controls">
                    <div>
                        <h4 class="section-label">Featured Vehicles</h4>
                        <p class="section-subtitle mb-0" style="font-size: 0.9rem;">Premium listings from verified dealers</p>
                    </div>
                    <div class="scroll-controls">
                        <button class="scroll-btn" onclick="scrollSection('featuredVehicles', 'left')">
                            <i class="bi bi-chevron-left"></i>
                        </button>
                        <button class="scroll-btn" onclick="scrollSection('featuredVehicles', 'right')">
                            <i class="bi bi-chevron-right"></i>
                        </button>
                    </div>
                </div>
                
                <div class="horizontal-scroll" id="featuredVehicles">
                    <div class="slide-container" id="featuredContainer">
                        <div class="loading">
                            <div class="loading-spinner"></div>
                            <p class="mt-2">Loading featured vehicles...</p>
                        </div>
                    </div>
                </div>

                <!-- Recent Vehicles -->
                <div class="section-controls mt-3">
                    <div>
                        <h4 class="section-label">Latest Listings</h4>
                        <p class="section-subtitle mb-0" style="font-size: 0.9rem;">Fresh listings from our network</p>
                    </div>
                    <div class="scroll-controls">
                        <button class="scroll-btn" onclick="scrollSection('recentVehicles', 'left')">
                            <i class="bi bi-chevron-left"></i>
                        </button>
                        <button class="scroll-btn" onclick="scrollSection('recentVehicles', 'right')">
                            <i class="bi bi-chevron-right"></i>
                        </button>
                    </div>
                </div>
                
                <div class="horizontal-scroll dual-slide" id="recentVehicles">
                    <div class="slide-container" id="recentContainer">
                        <div class="loading">
                            <div class="loading-spinner"></div>
                            <p class="mt-2">Loading recent vehicles...</p>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-3">
                    <a href="?page=vehicles" class="btn btn-search">
                        <i class="bi bi-grid me-2"></i>View All Vehicles
                    </a>
                </div>
            <?php else: ?>
                <div class="section-header">
                    <h2 class="section-title">
                        <?php if ($search_category): ?>
                            <?= $categories_data[$search_category]['name'] ?? ucfirst($search_category) ?>
                        <?php elseif ($search_query): ?>
                            Search Results for "<?= htmlspecialchars($search_query) ?>"
                        <?php else: ?>
                            All Vehicles
                        <?php endif; ?>
                    </h2>
                    <div class="section-subtitle" id="vehicleCount">Loading...</div>
                </div>
                
                <div class="row g-3" id="vehicleGrid">
                    <div class="loading">
                        <div class="loading-spinner"></div>
                        <p class="mt-2">Loading vehicles...</p>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <?php if (!$search_category && !$search_region && !$search_query && $page === 'home'): ?>
    <!-- Categories Section -->
    <section class="categories-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Browse by Category</h2>
                <p class="section-subtitle">Find exactly what you need in our specialized vehicle categories</p>
            </div>
            
            <div class="category-grid">
                <?php foreach ($categories_data as $key => $category): ?>
                    <?php if ($category_counts[$key] > 0): ?>
                        <a href="?category=<?= $key ?>" class="category-card">
                            <div class="category-icon">
                                <i class="<?= $category['icon'] ?>"></i>
                            </div>
                            <h3 class="category-title"><?= $category['name'] ?></h3>
                            <p class="category-count"><?= number_format($category_counts[$key]) ?> vehicles available</p>
                        </a>
                    <?php endif; ?>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <?php endif; ?>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-12 col-lg-4 mb-4 text-center text-lg-start">
                    <div class="footer-brand">
                        Trucks<span class="text-danger">ON</span>Sale
                    </div>
                    <p class="footer-description">
                        South Africa's premier online marketplace for trucks and commercial vehicles. Connect with verified dealers and find your perfect vehicle today.
                    </p>
                    <div class="social-links">
                        <a href="#" class="social-link"><i class="bi bi-facebook"></i></a>
                        <a href="#" class="social-link"><i class="bi bi-twitter"></i></a>
                        <a href="#" class="social-link"><i class="bi bi-instagram"></i></a>
                        <a href="#" class="social-link"><i class="bi bi-linkedin"></i></a>
                    </div>
                </div>
                
                <div class="col-6 col-md-3 col-lg-2 mb-3">
                    <div class="footer-section">
                        <h5>Quick Links</h5>
                        <ul class="list-unstyled">
                            <li><a href="?" class="footer-link">Home</a></li>
                            <li><a href="?page=vehicles" class="footer-link">All Vehicles</a></li>
                            <li><a href="?page=auctions" class="footer-link">Live Auctions</a></li>
                            <li><a href="?page=hire" class="footer-link">Vehicle Hire</a></li>
                            <li><a href="?page=rent-to-own" class="footer-link">Rent-to-Own</a></li>
                        </ul>
                    </div>
                </div>
                
                <div class="col-6 col-md-3 col-lg-2 mb-3">
                    <div class="footer-section">
                        <h5>Categories</h5>
                        <ul class="list-unstyled">
                            <?php $counter = 0; foreach ($categories_data as $key => $category): if ($counter >= 5) break; ?>
                                <li><a href="?category=<?= $key ?>" class="footer-link"><?= $category['name'] ?></a></li>
                            <?php $counter++; endforeach; ?>
                        </ul>
                    </div>
                </div>
                
                <div class="col-6 col-md-3 col-lg-2 mb-3">
                    <div class="footer-section">
                        <h5>Support</h5>
                        <ul class="list-unstyled">
                            <li><a href="#" class="footer-link">Help Center</a></li>
                            <li><a href="#" class="footer-link">Safety Tips</a></li>
                            <li><a href="#" class="footer-link">Contact Us</a></li>
                            <li><a href="#" class="footer-link">Dealer Portal</a></li>
                        </ul>
                    </div>
                </div>
                
                <div class="col-6 col-md-3 col-lg-2 mb-3">
                    <div class="footer-section">
                        <h5>Legal</h5>
                        <ul class="list-unstyled">
                            <li><a href="#" class="footer-link">Terms of Service</a></li>
                            <li><a href="#" class="footer-link">Privacy Policy</a></li>
                            <li><a href="#" class="footer-link">Cookie Policy</a></li>
                            <li><a href="#" class="footer-link">Disclaimer</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom">
                <div class="text-center">
                    <p class="mb-0">&copy; 2024 TrucksONSale. All rights reserved. | Powered by Advanced Marketplace Technology</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bid Modal -->
    <div class="modal fade" id="bidModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Place Your Bid</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <input type="hidden" name="vehicle_id" id="bidVehicleId">
                        
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            <strong>Current Bid:</strong> R <span id="currentBidAmount">0</span>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Full Name</label>
                                <input type="text" class="form-control" name="bidder_name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Email</label>
                                <input type="email" class="form-control" name="bidder_email" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Phone Number</label>
                            <input type="tel" class="form-control" name="bidder_phone" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Your Bid Amount (R)</label>
                            <input type="number" class="form-control" name="bid_amount" required step="100" min="0" id="bidAmountInput">
                            <div class="form-text">Your bid must be higher than the current bid</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="submit_bid" class="btn btn-warning">
                            <i class="bi bi-hammer me-2"></i>Place Bid
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Hire Modal -->
    <div class="modal fade" id="hireModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Book Vehicle Hire</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <input type="hidden" name="vehicle_id" id="hireVehicleId">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Full Name</label>
                                <input type="text" class="form-control" name="customer_name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Email Address</label>
                                <input type="email" class="form-control" name="email" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Phone Number</label>
                            <input type="tel" class="form-control" name="phone" required>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Start Date</label>
                                <input type="date" class="form-control" name="start_date" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">End Date</label>
                                <input type="date" class="form-control" name="end_date" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Pickup Location</label>
                            <input type="text" class="form-control" name="pickup_location" required 
                                   placeholder="Enter your preferred pickup location">
                        </div>
                        
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle me-2"></i>
                            <strong>Daily Rate:</strong> Will be calculated based on vehicle selected
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="submit_hire" class="btn btn-success">
                            <i class="bi bi-calendar-check me-2"></i>Submit Booking
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Video Modal -->
    <div class="modal fade" id="videoModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content bg-dark">
                <div class="modal-header border-0">
                    <h5 class="modal-title text-white">Vehicle Video</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body p-0">
                    <video id="modalVideo" controls class="w-100" style="max-height: 70vh;">
                        <source src="" type="video/mp4">
                        Your browser does not support the video tag.
                    </video>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Global Variables
        let currentCategory = '<?= $search_category ?>';
        let currentSubcategory = '<?= $search_subcategory ?>';
        let currentRegion = '<?= $search_region ?>';
        let currentQuery = '<?= $search_query ?>';
        let categoriesData = <?= json_encode($categories_data) ?>;

        // Auto-slide intervals
        let bannerInterval;
        let featuredInterval;
        let recentInterval;
        let boxAdsInterval;

        // Page Initialization
        document.addEventListener('DOMContentLoaded', function() {
            // Hide preloader
            setTimeout(() => {
                const preloader = document.getElementById('preloader');
                if (preloader) {
                    preloader.style.opacity = '0';
                    setTimeout(() => {
                        preloader.style.display = 'none';
                        document.body.style.overflow = 'auto';
                    }, 300);
                }
            }, 800);

            // Initialize components
            initializeNavigation();
            initializeSearchTabs();
            initializeScrollEffects();
            initializeBannerAds();
            
            // Load initial data
            if (currentCategory) {
                loadSubcategories(currentCategory);
                loadMakes(currentCategory);
            }
            
            // Load content based on page type
            const page = '<?= $page ?>';
            const isHomePage = !currentCategory && !currentRegion && !currentQuery && page === 'home';
            
            if (isHomePage) {
                loadFeaturedVehicles();
                loadRecentVehicles();
                initializeBoxAds();
            } else if (page === 'vehicles' || currentCategory || currentRegion || currentQuery) {
                loadVehicles();
            } else if (page === 'auctions') {
                loadAuctions();
            } else if (page === 'hire') {
                loadHireVehicles();
            } else if (page === 'rent-to-own') {
                loadRentToOwnVehicles();
            }

            // Setup event handlers
            setupEventHandlers();
        });

        // Banner Ads Auto-rotation
        function initializeBannerAds() {
            const banners = document.querySelectorAll('.banner-ad');
            if (banners.length <= 1) return;

            let currentBanner = 0;
            
            bannerInterval = setInterval(() => {
                banners[currentBanner].classList.remove('active');
                currentBanner = (currentBanner + 1) % banners.length;
                banners[currentBanner].classList.add('active');
            }, 4000);
        }

        // Box Ads Auto-slide
        function initializeBoxAds() {
            const container = document.getElementById('boxAdsContainer');
            if (!container || container.children.length <= 1) return;

            const isMobile = window.innerWidth < 576;
            const cardsToMove = isMobile ? 1 : 2;
            const minCards = isMobile ? 1 : 2;
            
            if (container.children.length <= minCards) return;

            let currentPosition = 0;
            const cardWidth = container.children[0].offsetWidth + 16; // Including gap
            
            boxAdsInterval = setInterval(() => {
                currentPosition -= cardWidth * cardsToMove;
                if (Math.abs(currentPosition) >= (container.children.length - minCards) * cardWidth) {
                    currentPosition = 0;
                }
                container.style.transform = `translateX(${currentPosition}px)`;
            }, 4000);
        }

        // Navigation Effects
        function initializeNavigation() {
            const navbar = document.querySelector('.navbar');
            
            function updateNavbar() {
                const scrolled = window.pageYOffset;
                
                if (scrolled > 30) {
                    navbar.classList.add('scrolled');
                } else {
                    navbar.classList.remove('scrolled');
                }
            }
            
            window.addEventListener('scroll', updateNavbar, { passive: true });

            // Auto-close mobile menu
            document.querySelectorAll('.nav-link, .dropdown-item').forEach(link => {
                link.addEventListener('click', () => {
                    const navbarCollapse = document.querySelector('.navbar-collapse');
                    if (navbarCollapse.classList.contains('show')) {
                        bootstrap.Collapse.getInstance(navbarCollapse).hide();
                    }
                });
            });
        }

        // Search Tabs
        function initializeSearchTabs() {
            const tabs = document.querySelectorAll('.search-tab');
            const contents = document.querySelectorAll('.search-content');
            
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const targetTab = this.dataset.tab;
                    
                    tabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                    
                    contents.forEach(content => {
                        content.classList.remove('active');
                        if (content.id === targetTab + 'Search') {
                            content.classList.add('active');
                        }
                    });
                }, { passive: true });
            });
        }

        // Scroll Effects
        function initializeScrollEffects() {
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        }

        // Event Handlers
        function setupEventHandlers() {
            // Category change handler
            document.querySelectorAll('select[name="category"]').forEach(select => {
                select.addEventListener('change', function() {
                    currentCategory = this.value;
                    if (this.id === 'categorySelect') {
                        loadSubcategories(currentCategory);
                        loadMakes(currentCategory);
                    }
                }, { passive: true });
            });

            // Form submit handler
            const searchForm = document.getElementById('searchForm');
            if (searchForm) {
                searchForm.addEventListener('submit', function(e) {
                    if ('<?= $page ?>' === 'home') {
                        const formData = new FormData(this);
                        const params = new URLSearchParams();
                        params.append('page', 'vehicles');
                        
                        for (const [key, value] of formData.entries()) {
                            if (value && key !== 'page') {
                                params.append(key, value);
                            }
                        }
                        
                        window.location.href = '?' + params.toString();
                        e.preventDefault();
                    }
                });
            }
        }

        // Load Functions
        async function loadSubcategories(category) {
            const subcategorySelect = document.getElementById('subcategorySelect');
            if (!subcategorySelect) return;

            if (!category) {
                subcategorySelect.innerHTML = '<option value="">All Types</option>';
                return;
            }

            try {
                const response = await fetch(`?ajax=subcategories&category=${category}`);
                const subcategories = await response.json();
                
                let options = '<option value="">All Types</option>';
                for (const [key, name] of Object.entries(subcategories)) {
                    const selected = key === currentSubcategory ? 'selected' : '';
                    options += `<option value="${key}" ${selected}>${name}</option>`;
                }
                
                subcategorySelect.innerHTML = options;
            } catch (error) {
                console.error('Error loading subcategories:', error);
            }
        }

        async function loadMakes(category) {
            const makeSelect = document.getElementById('makeSelect');
            if (!makeSelect) return;

            if (!category) {
                makeSelect.innerHTML = '<option value="">All Makes</option>';
                return;
            }

            try {
                const response = await fetch(`?ajax=makes&category=${category}`);
                const makes = await response.json();
                
                let options = '<option value="">All Makes</option>';
                makes.forEach(make => {
                    options += `<option value="${make}">${make}</option>`;
                });
                
                makeSelect.innerHTML = options;
            } catch (error) {
                console.error('Error loading makes:', error);
            }
        }

        // EXACTLY 4 CARDS SLIDING - REWRITTEN FOR PRECISION
        function startAutoSlide(containerId, interval) {
            const container = document.getElementById(containerId);
            if (!container) return;

            const cards = Array.from(container.children);
            const isPC = window.innerWidth >= 992;
            const isTablet = window.innerWidth >= 768 && window.innerWidth < 992;
            
            let cardsToMove, visibleCards;
            
            if (isPC) {
                cardsToMove = 4; // EXACTLY 4 cards move on PC
                visibleCards = 4; // EXACTLY 4 cards visible on PC
            } else if (isTablet) {
                cardsToMove = 3; // 3 cards move on tablet
                visibleCards = 3; // 3 cards visible on tablet
            } else {
                cardsToMove = 2; // 2 cards move on mobile
                visibleCards = 2; // 2 cards visible on mobile
            }
            
            if (cards.length <= visibleCards) return;

            let currentIndex = 0;
            
            featuredInterval = setInterval(() => {
                currentIndex += cardsToMove;
                
                // Reset if we go past the available cards
                if (currentIndex >= cards.length) {
                    currentIndex = 0;
                }
                
                // Calculate exact position for card width
                const containerWidth = container.parentElement.offsetWidth;
                const cardWidth = containerWidth / visibleCards;
                const newPosition = -(currentIndex * cardWidth);
                
                container.style.transform = `translateX(${newPosition}px)`;
                
                console.log(`Featured: Moving ${cardsToMove} cards, showing cards ${currentIndex} to ${currentIndex + visibleCards - 1}`);
            }, interval);
        }

        function startDualAutoSlide(containerId, interval) {
            const container = document.getElementById(containerId);
            if (!container) return;

            const cards = Array.from(container.children);
            const isPC = window.innerWidth >= 992;
            const isTablet = window.innerWidth >= 768 && window.innerWidth < 992;
            
            let cardsToMove, visibleCards;
            
            if (isPC) {
                cardsToMove = 4; // EXACTLY 4 cards move on PC
                visibleCards = 4; // EXACTLY 4 cards visible on PC
            } else if (isTablet) {
                cardsToMove = 3; // 3 cards move on tablet
                visibleCards = 3; // 3 cards visible on tablet
            } else {
                cardsToMove = 2; // 2 cards move on mobile
                visibleCards = 2; // 2 cards visible on mobile
            }
            
            if (cards.length <= visibleCards) return;

            let currentIndex = 0;
            
            recentInterval = setInterval(() => {
                currentIndex += cardsToMove;
                
                // Reset if we go past the available cards
                if (currentIndex >= cards.length) {
                    currentIndex = 0;
                }
                
                // Calculate exact position for card width
                const containerWidth = container.parentElement.offsetWidth;
                const cardWidth = containerWidth / visibleCards;
                const newPosition = -(currentIndex * cardWidth);
                
                container.style.transform = `translateX(${newPosition}px)`;
                
                console.log(`Recent: Moving ${cardsToMove} cards, showing cards ${currentIndex} to ${currentIndex + visibleCards - 1}`);
            }, interval);
        }

        async function loadFeaturedVehicles() {
            const container = document.getElementById('featuredContainer');
            if (!container) return;

            try {
                const response = await fetch('?ajax=vehicles&type=featured');
                const data = await response.json();
                
                if (data.vehicles && data.vehicles.length > 0) {
                    container.innerHTML = data.vehicles.map(vehicle => createSimpleVehicleCard(vehicle)).join('');
                    
                    // ALWAYS START SLIDING IF WE HAVE MORE THAN 4 CARDS
                    console.log(`Featured: Loaded ${data.vehicles.length} vehicles`);
                    
                    if (data.vehicles.length > 4) {
                        console.log('Featured: Starting auto-slide with 4-card movement');
                        startAutoSlide('featuredContainer', 4000);
                    }
                } else {
                    container.innerHTML = `
                        <div class="text-center py-4 w-100">
                            <i class="bi bi-star fs-1 text-muted mb-2"></i>
                            <h5 class="text-muted mb-2">No featured vehicles available</h5>
                            <p class="text-muted small">Featured listings will appear here</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Error loading featured vehicles:', error);
                container.innerHTML = `
                    <div class="text-center py-4 w-100">
                        <i class="bi bi-exclamation-triangle fs-1 text-danger mb-2"></i>
                        <h5 class="text-muted mb-2">Error loading featured vehicles</h5>
                        <button class="btn btn-danger btn-sm" onclick="loadFeaturedVehicles()">Try Again</button>
                    </div>
                `;
            }
        }

        async function loadRecentVehicles() {
            const container = document.getElementById('recentContainer');
            if (!container) return;

            try {
                const response = await fetch('?ajax=vehicles&type=recent');
                const data = await response.json();
                
                if (data.vehicles && data.vehicles.length > 0) {
                    container.innerHTML = data.vehicles.map(vehicle => createSimpleVehicleCard(vehicle)).join('');
                    
                    // ALWAYS START SLIDING IF WE HAVE MORE THAN 4 CARDS
                    console.log(`Recent: Loaded ${data.vehicles.length} vehicles`);
                    
                    if (data.vehicles.length > 4) {
                        console.log('Recent: Starting auto-slide with 4-card movement');
                        startDualAutoSlide('recentContainer', 4000);
                    }
                } else {
                    container.innerHTML = `
                        <div class="text-center py-4 w-100">
                            <i class="bi bi-clock-history fs-1 text-muted mb-2"></i>
                            <h5 class="text-muted mb-2">No recent vehicles available</h5>
                            <p class="text-muted small">New listings will appear here</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Error loading recent vehicles:', error);
                container.innerHTML = `
                    <div class="text-center py-4 w-100">
                        <i class="bi bi-exclamation-triangle fs-1 text-danger mb-2"></i>
                        <h5 class="text-muted mb-2">Error loading recent vehicles</h5>
                        <button class="btn btn-danger btn-sm" onclick="loadRecentVehicles()">Try Again</button>
                    </div>
                `;
            }
        }

        async function loadVehicles() {
            const grid = document.getElementById('vehicleGrid');
            const countElement = document.getElementById('vehicleCount');
            
            if (!grid) return;
            
            grid.innerHTML = `
                <div class="loading">
                    <div class="loading-spinner"></div>
                    <p class="mt-2">Loading vehicles...</p>
                </div>
            `;

            try {
                const urlParams = new URLSearchParams(window.location.search);
                const params = new URLSearchParams();
                params.append('ajax', 'vehicles');
                
                ['category', 'subcategory', 'region', 'query', 'make', 'min_price', 'max_price', 'min_year', 'max_year', 'listing_type'].forEach(key => {
                    const value = urlParams.get(key);
                    if (value) params.append(key, value);
                });

                const response = await fetch(`?${params.toString()}`);
                const data = await response.json();
                
                if (data.vehicles && data.vehicles.length > 0) {
                    grid.innerHTML = data.vehicles.map(vehicle => createGridVehicleCard(vehicle)).join('');
                    if (countElement) {
                        countElement.textContent = `${data.count} vehicles found`;
                    }
                    
                    setTimeout(() => {
                        grid.querySelectorAll('.vehicle-card').forEach((card, index) => {
                            setTimeout(() => {
                                card.style.opacity = '1';
                                card.style.transform = 'translateY(0)';
                            }, index * 100);
                        });
                    }, 100);
                } else {
                    grid.innerHTML = `
                        <div class="col-12">
                            <div class="text-center py-5">
                                <i class="bi bi-search fs-1 text-muted mb-3"></i>
                                <h4 class="text-muted mb-3">No vehicles found</h4>
                                <p class="text-muted mb-4">Try adjusting your search criteria</p>
                                <a href="?" class="btn btn-danger">Clear Search</a>
                            </div>
                        </div>
                    `;
                    if (countElement) {
                        countElement.textContent = '0 vehicles found';
                    }
                }
            } catch (error) {
                console.error('Error loading vehicles:', error);
                grid.innerHTML = `
                    <div class="col-12">
                        <div class="text-center py-5">
                            <i class="bi bi-exclamation-triangle fs-1 text-danger mb-3"></i>
                            <h4 class="text-muted mb-3">Error loading vehicles</h4>
                            <button class="btn btn-danger" onclick="loadVehicles()">Try Again</button>
                        </div>
                    </div>
                `;
            }
        }

        async function loadAuctions() {
            const grid = document.getElementById('auctionGrid');
            if (!grid) return;
            
            grid.innerHTML = `
                <div class="loading">
                    <div class="loading-spinner"></div>
                    <p class="mt-2">Loading auctions...</p>
                </div>
            `;

            try {
                const response = await fetch('?ajax=vehicles&type=auctions');
                const data = await response.json();
                
                if (data.vehicles && data.vehicles.length > 0) {
                    grid.innerHTML = data.vehicles.map(vehicle => createAuctionCard(vehicle)).join('');
                    
                    // Start auction timers
                    updateAuctionTimers();
                    setInterval(updateAuctionTimers, 60000); // Update every minute
                } else {
                    grid.innerHTML = `
                        <div class="col-12">
                            <div class="text-center py-5">
                                <i class="bi bi-hammer fs-1 text-muted mb-3"></i>
                                <h4 class="text-muted mb-3">No active auctions</h4>
                                <p class="text-muted">Check back later for new auctions</p>
                            </div>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Error loading auctions:', error);
                grid.innerHTML = `
                    <div class="col-12">
                        <div class="text-center py-5">
                            <i class="bi bi-exclamation-triangle fs-1 text-danger mb-3"></i>
                            <h4 class="text-muted mb-3">Error loading auctions</h4>
                            <button class="btn btn-danger" onclick="loadAuctions()">Try Again</button>
                        </div>
                    </div>
                `;
            }
        }

        async function loadHireVehicles() {
            const grid = document.getElementById('hireGrid');
            if (!grid) return;
            
            grid.innerHTML = `
                <div class="loading">
                    <div class="loading-spinner"></div>
                    <p class="mt-2">Loading hire vehicles...</p>
                </div>
            `;

            try {
                const response = await fetch('?ajax=vehicles&type=hire');
                const data = await response.json();
                
                if (data.vehicles && data.vehicles.length > 0) {
                    grid.innerHTML = data.vehicles.map(vehicle => createHireCard(vehicle)).join('');
                } else {
                    grid.innerHTML = `
                        <div class="col-12">
                            <div class="text-center py-5">
                                <i class="bi bi-calendar-check fs-1 text-muted mb-3"></i>
                                <h4 class="text-muted mb-3">No vehicles available for hire</h4>
                                <p class="text-muted">Check back later for hire options</p>
                            </div>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Error loading hire vehicles:', error);
                grid.innerHTML = `
                    <div class="col-12">
                        <div class="text-center py-5">
                            <i class="bi bi-exclamation-triangle fs-1 text-danger mb-3"></i>
                            <h4 class="text-muted mb-3">Error loading hire vehicles</h4>
                            <button class="btn btn-danger" onclick="loadHireVehicles()">Try Again</button>
                        </div>
                    </div>
                `;
            }
        }

        async function loadRentToOwnVehicles() {
            const grid = document.getElementById('rentToOwnGrid');
            if (!grid) return;
            
            grid.innerHTML = `
                <div class="loading">
                    <div class="loading-spinner"></div>
                    <p class="mt-2">Loading rent-to-own vehicles...</p>
                </div>
            `;

            try {
                const response = await fetch('?ajax=vehicles&listing_type=rent-to-own');
                const data = await response.json();
                
                if (data.vehicles && data.vehicles.length > 0) {
                    grid.innerHTML = data.vehicles.map(vehicle => createGridVehicleCard(vehicle)).join('');
                } else {
                    grid.innerHTML = `
                        <div class="col-12">
                            <div class="text-center py-5">
                                <i class="bi bi-arrow-repeat fs-1 text-muted mb-3"></i>
                                <h4 class="text-muted mb-3">No rent-to-own vehicles available</h4>
                                <p class="text-muted">Check back later for flexible ownership options</p>
                            </div>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Error loading rent-to-own vehicles:', error);
                grid.innerHTML = `
                    <div class="col-12">
                        <div class="text-center py-5">
                            <i class="bi bi-exclamation-triangle fs-1 text-danger mb-3"></i>
                            <h4 class="text-muted mb-3">Error loading rent-to-own vehicles</h4>
                            <button class="btn btn-danger" onclick="loadRentToOwnVehicles()">Try Again</button>
                        </div>
                    </div>
                `;
            }
        }

        // EXACT VEHICLE CARD FORMAT AS REQUESTED
        function createSimpleVehicleCard(vehicle) {
            const dealerName = vehicle.company_name || vehicle.username;
            
            return `
                <div class="vehicle-card" onclick="window.location.href='?vehicle_id=${vehicle.vehicle_id}'" style="cursor: pointer;">
                    <div class="vehicle-image">
                        <img src="${vehicle.primary_image || 'https://via.placeholder.com/300x200/f0f0f0/666666?text=No+Image'}" 
                             alt="${vehicle.make} ${vehicle.model}"
                             onerror="this.src='https://via.placeholder.com/300x200/f0f0f0/666666?text=No+Image'"
                             loading="lazy">
                    </div>
                    <div class="vehicle-content">
                        <div class="vehicle-badge featured-badge">Featured</div>
                        <h3 class="vehicle-title">${vehicle.make} ${vehicle.model}</h3>
                        <div class="dealer-name-display">${dealerName}</div>
                        <a href="?vehicle_id=${vehicle.vehicle_id}" class="btn-view-details-full">
                            View Details
                        </a>
                    </div>
                </div>
            `;
        }

        function createGridVehicleCard(vehicle) {
            const badge = getBadgeForVehicle(vehicle);
            const dealerInitials = (vehicle.company_name || vehicle.username).substring(0, 2).toUpperCase();
            const imageCount = vehicle.image_count || 0;
            
            return `
                <div class="col-12 col-md-6 col-lg-4">
                    <div class="vehicle-card" style="opacity: 0; transform: translateY(30px); transition: all 0.6s ease;" onclick="window.location.href='?vehicle_id=${vehicle.vehicle_id}'" style="cursor: pointer;">
                        <div class="vehicle-image">
                            <img src="${vehicle.primary_image || 'https://via.placeholder.com/400x300/f0f0f0/666666?text=No+Image'}" 
                                 alt="${vehicle.make} ${vehicle.model}"
                                 onerror="this.src='https://via.placeholder.com/400x300/f0f0f0/666666?text=No+Image'"
                                 loading="lazy">
                            ${badge ? badge : ''}
                            ${imageCount > 0 ? `<div class="vehicle-image-count"><i class="bi bi-camera"></i> ${imageCount}</div>` : ''}
                            <div class="vehicle-price-overlay">
                                ${getPriceDisplay(vehicle)}
                            </div>
                        </div>
                        <div class="vehicle-content">
                            <h3 class="vehicle-title">${vehicle.make} ${vehicle.model}</h3>
                            <div class="vehicle-details">
                                ${vehicle.year ? `
                                <div class="vehicle-detail">
                                    <i class="bi bi-calendar"></i>
                                    <span>${vehicle.year}</span>
                                </div>` : ''}
                                ${vehicle.mileage ? `
                                <div class="vehicle-detail">
                                    <i class="bi bi-speedometer2"></i>
                                    <span>${new Intl.NumberFormat().format(vehicle.mileage)} km</span>
                                </div>` : ''}
                                <div class="vehicle-detail">
                                    <i class="bi bi-geo-alt"></i>
                                    <span>${vehicle.city}</span>
                                </div>
                            </div>
                            <div class="vehicle-features">
                                ${vehicle.transmission ? `<span class="vehicle-feature">${vehicle.transmission}</span>` : ''}
                                ${vehicle.fuel_type ? `<span class="vehicle-feature">${vehicle.fuel_type}</span>` : ''}
                                ${vehicle.no_accidents ? `<span class="vehicle-feature">No Accidents</span>` : ''}
                                ${vehicle.warranty ? `<span class="vehicle-feature">Warranty</span>` : ''}
                            </div>
                            <div class="vehicle-footer">
                                <div class="dealer-info">
                                    <div class="dealer-avatar">${dealerInitials}</div>
                                    <div>
                                        <div class="dealer-name">${vehicle.company_name || vehicle.username}</div>
                                        <div class="dealer-verified">
                                            <i class="bi bi-patch-check-fill"></i>
                                            Verified
                                        </div>
                                    </div>
                                </div>
                                <div class="btn-view-details">
                                    <i class="bi bi-eye"></i>
                                    View Details
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function createAuctionCard(vehicle) {
            const dealerInitials = (vehicle.company_name || vehicle.username).substring(0, 2).toUpperCase();
            const endTime = new Date(vehicle.auction_end_date);
            const now = new Date();
            const timeLeft = Math.max(0, Math.floor((endTime - now) / 1000));
            
            return `
                <div class="col-12 col-md-6 col-lg-4">
                    <div class="vehicle-card auction-card" onclick="window.location.href='?vehicle_id=${vehicle.vehicle_id}'">
                        <div class="vehicle-image">
                            <img src="${vehicle.primary_image || 'https://via.placeholder.com/400x300/f0f0f0/666666?text=No+Image'}" 
                                 alt="${vehicle.make} ${vehicle.model}"
                                 onerror="this.src='https://via.placeholder.com/400x300/f0f0f0/666666?text=No+Image'"
                                 loading="lazy">
                            <div class="vehicle-badge auction-badge">AUCTION</div>
                        </div>
                        <div class="auction-timer" data-end-time="${vehicle.auction_end_date}">
                            ${formatTimeLeft(timeLeft)}
                        </div>
                        <div class="vehicle-content">
                            <h3 class="vehicle-title">${vehicle.make} ${vehicle.model} (${vehicle.year})</h3>
                            <div class="vehicle-detail mb-2">
                                <i class="bi bi-geo-alt"></i>
                                <span>${vehicle.city}</span>
                            </div>
                            <div class="bid-info">
                                <div>
                                    <div class="small text-muted">Current Bid</div>
                                    <div class="current-bid">R ${new Intl.NumberFormat().format(vehicle.current_bid || 0)}</div>
                                </div>
                                <button class="btn-bid" onclick="event.stopPropagation(); showBidModal(${vehicle.vehicle_id}, ${vehicle.current_bid || 0})">
                                    <i class="bi bi-hammer"></i> Bid
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function createHireCard(vehicle) {
            const dealerInitials = (vehicle.company_name || vehicle.username).substring(0, 2).toUpperCase();
            
            return `
                <div class="col-12 col-md-6 col-lg-4">
                    <div class="vehicle-card" onclick="window.location.href='?vehicle_id=${vehicle.vehicle_id}'">
                        <div class="vehicle-image">
                            <img src="${vehicle.primary_image || 'https://via.placeholder.com/400x300/f0f0f0/666666?text=No+Image'}" 
                                 alt="${vehicle.make} ${vehicle.model}"
                                 onerror="this.src='https://via.placeholder.com/400x300/f0f0f0/666666?text=No+Image'"
                                 loading="lazy">
                            <div class="vehicle-badge hire-badge">FOR HIRE</div>
                            <div class="vehicle-price-overlay">
                                R ${new Intl.NumberFormat().format(vehicle.daily_rate || 0)}/day
                            </div>
                        </div>
                        <div class="vehicle-content">
                            <h3 class="vehicle-title">${vehicle.make} ${vehicle.model} (${vehicle.year})</h3>
                            <div class="vehicle-details">
                                <div class="vehicle-detail">
                                    <i class="bi bi-calendar"></i>
                                    <span>${vehicle.year}</span>
                                </div>
                                <div class="vehicle-detail">
                                    <i class="bi bi-geo-alt"></i>
                                    <span>${vehicle.city}</span>
                                </div>
                            </div>
                            <div class="bid-info">
                                <div>
                                    <div class="small text-muted">Daily Rate</div>
                                    <div class="current-bid">R ${new Intl.NumberFormat().format(vehicle.daily_rate || 0)}</div>
                                </div>
                                <button class="btn btn-success btn-sm" onclick="event.stopPropagation(); showHireModal(${vehicle.vehicle_id})">
                                    <i class="bi bi-calendar-check"></i> Book
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // Helper Functions
        function getBadgeForVehicle(vehicle) {
            if (vehicle.featured) {
                return '<div class="vehicle-badge">Featured</div>';
            } else if (vehicle.listing_type === 'auction') {
                return '<div class="vehicle-badge auction-badge">Auction</div>';
            } else if (vehicle.listing_type === 'hire') {
                return '<div class="vehicle-badge hire-badge">For Hire</div>';
            } else if (vehicle.listing_type === 'rent-to-own') {
                return '<div class="vehicle-badge rent-to-own-badge">Rent-to-Own</div>';
            }
            return '';
        }

        function getPriceDisplay(vehicle) {
            if (vehicle.listing_type === 'auction') {
                return `Current: R ${new Intl.NumberFormat().format(vehicle.current_bid || 0)}`;
            } else if (vehicle.listing_type === 'hire') {
                return `From R ${new Intl.NumberFormat().format(vehicle.daily_rate || 0)}/day`;
            } else {
                return `R ${new Intl.NumberFormat().format(vehicle.price)}`;
            }
        }

        function formatTimeLeft(seconds) {
            if (seconds <= 0) return 'Auction Ended';
            
            const days = Math.floor(seconds / 86400);
            const hours = Math.floor((seconds % 86400) / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            
            if (days > 0) {
                return `${days}d ${hours}h ${minutes}m`;
            } else if (hours > 0) {
                return `${hours}h ${minutes}m`;
            } else {
                return `${minutes}m`;
            }
        }

        function updateAuctionTimers() {
            document.querySelectorAll('.auction-timer').forEach(timer => {
                const endTime = new Date(timer.dataset.endTime);
                const now = new Date();
                const timeLeft = Math.max(0, Math.floor((endTime - now) / 1000));
                timer.textContent = formatTimeLeft(timeLeft);
                
                if (timeLeft === 0) {
                    timer.style.background = '#fee2e2';
                    timer.style.color = '#991b1b';
                }
            });
        }

        // Modal Functions
        function showBidModal(vehicleId, currentBid) {
            document.getElementById('bidVehicleId').value = vehicleId;
            document.getElementById('currentBidAmount').textContent = new Intl.NumberFormat().format(currentBid);
            document.getElementById('bidAmountInput').min = currentBid + 100;
            document.getElementById('bidAmountInput').placeholder = `Minimum: R ${new Intl.NumberFormat().format(currentBid + 100)}`;
            
            const bidModal = new bootstrap.Modal(document.getElementById('bidModal'));
            bidModal.show();
        }

        function showHireModal(vehicleId) {
            document.getElementById('hireVehicleId').value = vehicleId;
            
            // Set minimum dates
            const today = new Date().toISOString().split('T')[0];
            document.querySelector('#hireModal input[name="start_date"]').min = today;
            document.querySelector('#hireModal input[name="end_date"]').min = today;
            
            const hireModal = new bootstrap.Modal(document.getElementById('hireModal'));
            hireModal.show();
        }

        // Video Modal Function
        function playVideo(videoPath) {
            const videoModal = new bootstrap.Modal(document.getElementById('videoModal'));
            const modalVideo = document.getElementById('modalVideo');
            
            modalVideo.src = videoPath;
            videoModal.show();
            
            // Pause video when modal is hidden
            document.getElementById('videoModal').addEventListener('hidden.bs.modal', function () {
                modalVideo.pause();
                modalVideo.src = '';
            });
        }

        // Horizontal Scroll Functions
        function scrollSection(sectionId, direction) {
            const section = document.getElementById(sectionId);
            if (!section) return;
            
            const isMobile = window.innerWidth < 576;
            const scrollAmount = isMobile ? 200 : (window.innerWidth < 768 ? 250 : 300);
            const currentScroll = section.scrollLeft;
            
            if (direction === 'left') {
                section.scrollTo({
                    left: currentScroll - scrollAmount,
                    behavior: 'smooth'
                });
            } else {
                section.scrollTo({
                    left: currentScroll + scrollAmount,
                    behavior: 'smooth'
                });
            }
        }

        // Cleanup intervals on page unload
        window.addEventListener('beforeunload', function() {
            if (bannerInterval) clearInterval(bannerInterval);
            if (featuredInterval) clearInterval(featuredInterval);
            if (recentInterval) clearInterval(recentInterval);
            if (boxAdsInterval) clearInterval(boxAdsInterval);
        });

        console.log('🚛 TrucksONSale Enhanced with Premium Ads System loaded!');
        console.log('✅ 200px Banner Ads (150px mobile) - DRAMATICALLY INCREASED SIZE');
        console.log(' Premium Ads with gradient placeholder and larger text');
        console.log('✅ COMPLETELY REWRITTEN SLIDING: 4 cards move at once on PC, 2 on mobile');
        console.log('✅ Featured vehicles: MOVES 4 CARDS AT ONCE on PC, 2 on mobile');
        console.log('✅ Recent vehicles: MOVES 4 CARDS AT ONCE on PC, 2 on mobile');
        console.log('✅ Console logging shows card movement for debugging');
        console.log('✅ Banner ads fetch from premium_ads table correctly');
        console.log(' Mobile responsive: 150px banner on mobile, 120px on small screens');
        console.log(' Page headers adjusted for larger banner size');
    </script>
</body>
</html>