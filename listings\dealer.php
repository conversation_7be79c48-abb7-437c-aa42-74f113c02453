<?php
// Fix headers already sent by using output buffering
ob_start();

session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if user is logged in and is a dealer
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] != 'dealer') {
    ob_end_clean();
    header("Location: login.php");
    exit;
}

// Database Configuration
$host = 'localhost';
$db_name = 'truc_tos';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$db_name;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Database Connection Error: " . $e->getMessage());
}

// CREATE REQUIRED TABLES IF THEY DON'T EXIST
try {
    // 1. Premium Ads Table for Admin
    $pdo->exec("CREATE TABLE IF NOT EXISTS premium_ads (
        id INT PRIMARY KEY AUTO_INCREMENT,
        title VARCHAR(255) NOT NULL,
        image_url VARCHAR(500) NOT NULL,
        link_url VARCHAR(500),
        ad_type ENUM('banner', 'box') NOT NULL,
        status ENUM('active', 'inactive') DEFAULT 'active',
        display_order INT DEFAULT 0,
        created_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_ad_type (ad_type),
        INDEX idx_status (status),
        INDEX idx_display_order (display_order)
    )");

    // 2. Dealership Limits Table
    $pdo->exec("CREATE TABLE IF NOT EXISTS dealership_limits (
        id INT PRIMARY KEY AUTO_INCREMENT,
        dealership_id INT NOT NULL,
        featured_limit INT DEFAULT 2,
        current_featured_count INT DEFAULT 0,
        updated_by INT,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_dealership (dealership_id),
        INDEX idx_dealership (dealership_id)
    )");

    // 3. Add featured fields to vehicles table if they don't exist
    $pdo->exec("ALTER TABLE vehicles 
        ADD COLUMN IF NOT EXISTS featured BOOLEAN DEFAULT FALSE,
        ADD COLUMN IF NOT EXISTS featured_until DATETIME NULL,
        ADD COLUMN IF NOT EXISTS premium_listing BOOLEAN DEFAULT FALSE,
        ADD COLUMN IF NOT EXISTS premium_until DATETIME NULL,
        ADD COLUMN IF NOT EXISTS display_order INT DEFAULT 0,
        ADD COLUMN IF NOT EXISTS vin_number VARCHAR(50) NULL,
        ADD COLUMN IF NOT EXISTS registration_number VARCHAR(50) NULL");

    // 4. Enhanced Categories for Premium Ads
    $pdo->exec("ALTER TABLE categories 
        ADD COLUMN IF NOT EXISTS is_premium BOOLEAN DEFAULT FALSE");

    // 5. Insert default premium categories if they don't exist
    $stmt = $pdo->prepare("INSERT IGNORE INTO categories (category_key, category_name, is_premium, status) VALUES 
        ('premium-ads', 'Premium Ads', 1, 'active'),
        ('banner-ads', 'Banner Ads', 1, 'active'),
        ('box-ads', 'Box Ads', 1, 'active')");
    $stmt->execute();

    // 6. Create dealer sales team table if not exists
    $pdo->exec("CREATE TABLE IF NOT EXISTS dealer_sales_team (
        id INT PRIMARY KEY AUTO_INCREMENT,
        dealer_id INT NOT NULL,
        name VARCHAR(255) NOT NULL,
        position VARCHAR(255),
        phone VARCHAR(50),
        email VARCHAR(255),
        whatsapp VARCHAR(50),
        photo VARCHAR(500),
        facebook_url VARCHAR(500),
        twitter_url VARCHAR(500),
        instagram_url VARCHAR(500),
        linkedin_url VARCHAR(500),
        youtube_url VARCHAR(500),
        tiktok_url VARCHAR(500),
        website_url VARCHAR(500),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_dealer (dealer_id)
    )");

} catch(PDOException $e) {
    error_log("Database setup error: " . $e->getMessage());
}

// Enhanced Helper Functions

// Enhanced file upload with size limits
function uploadFile($file, $upload_dir = 'uploads', $max_size = null) {
    if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
        return false;
    }
    
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    
    // Set size limits based on file type
    $size_limits = [
        'image' => 2 * 1024 * 1024, // 2MB for images
        'video' => 50 * 1024 * 1024, // 50MB for videos
        'document' => 10 * 1024 * 1024 // 10MB for documents
    ];
    
    $allowed_types = [
        'image' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
        'video' => ['mp4', 'avi', 'mov', 'wmv', 'flv'],
        'document' => ['pdf'] // PDF only
    ];
    
    $file_type = '';
    foreach ($allowed_types as $type => $extensions) {
        if (in_array($file_extension, $extensions)) {
            $file_type = $type;
            break;
        }
    }
    
    if (empty($file_type)) {
        return false;
    }
    
    // Check file size
    $max_allowed = $max_size ?: $size_limits[$file_type];
    if ($file['size'] > $max_allowed) {
        return false;
    }
    
    // Generate unique filename
    $unique_name = uniqid() . '_' . preg_replace('/[^a-zA-Z0-9._-]/', '', $file['name']);
    $upload_path = $upload_dir . '/' . $unique_name;
    
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    if (move_uploaded_file($file['tmp_name'], $upload_path)) {
        return [
            'path' => $upload_path,
            'name' => $file['name'],
            'type' => $file_type,
            'size' => $file['size']
        ];
    }
    
    return false;
}

// FIXED: Get dealer's featured listing limit with real-time count
function getDealerFeaturedLimit($pdo, $dealer_id) {
    try {
        // Get the limit setting
        $stmt = $pdo->prepare("SELECT featured_limit FROM dealership_limits WHERE dealership_id = ?");
        $stmt->execute([$dealer_id]);
        $limit_row = $stmt->fetch();
        
        // If no limit exists, create default
        if (!$limit_row) {
            $stmt = $pdo->prepare("INSERT INTO dealership_limits (dealership_id, featured_limit, current_featured_count) VALUES (?, 2, 0)");
            $stmt->execute([$dealer_id]);
            $featured_limit = 2;
        } else {
            $featured_limit = $limit_row['featured_limit'];
        }
        
        // CRITICAL: Get REAL-TIME count of currently active featured listings
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count 
            FROM vehicles 
            WHERE dealer_id = ? 
            AND featured = 1 
            AND (featured_until IS NULL OR featured_until > NOW())
            AND status != 'deleted'
        ");
        $stmt->execute([$dealer_id]);
        $current_count = (int)$stmt->fetchColumn();
        
        // Update the stored count to match reality
        $stmt = $pdo->prepare("
            UPDATE dealership_limits 
            SET current_featured_count = ?, updated_at = NOW() 
            WHERE dealership_id = ?
        ");
        $stmt->execute([$current_count, $dealer_id]);
        
        return [
            'limit' => (int)$featured_limit, 
            'current' => $current_count
        ];
        
    } catch(PDOException $e) {
        error_log("Error getting dealer featured limit: " . $e->getMessage());
        return ['limit' => 2, 'current' => 0]; // Default fallback
    }
}

// FIXED: Update dealer's featured count with real-time accuracy
function updateDealerFeaturedCount($pdo, $dealer_id) {
    try {
        // Count ONLY currently active featured listings
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count 
            FROM vehicles 
            WHERE dealer_id = ? 
            AND featured = 1 
            AND (featured_until IS NULL OR featured_until > NOW())
            AND status != 'deleted'
        ");
        $stmt->execute([$dealer_id]);
        $current_count = (int)$stmt->fetchColumn();
        
        // Update or insert the current count
        $stmt = $pdo->prepare("
            INSERT INTO dealership_limits (dealership_id, current_featured_count, featured_limit, updated_at) 
            VALUES (?, ?, 2, NOW()) 
            ON DUPLICATE KEY UPDATE 
            current_featured_count = VALUES(current_featured_count),
            updated_at = NOW()
        ");
        $stmt->execute([$dealer_id, $current_count]);
        
        return $current_count;
    } catch(PDOException $e) {
        error_log("Error updating dealer featured count: " . $e->getMessage());
        return 0;
    }
}

// FIXED: Real-time featured count check
function canAddFeaturedListing($pdo, $dealer_id, $exclude_vehicle_id = null) {
    try {
        // Get current limit
        $stmt = $pdo->prepare("SELECT featured_limit FROM dealership_limits WHERE dealership_id = ?");
        $stmt->execute([$dealer_id]);
        $limit_row = $stmt->fetch();
        $featured_limit = $limit_row ? (int)$limit_row['featured_limit'] : 2;
        
        // -1 means unlimited
        if ($featured_limit === -1) {
            return ['can_add' => true, 'current' => 0, 'limit' => -1];
        }
        
        // Count current active featured listings (excluding the one being edited if applicable)
        $sql = "
            SELECT COUNT(*) as count 
            FROM vehicles 
            WHERE dealer_id = ? 
            AND featured = 1 
            AND (featured_until IS NULL OR featured_until > NOW())
            AND status != 'deleted'
        ";
        $params = [$dealer_id];
        
        if ($exclude_vehicle_id) {
            $sql .= " AND vehicle_id != ?";
            $params[] = $exclude_vehicle_id;
        }
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $current_count = (int)$stmt->fetchColumn();
        
        $can_add = $current_count < $featured_limit;
        
        return [
            'can_add' => $can_add,
            'current' => $current_count,
            'limit' => $featured_limit
        ];
        
    } catch(PDOException $e) {
        error_log("Error checking featured limit: " . $e->getMessage());
        return ['can_add' => false, 'current' => 0, 'limit' => 2];
    }
}

// WORKING AJAX Handlers
if (isset($_GET['ajax'])) {
    header('Content-Type: application/json');
    
    switch ($_GET['ajax']) {
   
            
        case 'get_makes':
            $category_id = (int)($_GET['category_id'] ?? 0);
            try {
                $stmt = $pdo->prepare("
                    SELECT make_id, make_name 
                    FROM category_makes 
                    WHERE category_id = ? AND status = 'active' 
                    ORDER BY make_name
                ");
                $stmt->execute([$category_id]);
                $makes = $stmt->fetchAll(PDO::FETCH_ASSOC);
                echo json_encode(['success' => true, 'data' => $makes]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'error' => $e->getMessage()]);
            }
            exit;
            
        case 'get_models':
            $make_id = (int)($_GET['make_id'] ?? 0);
            try {
                $stmt = $pdo->prepare("
                    SELECT model_id, model_name 
                    FROM category_models 
                    WHERE make_id = ? AND status = 'active' 
                    ORDER BY model_name
                ");
                $stmt->execute([$make_id]);
                $models = $stmt->fetchAll(PDO::FETCH_ASSOC);
                echo json_encode(['success' => true, 'data' => $models]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'error' => $e->getMessage()]);
            }
            exit;
            
        case 'get_variants':
            $model_id = (int)($_GET['model_id'] ?? 0);
            try {
                $stmt = $pdo->prepare("
                    SELECT variant_id, variant_name 
                    FROM category_variants 
                    WHERE model_id = ? AND status = 'active' 
                    ORDER BY variant_name
                ");
                $stmt->execute([$model_id]);
                $variants = $stmt->fetchAll(PDO::FETCH_ASSOC);
                echo json_encode(['success' => true, 'data' => $variants]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'error' => $e->getMessage()]);
            }
            exit;
            
        case 'get_category_info':
            $category_id = (int)($_GET['category_id'] ?? 0);
            try {
                $stmt = $pdo->prepare("
                    SELECT show_hours, mileage_label, engine_label, show_transmission, show_fuel_type, show_year 
                    FROM categories 
                    WHERE category_id = ?
                ");
                $stmt->execute([$category_id]);
                $category_info = $stmt->fetch(PDO::FETCH_ASSOC);
                echo json_encode(['success' => true, 'data' => $category_info]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'error' => $e->getMessage()]);
            }
            exit;

        case 'check_featured_limit':
            $dealer_id = $_SESSION['user_id'];
            $exclude_vehicle_id = isset($_GET['exclude_vehicle_id']) ? (int)$_GET['exclude_vehicle_id'] : null;
            
            // Get real-time featured limit check
            $check_result = canAddFeaturedListing($pdo, $dealer_id, $exclude_vehicle_id);
            
            echo json_encode([
                'success' => true, 
                'data' => [
                    'limit' => $check_result['limit'],
                    'current' => $check_result['current'],
                    'can_add' => $check_result['can_add'],
                    'remaining' => $check_result['limit'] === -1 ? 'unlimited' : max(0, $check_result['limit'] - $check_result['current'])
                ]
            ]);
            exit;
    }
    
    echo json_encode(['success' => false, 'error' => 'Invalid AJAX request']);
    exit;
}

// Handle POST requests
$action = $_POST['action'] ?? $_GET['action'] ?? 'dealer_dashboard';
$message = '';
$error = '';

if ($_POST) {
    try {
        switch ($_POST['action'] ?? $action) {
          case 'add_vehicle':
    if (($_SESSION['status'] ?? 'active') != 'active') {
        $error = "Account not active.";
        break;
    }
    
    // Validate description length
    if (strlen($_POST['description'] ?? '') > 3000) {
        $error = "Description cannot exceed 3000 characters.";
        break;
    }

    // FIXED: Check featured limit if featured is requested
    if (isset($_POST['featured'])) {
        $featured_check = canAddFeaturedListing($pdo, $_SESSION['user_id']);
        if (!$featured_check['can_add']) {
            $error = "You have reached your featured listing limit ({$featured_check['limit']}). Current: {$featured_check['current']}/{$featured_check['limit']}. Please contact admin to increase your limit.";
            break;
        }
    }
    
    // Insert vehicle with enhanced fields including featured status
    $stmt = $pdo->prepare("INSERT INTO vehicles (
        dealer_id, category, listing_type, condition_type, make, model, variant, year, price, 
        mileage, hours_used, engine_type, horsepower, transmission, fuel_type, color, region, 
        city, no_accidents, warranty, finance_available, trade_in, service_history, roadworthy, 
        description, features, youtube_video_url, daily_rate, weekly_rate, monthly_rate, 
        auction_start_date, auction_end_date, reserve_price, featured, featured_until, 
        vin_number, registration_number, created_at
    ) VALUES (
        ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 
        ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 
        ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 
        ?, ?, ?, ?, ?, ?, ?
    )");

    $stmt->execute([
        $_SESSION['user_id'],
        $_POST['category'],
        $_POST['listing_type'] ?? 'sale',
        $_POST['condition_type'] ?? 'used',
        $_POST['make'],
        $_POST['model'],
        $_POST['variant'] ?: null,
        $_POST['year'] ?: null,
        $_POST['price'],
        $_POST['mileage'] ?: 0,
        $_POST['hours_used'] ?: 0,
        $_POST['engine_type'] ?: null,
        $_POST['horsepower'] ?: null,
        $_POST['transmission'] ?: null,
        $_POST['fuel_type'] ?: null,
        $_POST['color'] ?: null,
        $_POST['region'],
        $_POST['city'],
        isset($_POST['no_accidents']) ? 1 : 0,
        isset($_POST['warranty']) ? 1 : 0,
        isset($_POST['finance_available']) ? 1 : 0,
        isset($_POST['trade_in']) ? 1 : 0,
        isset($_POST['service_history']) ? 1 : 0,
        isset($_POST['roadworthy']) ? 1 : 0,
        $_POST['description'] ?: null,
        $_POST['features'] ?: null,
        $_POST['youtube_video_url'] ?: null,
        $_POST['daily_rate'] ?: null,
        $_POST['weekly_rate'] ?: null,
        $_POST['monthly_rate'] ?: null,
        $_POST['auction_start_date'] ?: null,
        $_POST['auction_end_date'] ?: null,
        $_POST['reserve_price'] ?: null,
        isset($_POST['featured']) ? 1 : 0,
        $_POST['featured_until'] ?: null,
        $_POST['vin_number'] ?: null,
        $_POST['registration_number'] ?: null,
        date('Y-m-d H:i:s')
    ]);
    $vehicle_id = $pdo->lastInsertId();
    
   

                // Update featured count if featured
                if (isset($_POST['featured'])) {
                    updateDealerFeaturedCount($pdo, $_SESSION['user_id']);
                }
                
                // Handle file uploads with enhanced limits
                $image_count = 0;
                $video_count = 0;
                
                // Handle images (max 40 images, 2MB each)
                if (!empty($_FILES['images']['name'][0])) {
                    foreach ($_FILES['images']['name'] as $key => $name) {
                        if ($image_count >= 40) break; // Limit to 40 images
                        
                        if ($_FILES['images']['error'][$key] === UPLOAD_ERR_OK) {
                            $file = [
                                'name' => $_FILES['images']['name'][$key],
                                'tmp_name' => $_FILES['images']['tmp_name'][$key],
                                'error' => $_FILES['images']['error'][$key],
                                'size' => $_FILES['images']['size'][$key]
                            ];
                            
                            $upload_result = uploadFile($file);
                            if ($upload_result && $upload_result['type'] === 'image') {
                                $is_primary = ($image_count === 0) ? 1 : 0;
                                $stmt = $pdo->prepare("INSERT INTO vehicle_images (vehicle_id, image_path, image_name, file_size, image_order, is_primary) VALUES (?, ?, ?, ?, ?, ?)");
                                $stmt->execute([$vehicle_id, $upload_result['path'], $upload_result['name'], $upload_result['size'], $image_count, $is_primary]);
                                $image_count++;
                            }
                        }
                    }
                }
                
                // Handle videos (max 5 videos, 50MB each)
                if (!empty($_FILES['videos']['name'][0])) {
                    foreach ($_FILES['videos']['name'] as $key => $name) {
                        if ($video_count >= 5) break; // Limit to 5 videos
                        
                        if ($_FILES['videos']['error'][$key] === UPLOAD_ERR_OK) {
                            $file = [
                                'name' => $_FILES['videos']['name'][$key],
                                'tmp_name' => $_FILES['videos']['tmp_name'][$key],
                                'error' => $_FILES['videos']['error'][$key],
                                'size' => $_FILES['videos']['size'][$key]
                            ];
                            
                            $upload_result = uploadFile($file);
                            if ($upload_result && $upload_result['type'] === 'video') {
                                $stmt = $pdo->prepare("INSERT INTO vehicle_videos (vehicle_id, video_path, video_title, file_size) VALUES (?, ?, ?, ?)");
                                $stmt->execute([$vehicle_id, $upload_result['path'], $upload_result['name'], $upload_result['size']]);
                                $video_count++;
                            }
                        }
                    }
                }
                
                // Handle documents (PDF only)
                if (!empty($_FILES['documents']['name'][0])) {
                    foreach ($_FILES['documents']['name'] as $key => $name) {
                        if ($_FILES['documents']['error'][$key] === UPLOAD_ERR_OK) {
                            $file = [
                                'name' => $_FILES['documents']['name'][$key],
                                'tmp_name' => $_FILES['documents']['tmp_name'][$key],
                                'error' => $_FILES['documents']['error'][$key],
                                'size' => $_FILES['documents']['size'][$key]
                            ];
                            
                            $upload_result = uploadFile($file);
                            if ($upload_result && $upload_result['type'] === 'document') {
                                $stmt = $pdo->prepare("INSERT INTO vehicle_documents (vehicle_id, document_name, document_path, file_size) VALUES (?, ?, ?, ?)");
                                $stmt->execute([$vehicle_id, $upload_result['name'], $upload_result['path'], $upload_result['size']]);
                            }
                        }
                    }
                }
                
                ob_end_clean();
                header("Location: dealer.php?msg=added");
                exit;
                break;

            case 'update_vehicle':
                if (($_SESSION['status'] ?? 'active') != 'active') {
                    $error = "Account not active.";
                    break;
                }
                
                $vehicle_id = (int)$_POST['vehicle_id'];
                
                // Verify ownership
                $stmt = $pdo->prepare("SELECT vehicle_id, featured FROM vehicles WHERE vehicle_id = ? AND dealer_id = ?");
                $stmt->execute([$vehicle_id, $_SESSION['user_id']]);
                $existing_vehicle = $stmt->fetch();
                
                if (!$existing_vehicle) {
                    $error = "Vehicle not found or access denied.";
                    break;
                }
                
                // Validate description length
                if (strlen($_POST['description'] ?? '') > 3000) {
                    $error = "Description cannot exceed 3000 characters.";
                    break;
                }

                // FIXED: Check featured limit if featured is being enabled
                if (isset($_POST['featured']) && !$existing_vehicle['featured']) {
                    $featured_check = canAddFeaturedListing($pdo, $_SESSION['user_id'], $vehicle_id);
                    if (!$featured_check['can_add']) {
                        $error = "You have reached your featured listing limit ({$featured_check['limit']}). Current: {$featured_check['current']}/{$featured_check['limit']}. Please contact admin to increase your limit.";
                        break;
                    }
                }
                
                // Update vehicle
                $stmt = $pdo->prepare("
                    UPDATE vehicles SET 
                    category = ?, listing_type = ?, condition_type = ?, make = ?, model = ?, variant = ?, 
                    year = ?, price = ?, mileage = ?, hours_used = ?, color = ?, region = ?, city = ?,
                    no_accidents = ?, warranty = ?, finance_available = ?, trade_in = ?, service_history = ?, 
                    roadworthy = ?, description = ?, features = ?, youtube_video_url = ?, 
                    featured = ?, featured_until = ?, updated_at = NOW()
                    WHERE vehicle_id = ?
                ");
                
                $stmt->execute([
                    $_POST['category'],
                    $_POST['listing_type'] ?? 'sale',
                    $_POST['condition_type'] ?? 'used',
                    $_POST['make'],
                    $_POST['model'],
                    $_POST['variant'] ?: null,
                    $_POST['year'] ?: null,
                    $_POST['price'],
                    $_POST['mileage'] ?: 0,
                    $_POST['hours_used'] ?: 0,
                    $_POST['color'] ?: null,
                    $_POST['region'],
                    $_POST['city'],
                    isset($_POST['no_accidents']) ? 1 : 0,
                    isset($_POST['warranty']) ? 1 : 0,
                    isset($_POST['finance_available']) ? 1 : 0,
                    isset($_POST['trade_in']) ? 1 : 0,
                    isset($_POST['service_history']) ? 1 : 0,
                    isset($_POST['roadworthy']) ? 1 : 0,
                    $_POST['description'] ?: null,
                    $_POST['features'] ?: null,
                    $_POST['youtube_video_url'] ?: null,
                    isset($_POST['featured']) ? 1 : 0,
                    $_POST['featured_until'] ?: null,
                    $vehicle_id
                ]);
                
                // Update featured count
                updateDealerFeaturedCount($pdo, $_SESSION['user_id']);
                
                ob_end_clean();
                header("Location: dealer.php?msg=updated");
                exit;
                break;

            case 'toggle_featured':
                $vehicle_id = (int)$_POST['vehicle_id'];
                $featured = isset($_POST['featured']) ? 1 : 0;
                $featured_until = $_POST['featured_until'] ?: null;
                
                // Verify ownership
                $stmt = $pdo->prepare("SELECT vehicle_id FROM vehicles WHERE vehicle_id = ? AND dealer_id = ?");
                $stmt->execute([$vehicle_id, $_SESSION['user_id']]);
                
                if ($stmt->fetch()) {
                    // FIXED: Check featured limit if enabling featured
                    if ($featured) {
                        $featured_check = canAddFeaturedListing($pdo, $_SESSION['user_id'], $vehicle_id);
                        if (!$featured_check['can_add']) {
                            $error = "You have reached your featured listing limit ({$featured_check['limit']}). Current active: {$featured_check['current']}/{$featured_check['limit']}. Please contact admin to increase your limit.";
                            break;
                        }
                    }

                    $stmt = $pdo->prepare("UPDATE vehicles SET featured = ?, featured_until = ? WHERE vehicle_id = ?");
                    $stmt->execute([$featured, $featured_until, $vehicle_id]);
                    
                    // Update featured count
                    updateDealerFeaturedCount($pdo, $_SESSION['user_id']);
                    
                    ob_end_clean();
                    header("Location: dealer.php?msg=featured_updated");
                    exit;
                } else {
                    $error = "Vehicle not found or access denied.";
                }
                break;

            case 'add_branch':
                // Validate input
                $required = ['branch_name', 'address', 'city', 'region'];
                foreach ($required as $field) {
                    if (empty($_POST[$field])) {
                        $error = "Please fill all required fields";
                        break 2;
                    }
                }
                
                // Handle main branch - only one can be main
                $is_main = isset($_POST['is_main_branch']) ? 1 : 0;
                if ($is_main) {
                    // Reset any existing main branch
                    $stmt = $pdo->prepare("UPDATE dealer_branches SET is_main_branch = 0 WHERE dealer_id = ?");
                    $stmt->execute([$_SESSION['user_id']]);
                }
                
                $stmt = $pdo->prepare("INSERT INTO dealer_branches (dealer_id, branch_name, address, city, region, postal_code, phone, email, is_main_branch, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute([
                    $_SESSION['user_id'],
                    $_POST['branch_name'],
                    $_POST['address'],
                    $_POST['city'],
                    $_POST['region'],
                    $_POST['postal_code'] ?? null,
                    $_POST['phone'] ?? null,
                    $_POST['email'] ?? null,
                    $is_main,
                    'active'
                ]);
                
                ob_end_clean();
                header("Location: dealer.php?action=manage_branches&msg=added");
                exit;
                break;

            case 'update_branch':
                $branch_id = (int)$_POST['branch_id'];
                
                // Verify ownership
                $stmt = $pdo->prepare("SELECT branch_id FROM dealer_branches WHERE branch_id = ? AND dealer_id = ?");
                $stmt->execute([$branch_id, $_SESSION['user_id']]);
                
                if ($stmt->fetch()) {
                    $is_main = isset($_POST['is_main_branch']) ? 1 : 0;
                    if ($is_main) {
                        // Reset any existing main branch
                        $stmt = $pdo->prepare("UPDATE dealer_branches SET is_main_branch = 0 WHERE dealer_id = ?");
                        $stmt->execute([$_SESSION['user_id']]);
                    }
                    
                    $stmt = $pdo->prepare("UPDATE dealer_branches SET branch_name = ?, address = ?, city = ?, region = ?, postal_code = ?, phone = ?, email = ?, is_main_branch = ?, status = ? WHERE branch_id = ?");
                    $stmt->execute([
                        $_POST['branch_name'],
                        $_POST['address'],
                        $_POST['city'],
                        $_POST['region'],
                        $_POST['postal_code'] ?? null,
                        $_POST['phone'] ?? null,
                        $_POST['email'] ?? null,
                        $is_main,
                        $_POST['status'] ?? 'active',
                        $branch_id
                    ]);
                    
                    ob_end_clean();
                    header("Location: dealer.php?action=manage_branches&msg=updated");
                    exit;
                } else {
                    $error = "Branch not found or access denied.";
                }
                break;

            case 'delete_branch':
                $branch_id = (int)$_POST['branch_id'];
                
                // Verify ownership
                $stmt = $pdo->prepare("SELECT branch_id FROM dealer_branches WHERE branch_id = ? AND dealer_id = ?");
                $stmt->execute([$branch_id, $_SESSION['user_id']]);
                
                if ($stmt->fetch()) {
                    // Don't allow deleting the main branch
                    $stmt = $pdo->prepare("SELECT is_main_branch FROM dealer_branches WHERE branch_id = ?");
                    $stmt->execute([$branch_id]);
                    $is_main = $stmt->fetchColumn();
                    
                    if ($is_main) {
                        $error = "Cannot delete the main branch. Set another branch as main first.";
                        break;
                    }
                    
                    $stmt = $pdo->prepare("DELETE FROM dealer_branches WHERE branch_id = ?");
                    $stmt->execute([$branch_id]);
                    
                    ob_end_clean();
                    header("Location: dealer.php?action=manage_branches&msg=deleted");
                    exit;
                } else {
                    $error = "Branch not found or access denied.";
                }
                break;

            case 'delete_vehicle':
                $vehicle_id = $_POST['vehicle_id'];
                
                // Verify ownership
                $stmt = $pdo->prepare("SELECT vehicle_id FROM vehicles WHERE vehicle_id = ? AND dealer_id = ?");
                $stmt->execute([$vehicle_id, $_SESSION['user_id']]);
                
                if ($stmt->fetch()) {
                    // Delete associated files first
                    try {
                        $stmt = $pdo->prepare("SELECT image_path FROM vehicle_images WHERE vehicle_id = ?");
                        $stmt->execute([$vehicle_id]);
                        $images = $stmt->fetchAll(PDO::FETCH_COLUMN);
                        foreach ($images as $image_path) {
                            if (file_exists($image_path)) {
                                unlink($image_path);
                            }
                        }
                        
                        $stmt = $pdo->prepare("SELECT video_path FROM vehicle_videos WHERE vehicle_id = ?");
                        $stmt->execute([$vehicle_id]);
                        $videos = $stmt->fetchAll(PDO::FETCH_COLUMN);
                        foreach ($videos as $video_path) {
                            if (file_exists($video_path)) {
                                unlink($video_path);
                            }
                        }
                        
                        $stmt = $pdo->prepare("SELECT document_path FROM vehicle_documents WHERE vehicle_id = ?");
                        $stmt->execute([$vehicle_id]);
                        $documents = $stmt->fetchAll(PDO::FETCH_COLUMN);
                        foreach ($documents as $document_path) {
                            if (file_exists($document_path)) {
                                unlink($document_path);
                            }
                        }
                    } catch(Exception $e) {
                        error_log("Error deleting files: " . $e->getMessage());
                    }
                    
                    // Delete vehicle (cascade will handle related records)
                    $stmt = $pdo->prepare("DELETE FROM vehicles WHERE vehicle_id = ?");
                    $stmt->execute([$vehicle_id]);
                    
                    // Update featured count
                    updateDealerFeaturedCount($pdo, $_SESSION['user_id']);
                    
                    ob_end_clean();
                    header("Location: dealer.php?msg=deleted");
                    exit;
                } else {
                    $error = "Vehicle not found or access denied.";
                }
                break;

            case 'add_sales_team':
                $photo_path = null;
                
                // Handle photo upload
                if (!empty($_FILES['photo']['name'])) {
                    $upload_result = uploadFile($_FILES['photo']);
                    if ($upload_result && $upload_result['type'] === 'image') {
                        $photo_path = $upload_result['path'];
                    }
                }
                
                $stmt = $pdo->prepare("INSERT INTO dealer_sales_team (dealer_id, name, position, phone, email, whatsapp, photo, facebook_url, twitter_url, instagram_url, linkedin_url, youtube_url, tiktok_url, website_url) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute([
                    $_SESSION['user_id'],
                    $_POST['name'],
                    $_POST['position'] ?: null,
                    $_POST['phone'] ?: null,
                    $_POST['email'] ?: null,
                    $_POST['whatsapp'] ?: null,
                    $photo_path,
                    $_POST['facebook_url'] ?: null,
                    $_POST['twitter_url'] ?: null,
                    $_POST['instagram_url'] ?: null,
                    $_POST['linkedin_url'] ?: null,
                    $_POST['youtube_url'] ?: null,
                    $_POST['tiktok_url'] ?: null,
                    $_POST['website_url'] ?: null
                ]);
                
                ob_end_clean();
                header("Location: dealer.php?action=manage_sales_team&msg=added");
                exit;
                break;
        }
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
        error_log("Dealer action error: " . $e->getMessage());
    }
}

// Handle logout
if ($action == 'logout') {
    session_destroy();
    ob_end_clean();
    header("Location: login.php");
    exit;
}

// Handle success messages
if (isset($_GET['msg'])) {
    $messages = [
        'added' => 'Record added successfully!',
        'updated' => 'Record updated successfully!',
        'deleted' => 'Record deleted successfully!',
        'featured_updated' => 'Featured listing status updated!',
    ];
    $message = $messages[$_GET['msg']] ?? 'Action completed successfully!';
}
?> 
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TrucksONSale - Professional Dealer Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-navy: #2563EB;
            --primary-navy-light: #3B82F6;
            --primary-navy-dark: #1E40AF;
            --accent-blue: #60A5FA;
            --accent-light: #DBEAFE;
            --accent-orange: #F1FAEE;
            --accent-coral: #EF4444;
            --success-green: #10B981;
            --warning-orange: #F59E0B;
            --text-dark: #1F2937;
            --text-light: #F9FAFB;
            --gray-100: #F8F9FA;
            --gray-200: #E9ECEF;
            --gray-300: #DEE2E6;
            --gray-400: #CED4DA;
            --gray-500: #ADB5BD;
            --gray-600: #6C757D;
            --gray-700: #495057;
            --gray-800: #343A40;
            --gray-900: #212529;
            --white: #FFFFFF;
            --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
            --border-radius: 0.75rem;
            --border-radius-lg: 1rem;
            --border-radius-xl: 1.5rem;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --gradient-primary: linear-gradient(135deg, var(--primary-navy), var(--primary-navy-light));
            --gradient-accent: linear-gradient(135deg, var(--accent-blue), var(--accent-light));
            --gradient-success: linear-gradient(135deg, var(--success-green), #4ECDC4);
            --gradient-warning: linear-gradient(135deg, var(--warning-orange), #FFA726);
            --gradient-danger: linear-gradient(135deg, var(--accent-coral), #FF5722);
        }
        
        * {
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: var(--text-dark);
            line-height: 1.5;
            margin: 0;
            padding: 0;
            font-weight: 400;
            font-size: 14px;
        }

        /* Professional Navigation */
        .navbar-custom {
            background: var(--gradient-primary) !important;
            box-shadow: var(--shadow-lg);
            backdrop-filter: blur(20px);
            padding: 0.75rem 0;
            border-bottom: 2px solid var(--accent-blue);
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.25rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--text-light) !important;
            text-decoration: none;
            transition: var(--transition);
        }

        .navbar-brand:hover {
            transform: scale(1.02);
            color: var(--accent-light) !important;
        }

        .navbar-brand i {
            font-size: 1.5rem;
            color: var(--accent-light);
        }

        .navbar-text {
            color: var(--text-light) !important;
            font-weight: 500;
            font-size: 0.875rem;
        }

        /* Professional Button System */
        .btn {
            font-weight: 600;
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius);
            transition: var(--transition);
            border: none;
            position: relative;
            overflow: hidden;
            text-transform: none;
            font-size: 0.875rem;
            box-shadow: var(--shadow-sm);
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: var(--text-light);
            border: 2px solid transparent;
        }
        
        .btn-primary:hover {
            background: var(--gradient-accent);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            color: var(--white);
        }

        .btn-outline-primary {
            border: 2px solid var(--primary-navy);
            color: var(--primary-navy);
            background: transparent;
        }
        
        .btn-outline-primary:hover {
            background: var(--gradient-primary);
            color: var(--text-light);
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        .btn-success {
            background: var(--gradient-success);
            color: var(--white);
        }

        .btn-warning {
            background: var(--gradient-warning);
            color: var(--white);
        }

        .btn-danger {
            background: var(--gradient-danger);
            color: var(--white);
        }

        .btn-info {
            background: var(--gradient-accent);
            color: var(--white);
        }

        .btn-light {
            background: var(--white);
            color: var(--text-dark);
            border: 2px solid var(--gray-200);
        }

        .btn-outline-light {
            border: 2px solid var(--text-light);
            color: var(--text-light);
            background: transparent;
        }

        .btn-outline-light:hover {
            background: var(--text-light);
            color: var(--primary-navy);
        }

        /* Professional Card System */
        .card {
            border: none;
            box-shadow: var(--shadow);
            border-radius: var(--border-radius-lg);
            overflow: hidden;
            transition: var(--transition);
            background: var(--white);
            margin-bottom: 2rem;
            border: 1px solid var(--gray-200);
        }
        
        .card:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-4px);
        }
        
        .card-header {
            background: var(--gradient-primary);
            color: var(--text-light);
            border: none;
            padding: 1rem 1.5rem;
            font-weight: 700;
            font-size: 1rem;
            position: relative;
        }

        .card-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: var(--gradient-accent);
        }

        .card-body {
            padding: 1.5rem;
        }

        /* Professional Form Elements */
        .form-control, .form-select {
            border: 2px solid var(--gray-300);
            border-radius: var(--border-radius);
            padding: 0.5rem 0.75rem;
            transition: var(--transition);
            font-size: 0.875rem;
            font-weight: 500;
            background: var(--white);
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--accent-blue);
            box-shadow: 0 0 0 0.2rem rgba(96, 165, 250, 0.25);
            outline: none;
            transform: translateY(-1px);
        }
        
        .form-label {
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
        }

        .form-label i {
            color: var(--accent-blue);
            font-size: 1rem;
        }

        /* Enhanced File Upload */
        .file-upload-area {
            border: 2px dashed var(--gray-400);
            border-radius: var(--border-radius-lg);
            padding: 2rem 1.5rem;
            text-align: center;
            transition: var(--transition);
            background: var(--gray-100);
            margin-bottom: 1rem;
            position: relative;
            overflow: hidden;
        }
        
        .file-upload-area:hover {
            border-color: var(--accent-blue);
            background: linear-gradient(135deg, var(--accent-light), rgba(219, 234, 254, 0.3));
            transform: scale(1.01);
        }

        .file-upload-area i {
            color: var(--accent-blue);
            margin-bottom: 0.75rem;
            font-size: 2rem;
        }

        .file-upload-area h6 {
            font-size: 0.9rem;
            font-weight: 600;
        }

        .file-upload-limits {
            font-size: 0.75rem;
            color: var(--gray-600);
            margin-top: 0.75rem;
            font-weight: 500;
        }

        /* Professional Stats Cards */
        .stats-card {
            background: var(--gradient-primary);
            color: var(--text-light);
            text-align: center;
            padding: 1.5rem 1rem;
            border-radius: var(--border-radius-xl);
            box-shadow: var(--shadow-lg);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            transform: rotate(45deg);
            transition: var(--transition);
            opacity: 0;
        }

        .stats-card:hover::before {
            opacity: 1;
            animation: shimmer 2s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { transform: rotate(45deg) translate(-20%, -20%); }
            50% { transform: rotate(45deg) translate(20%, 20%); }
        }
        
        .stats-card:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 1rem 2rem rgba(37, 99, 235, 0.3);
        }
        
        .stats-card i {
            font-size: 2.5rem;
            margin-bottom: 0.75rem;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }
        
        .stats-card h4 {
            font-size: 2rem;
            font-weight: 700;
            margin: 0.5rem 0;
            position: relative;
            z-index: 2;
        }

        .stats-card p {
            font-size: 0.875rem;
            font-weight: 600;
            margin: 0;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }

        /* Color Variants for Stats Cards */
        .stats-card.bg-success {
            background: var(--gradient-success);
        }

        .stats-card.bg-warning {
            background: var(--gradient-warning);
        }

        .stats-card.bg-danger {
            background: var(--gradient-danger);
        }

        .stats-card.bg-info {
            background: var(--gradient-accent);
        }

        /* Professional Page Header */
        .page-header {
            background: var(--gradient-primary);
            color: var(--text-light);
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: var(--border-radius-xl);
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .page-header .container {
            position: relative;
            z-index: 2;
        }

        .page-header h1 {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .page-header p {
            font-size: 0.9rem;
            opacity: 0.9;
            font-weight: 500;
        }

        /* Enhanced Alert System */
        .alert {
            border: none;
            border-radius: var(--border-radius-lg);
            padding: 0.75rem 1rem;
            margin-bottom: 1.5rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            box-shadow: var(--shadow);
            border-left: 4px solid;
            font-size: 0.875rem;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
            border-left-color: var(--success-green);
        }
        
        .alert-danger {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
            border-left-color: var(--accent-coral);
        }
        
        .alert-info {
            background: linear-gradient(135deg, var(--accent-light), rgba(219, 234, 254, 0.5));
            color: var(--text-dark);
            border-left-color: var(--accent-blue);
        }

        /* Professional Vehicle Cards */
        .vehicle-card {
            transition: var(--transition);
            overflow: hidden;
            border-radius: var(--border-radius-lg);
            background: var(--white);
            box-shadow: var(--shadow);
            border: 1px solid var(--gray-200);
        }

        .vehicle-card:hover {
            transform: translateY(-6px);
            box-shadow: var(--shadow-lg);
        }
        
        .vehicle-image {
            height: 200px;
            overflow: hidden;
            position: relative;
            border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
        }
        
        .vehicle-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s ease;
        }
        
        .vehicle-card:hover .vehicle-image img {
            transform: scale(1.1);
        }

        /* Professional Badges */
        .listing-type-badge {
            position: absolute;
            top: 0.75rem;
            left: 0.75rem;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.7rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            z-index: 3;
            box-shadow: var(--shadow);
            backdrop-filter: blur(10px);
        }
        
        .listing-type-sale { 
            background: linear-gradient(135deg, var(--primary-navy), var(--primary-navy-light)); 
            color: var(--white); 
        }
        .listing-type-rent_to_own { 
            background: var(--gradient-success); 
            color: var(--white); 
        }
        .listing-type-hire { 
            background: var(--gradient-warning); 
            color: var(--white); 
        }
        .listing-type-auction { 
            background: var(--gradient-danger); 
            color: var(--white); 
        }

        .featured-badge {
            position: absolute;
            top: 2.5rem;
            left: 0.75rem;
            background: var(--gradient-danger);
            color: var(--white);
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.7rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            z-index: 3;
            box-shadow: var(--shadow);
            backdrop-filter: blur(10px);
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .premium-badge {
            position: absolute;
            top: 0.75rem;
            right: 0.75rem;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            color: #8B4513;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.7rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            z-index: 3;
            box-shadow: var(--shadow);
            backdrop-filter: blur(10px);
        }

        /* Enhanced Featured Toggle */
        .featured-toggle {
            background: linear-gradient(135deg, #fff5f5, #fee);
            border: 2px dashed var(--accent-coral);
            border-radius: var(--border-radius-lg);
            padding: 1rem;
            margin-bottom: 1rem;
            transition: var(--transition);
        }

        .featured-toggle:hover {
            background: linear-gradient(135deg, #fee, #fdd);
            transform: scale(1.01);
        }

        /* FIXED: Real-time Featured Limit Info */
        .featured-limit-info {
            background: linear-gradient(135deg, var(--accent-light), rgba(219, 234, 254, 0.3));
            border: 2px solid var(--accent-blue);
            border-radius: var(--border-radius);
            padding: 0.75rem;
            margin-bottom: 0.75rem;
            font-weight: 600;
            text-align: center;
            font-size: 0.8rem;
            transition: var(--transition);
        }

        .featured-limit-info.warning {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border-color: var(--warning-orange);
            color: #856404;
        }

        .featured-limit-info.danger {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            border-color: var(--accent-coral);
            color: #721c24;
            animation: pulse 2s ease-in-out infinite;
        }

        /* Professional Loading States */
        .loading-spinner {
            display: none;
            width: 24px;
            height: 24px;
            border: 3px solid var(--gray-300);
            border-top: 3px solid var(--accent-blue);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 12px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .select-loading {
            position: relative;
        }
        
        .select-loading::after {
            content: '';
            position: absolute;
            right: 35px;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 20px;
            border: 2px solid var(--gray-300);
            border-top: 2px solid var(--accent-blue);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        /* Character Counter */
        .char-counter {
            font-size: 0.75rem;
            color: var(--gray-600);
            text-align: right;
            margin-top: 0.25rem;
            font-weight: 500;
        }
        
        .char-counter.warning {
            color: var(--warning-orange);
            font-weight: 700;
        }
        
        .char-counter.danger {
            color: var(--accent-coral);
            font-weight: 700;
            animation: pulse 1s ease-in-out infinite;
        }

        /* Professional Hours Display */
        .hours-display {
            background: var(--gradient-warning);
            color: var(--white);
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: 700;
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            box-shadow: var(--shadow-sm);
        }

        /* Sales Team Cards */
        .team-member-card {
            transition: var(--transition);
            border: none;
            box-shadow: var(--shadow);
            border-radius: var(--border-radius-lg);
            overflow: hidden;
            background: var(--white);
        }

        .team-member-card:hover {
            transform: translateY(-6px);
            box-shadow: var(--shadow-lg);
        }

        .contact-info .btn, .social-links .btn {
            margin: 0.25rem;
            border-radius: 50px;
            width: 45px;
            height: 45px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 1.1rem;
        }

        /* Enhanced Responsive Design */
        @media (max-width: 768px) {
            .card-body {
                padding: 1rem;
            }
            
            .btn {
                padding: 0.4rem 0.8rem;
                font-size: 0.8rem;
            }
            
            .stats-card {
                padding: 1rem 0.75rem;
            }
            
            .stats-card h4 {
                font-size: 1.5rem;
            }

            .stats-card i {
                font-size: 2rem;
            }

            .page-header {
                padding: 1.5rem 0;
            }

            .page-header h1 {
                font-size: 1.5rem;
            }

            .navbar-brand {
                font-size: 1.1rem;
            }

            .vehicle-image {
                height: 180px;
            }

            .form-control, .form-select {
                padding: 0.4rem 0.6rem;
                font-size: 0.8rem;
            }

            .card-header {
                padding: 0.75rem 1rem;
                font-size: 0.9rem;
            }
        }

        /* Professional Empty States */
        .empty-state {
            text-align: center;
            padding: 3rem 1.5rem;
            color: var(--gray-600);
        }

        .empty-state i {
            font-size: 3rem;
            color: var(--gray-400);
            margin-bottom: 1rem;
        }

        .empty-state h4 {
            color: var(--gray-700);
            margin-bottom: 0.75rem;
            font-size: 1.1rem;
        }

        .empty-state p {
            color: var(--gray-600);
            margin-bottom: 1.5rem;
            font-size: 0.9rem;
        }

        /* Professional Toast Notifications */
        .toast-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 280px;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-lg);
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
            font-size: 0.875rem;
        }

        .toast-notification.show {
            opacity: 1;
            transform: translateX(0);
        }

        /* Form Check Enhancements */
        .form-check {
            padding: 0.75rem;
            border: 2px solid var(--gray-300);
            border-radius: var(--border-radius);
            transition: var(--transition);
            margin-bottom: 0.75rem;
        }

        .form-check:hover {
            border-color: var(--accent-blue);
            background: var(--gray-100);
        }

        .form-check-input:checked {
            background-color: var(--accent-blue);
            border-color: var(--accent-blue);
        }

        .form-check-label {
            font-size: 0.875rem;
        }

        .form-check-label small {
            font-size: 0.75rem;
        }

        /* Vehicle Details Enhancement */
        .vehicle-details {
            background: var(--gray-100);
            border-radius: var(--border-radius);
            padding: 0.75rem;
            font-size: 0.875rem;
        }

        .vehicle-details div {
            border-bottom: 1px solid var(--gray-300);
            padding: 0.25rem 0;
        }

        .vehicle-details div:last-child {
            border-bottom: none;
        }

        /* Professional Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--gray-200);
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--accent-blue), var(--accent-light));
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--primary-navy), var(--accent-blue));
        }
    </style>
</head>
<body>

<nav class="navbar navbar-expand-lg navbar-dark navbar-custom">
    <div class="container">
        <a class="navbar-brand" href="dealer.php">
            <i class="fas fa-truck"></i>
            <span>TrucksONSale Professional</span>
        </a>
        <div class="navbar-nav ms-auto">
            <span class="navbar-text me-4">
                <i class="fas fa-user-circle me-2"></i>
                Welcome, <strong><?php echo htmlspecialchars($_SESSION['username']); ?></strong>
            </span>
            <a href="dealer.php?action=logout" class="btn btn-outline-light">
                <i class="fas fa-sign-out-alt me-2"></i>Logout
            </a>
        </div>
    </div>
</nav>

<div class="container mt-4">
    <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show" data-aos="fade-down">
            <i class="fas fa-check-circle"></i>
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" data-aos="fade-down">
            <i class="fas fa-exclamation-circle"></i>
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php
    switch ($action) {
        case 'dealer_dashboard':
        default:
            if (($_SESSION['status'] ?? 'active') != 'active') {
                ?>
                <div class="text-center mt-5" data-aos="fade-up">
                    <div class="card">
                        <div class="card-body py-5">
                            <i class="fas fa-clock fa-4x text-warning mb-3"></i>
                            <h3>Account Status: <?php echo ucfirst($_SESSION['status'] ?? 'pending'); ?></h3>
                            <p class="text-muted">Your account is pending admin approval. Please wait for activation.</p>
                        </div>
                    </div>
                </div>
                <?php
                break;
            }

            // Get dealer's enhanced vehicles with featured status
            $stmt = $pdo->prepare("
                SELECT v.*, c.category_name, c.icon as category_icon, s.subcategory_name
                FROM vehicles v 
                LEFT JOIN categories c ON v.category = c.category_key
                LEFT JOIN subcategories s ON (s.category_id = c.category_id AND v.subcategory = s.subcategory_key)
                WHERE v.dealer_id = ? 
                ORDER BY v.featured DESC, v.premium_listing DESC, v.created_at DESC
            ");
            $stmt->execute([$_SESSION['user_id']]);
            $dealer_vehicles = $stmt->fetchAll();

            // Get stats by listing type
            $stmt = $pdo->prepare("SELECT listing_type, COUNT(*) as count FROM vehicles WHERE dealer_id = ? GROUP BY listing_type");
            $stmt->execute([$_SESSION['user_id']]);
            $listing_type_stats = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

            // FIXED: Get real-time featured and premium counts
            $stmt = $pdo->prepare("
                SELECT COUNT(*) FROM vehicles 
                WHERE dealer_id = ? AND featured = 1 
                AND (featured_until IS NULL OR featured_until > NOW()) 
                AND status != 'deleted'
            ");
            $stmt->execute([$_SESSION['user_id']]);
            $featured_count = (int)$stmt->fetchColumn();

            $stmt = $pdo->prepare("
                SELECT COUNT(*) FROM vehicles 
                WHERE dealer_id = ? AND premium_listing = 1 
                AND (premium_until IS NULL OR premium_until > NOW()) 
                AND status != 'deleted'
            ");
            $stmt->execute([$_SESSION['user_id']]);
            $premium_count = (int)$stmt->fetchColumn();

            // FIXED: Get dealer's featured limit with real-time data
            $featured_info = getDealerFeaturedLimit($pdo, $_SESSION['user_id']);
            ?>
            
            <div class="page-header" data-aos="fade-down">
                <div class="container">
                    <div class="d-flex justify-content-between align-items-center flex-wrap">
                        <div>
                            <h1><i class="fas fa-tachometer-alt me-3"></i>Professional Dashboard</h1>
                            <p class="mb-0">Manage your premium vehicle listings with advanced tools</p>
                        </div>
                       
                        <div class="btn-group" role="group">
                            <a href="?action=add_vehicle" class="btn btn-light btn-lg">
                                <i class="fas fa-plus me-2"></i>Add New Listing
                            </a>
                            <a href="?action=manage_sales_team" class="btn btn-outline-light">
                                <i class="fas fa-users me-2"></i>Sales Team
                            </a>
                            <a href="?action=manage_branches" class="btn btn-outline-light">
                                <i class="fas fa-store me-2"></i>Branches
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- FIXED: Real-time Featured Listing Limit Info -->
            <div class="row mb-4">
                <div class="col-12">
                    <?php 
                    $limit_class = '';
                    $limit_text = '';
                    $remaining = $featured_info['limit'] === -1 ? 'unlimited' : max(0, $featured_info['limit'] - $featured_info['current']);
                    
                    if ($featured_info['limit'] !== -1) {
                        $percentage = ($featured_info['current'] / $featured_info['limit']) * 100;
                        if ($percentage >= 100) {
                            $limit_class = 'danger';
                            $limit_text = 'LIMIT REACHED';
                        } elseif ($percentage >= 80) {
                            $limit_class = 'warning';
                            $limit_text = 'ALMOST FULL';
                        }
                    }
                    ?>
                    <div class="featured-limit-info <?= $limit_class ?>" data-aos="fade-up" id="featured-limit-display">
                        <i class="fas fa-fire me-2"></i>
                        <strong>Featured Listings:</strong> 
                        <span id="current-featured-count"><?= $featured_info['current'] ?></span> / 
                        <span id="featured-limit"><?= $featured_info['limit'] == -1 ? 'Unlimited' : $featured_info['limit'] ?></span>
                        <?php if ($featured_info['limit'] != -1): ?>
                            <span class="ms-3">
                                <i class="fas fa-info-circle me-1"></i>
                                <span id="remaining-slots"><?= $remaining ?></span> featured slots remaining
                                <?php if ($limit_text): ?>
                                    <strong class="ms-2"><?= $limit_text ?></strong>
                                <?php endif; ?>
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Enhanced Stats Cards -->
            <div class="row mb-5">
                <div class="col-md-3 mb-4" data-aos="fade-up" data-aos-delay="100">
                    <div class="stats-card">
                        <i class="fas fa-list-alt"></i>
                        <h4><?php echo count($dealer_vehicles); ?></h4>
                        <p>Total Listings</p>
                    </div>
                </div>
                <div class="col-md-3 mb-4" data-aos="fade-up" data-aos-delay="200">
                    <div class="stats-card bg-success">
                        <i class="fas fa-shopping-cart"></i>
                        <h4><?php echo $listing_type_stats['sale'] ?? 0; ?></h4>
                        <p>For Sale</p>
                    </div>
                </div>
                <div class="col-md-3 mb-4" data-aos="fade-up" data-aos-delay="300">
                    <div class="stats-card bg-warning">
                        <i class="fas fa-calendar-check"></i>
                        <h4><?php echo ($listing_type_stats['hire'] ?? 0) + ($listing_type_stats['rent-to-own'] ?? 0); ?></h4>
                        <p>Hire/Rent</p>
                    </div>
                </div>
                <div class="col-md-3 mb-4" data-aos="fade-up" data-aos-delay="400">
                    <div class="stats-card bg-danger">
                        <i class="fas fa-gavel"></i>
                        <h4><?php echo $listing_type_stats['auction'] ?? 0; ?></h4>
                        <p>Auctions</p>
                    </div>
                </div>
            </div>

            <!-- Featured and Premium Stats -->
            <div class="row mb-5">
                <div class="col-md-6 mb-4" data-aos="fade-left">
                    <div class="card">
                        <div class="card-body text-center py-4">
                            <i class="fas fa-fire fa-3x text-danger mb-3"></i>
                            <h3 class="text-danger mb-2" id="featured-count-display"><?= $featured_count ?></h3>
                            <p class="text-muted mb-2"><strong>Featured Listings</strong></p>
                            <small class="text-muted">Enhanced visibility and priority placement</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-4" data-aos="fade-right">
                    <div class="card">
                        <div class="card-body text-center py-4">
                            <i class="fas fa-crown fa-3x text-warning mb-3"></i>
                            <h3 class="text-warning mb-2"><?= $premium_count ?></h3>
                            <p class="text-muted mb-2"><strong>Premium Listings</strong></p>
                            <small class="text-muted">Premium features and benefits</small>
                        </div>
                    </div>
                </div>
            </div>

            <?php if (empty($dealer_vehicles)): ?>
                <div class="empty-state" data-aos="fade-up">
                    <div class="card">
                        <div class="card-body py-5">
                            <i class="fas fa-list-alt"></i>
                            <h4>No Listings Yet</h4>
                            <p>Start building your inventory by adding your first professional listing.</p>
                            <a href="?action=add_vehicle" class="btn btn-primary btn-lg">
                                <i class="fas fa-plus me-2"></i>Add Your First Listing
                            </a>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <div class="row">
                    <?php foreach ($dealer_vehicles as $index => $v): ?>
                    <div class="col-md-6 col-xl-4 mb-4" data-aos="fade-up" data-aos-delay="<?= $index * 100 ?>">
                        <div class="card vehicle-card">
                            <div class="vehicle-image">
                                <?php
                                // Get primary image
                                $stmt = $pdo->prepare("SELECT image_path FROM vehicle_images WHERE vehicle_id = ? AND is_primary = 1 LIMIT 1");
                                $stmt->execute([$v['vehicle_id']]);
                                $primary_image = $stmt->fetchColumn();
                                ?>
                                <img src="<?php echo $primary_image ?: 'https://via.placeholder.com/400x250/f8fafc/64748b?text=No+Image'; ?>" alt="Vehicle Image">
                                
                                <!-- Listing type badge -->
                                <div class="listing-type-badge listing-type-<?= str_replace('-', '_', $v['listing_type']) ?>">
                                    <i class="fas fa-tag me-1"></i>
                                    <?= strtoupper(str_replace('-', ' ', $v['listing_type'])) ?>
                                </div>
                                
                                <!-- Featured badge if applicable -->
                                <?php if ($v['featured'] && (!$v['featured_until'] || strtotime($v['featured_until']) > time())): ?>
                                    <div class="featured-badge">
                                        <i class="fas fa-fire me-1"></i>FEATURED
                                    </div>
                                <?php endif; ?>
                                
                                <!-- Premium badge if applicable -->
                                <?php if ($v['premium_listing'] && (!$v['premium_until'] || strtotime($v['premium_until']) > time())): ?>
                                    <div class="premium-badge">
                                        <i class="fas fa-crown me-1"></i>PREMIUM
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="card-body">
                                <h5 class="card-title mb-3">
                                    <strong><?php echo htmlspecialchars($v['make'] . ' ' . $v['model']); ?></strong>
                                    <?php if ($v['variant']): ?>
                                        <div class="text-muted" style="font-size: 0.9rem;"><?= htmlspecialchars($v['variant']) ?></div>
                                    <?php endif; ?>
                                </h5>
                                
                                <div class="vehicle-details mb-3">
                                    <?php if ($v['year']): ?>
                                        <div class="mb-2">
                                            <i class="fas fa-calendar text-primary me-2"></i>
                                            <strong>Year:</strong> <?php echo $v['year']; ?>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="mb-2">
                                        <i class="fas fa-dollar-sign text-success me-2"></i>
                                        <strong>Price:</strong> R<?php echo number_format($v['price'], 2); ?>
                                    </div>
                                    
                                    <!-- Enhanced display with hours for machinery -->
                                    <?php if ($v['hours_used'] > 0): ?>
                                        <div class="mb-2">
                                            <span class="hours-display">
                                                <i class="fas fa-clock"></i><?= number_format($v['hours_used']) ?> hours
                                            </span>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="mb-2">
                                        <i class="fas fa-map-marker-alt text-info me-2"></i>
                                        <strong>Location:</strong> <?php echo htmlspecialchars($v['city'] . ', ' . $v['region']); ?>
                                    </div>
                                </div>
                                
                                <!-- FIXED: Enhanced Featured Toggle with Real-time Validation -->
                                <div class="featured-toggle mb-3">
                                    <form method="POST" action="dealer.php" class="featured-form" data-vehicle-id="<?= $v['vehicle_id'] ?>">
                                        <input type="hidden" name="action" value="toggle_featured">
                                        <input type="hidden" name="vehicle_id" value="<?= $v['vehicle_id'] ?>">
                                        <div class="row align-items-center">
                                            <div class="col-8">
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input featured-checkbox" type="checkbox" name="featured" 
                                                           <?= $v['featured'] ? 'checked' : '' ?>
                                                           id="featured_<?= $v['vehicle_id'] ?>"
                                                           data-vehicle-id="<?= $v['vehicle_id'] ?>">
                                                    <label class="form-check-label" for="featured_<?= $v['vehicle_id'] ?>">
                                                        <strong><i class="fas fa-fire me-1"></i>Featured Listing</strong>
                                                    </label>
                                                </div>
                                                <input type="date" class="form-control form-control-sm" name="featured_until" 
                                                       value="<?= $v['featured_until'] ? date('Y-m-d', strtotime($v['featured_until'])) : '' ?>" 
                                                       min="<?= date('Y-m-d') ?>" 
                                                       placeholder="Featured until...">
                                            </div>
                                            <div class="col-4">
                                                <button type="submit" class="btn btn-danger w-100 btn-sm">
                                                    <i class="fas fa-fire"></i>
                                                    Update
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                                
                                <div class="btn-group w-100" role="group">
                                    <a href="?action=view_listing&id=<?php echo $v['vehicle_id']; ?>" class="btn btn-info btn-sm">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="?action=edit_vehicle&id=<?php echo $v['vehicle_id']; ?>" class="btn btn-primary btn-sm">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form method="POST" style="flex: 1;" action="dealer.php">
                                        <input type="hidden" name="vehicle_id" value="<?php echo $v['vehicle_id']; ?>">
                                        <input type="hidden" name="action" value="delete_vehicle">
                                        <button type="submit" class="btn btn-danger btn-sm w-100" onclick="return confirm('Delete this listing permanently?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
            <?php
            break;

        case 'add_vehicle':
            if (($_SESSION['status'] ?? 'active') != 'active') {
                ob_end_clean();
                header("Location: dealer.php");
                exit;
            }

            // Get system years for dropdown
            $stmt = $pdo->query("SELECT year_value FROM system_years WHERE status = 'active' ORDER BY year_value DESC");
            $system_years = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            // If no system years, create default range
            if (empty($system_years)) {
                $current_year = date('Y');
                $system_years = range($current_year + 1, 1900);
            }

            // Get dealer branches
            $stmt = $pdo->prepare("SELECT * FROM dealer_branches WHERE dealer_id = ? AND status = 'active'");
            $stmt->execute([$_SESSION['user_id']]);
            $dealer_branches = $stmt->fetchAll();

            // Get categories for the dropdown
            $stmt = $pdo->query("SELECT category_id, category_key, category_name FROM categories WHERE status = 'active' AND (listing_type = 'sale' OR listing_type IS NULL) ORDER BY category_order, category_name");
            $categories = $stmt->fetchAll();

            // FIXED: Get dealer's featured limit with real-time data
            $featured_info = getDealerFeaturedLimit($pdo, $_SESSION['user_id']);
            ?>
           
            <div class="page-header" data-aos="fade-down">
                <div class="container">
                    <div class="d-flex justify-content-between align-items-center flex-wrap">
                        <div>
                            <h1><i class="fas fa-plus me-3"></i>Professional Listing Creator</h1>
                            <p class="mb-0">Create stunning vehicle listings with our advanced form system</p>
                        </div>
                        <a href="dealer.php" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <div class="row justify-content-center">
                <div class="col-md-12">
                    <div class="card" data-aos="fade-up">
                        <div class="card-header">
                            <h4><i class="fas fa-car me-2"></i>Enhanced Vehicle Listing Form</h4>
                            <p class="mb-0 text-light">Advanced cascading dropdowns with real-time validation</p>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="dealer.php" enctype="multipart/form-data" id="vehicle-form">
                                <input type="hidden" name="action" value="add_vehicle">
                                
                                <!-- Listing Type Selection -->
                                <div class="row mb-4" data-aos="fade-up" data-aos-delay="100">
                                    <div class="col-md-12">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6><i class="fas fa-tags me-2"></i>Listing Type Selection</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-3">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="radio" name="listing_type" value="sale" id="listing_sale" checked>
                                                            <label class="form-check-label w-100" for="listing_sale">
                                                                <i class="fas fa-shopping-cart text-primary me-2"></i>
                                                                <strong>For Sale</strong><br>
                                                                <small class="text-muted">Standard sale listing</small>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="radio" name="listing_type" value="rent-to-own" id="listing_rent">
                                                            <label class="form-check-label w-100" for="listing_rent">
                                                                <i class="fas fa-key text-success me-2"></i>
                                                                <strong>Rent-to-Own</strong><br>
                                                                <small class="text-muted">Rental with ownership option</small>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="radio" name="listing_type" value="hire" id="listing_hire">
                                                            <label class="form-check-label w-100" for="listing_hire">
                                                                <i class="fas fa-calendar text-warning me-2"></i>
                                                                <strong>Hire/Rental</strong><br>
                                                                <small class="text-muted">Short-term rental</small>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="radio" name="listing_type" value="auction" id="listing_auction">
                                                            <label class="form-check-label w-100" for="listing_auction">
                                                                <i class="fas fa-gavel text-danger me-2"></i>
                                                                <strong>Auction</strong><br>
                                                                <small class="text-muted">Bid-based sale</small>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Category and Vehicle Details -->
                                <div class="row" data-aos="fade-up" data-aos-delay="200">
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-tags"></i>Category</label>
                                            <select class="form-control" id="category" name="category" required>
                                                <option value="">Select Category</option>
                                                <?php foreach ($categories as $cat): ?>
                                                    <option value="<?= $cat['category_key'] ?>" data-id="<?= $cat['category_id'] ?>">
                                                        <?= htmlspecialchars($cat['category_name']) ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                            <div class="loading-spinner" id="category-spinner"></div>
                                        </div>
                                    </div>
                               
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-industry"></i>Make</label>
                                            <select class="form-control" id="make" name="make" required disabled>
                                                <option value="">Select Make</option>
                                            </select>
                                            <div class="loading-spinner" id="make-spinner"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-car"></i>Model</label>
                                            <select class="form-control" id="model" name="model" required disabled>
                                                <option value="">Select Model</option>
                                            </select>
                                            <div class="loading-spinner" id="model-spinner"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-cogs"></i>Variant</label>
                                            <select class="form-control" id="variant" name="variant" disabled>
                                                <option value="">Select Variant</option>
                                            </select>
                                            <div class="loading-spinner" id="variant-spinner"></div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Year, Condition, Price, Location -->
                                <div class="row" data-aos="fade-up" data-aos-delay="300">
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-calendar"></i>Year</label>
                                            <select class="form-control" name="year">
                                                <option value="">Select Year</option>
                                                <?php foreach ($system_years as $year): ?>
                                                    <option value="<?= $year ?>"><?= $year ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-star"></i>Condition</label>
                                            <select class="form-control" name="condition_type">
                                                <option value="used">Used</option>
                                                <option value="new">New</option>
                                                <option value="refurbished">Refurbished</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-dollar-sign"></i>Price (R)</label>
                                            <input type="number" class="form-control" name="price" step="0.01" required placeholder="0.00">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-map"></i>Province</label>
                                            <select class="form-control" name="region" required>
                                                <option value="">Select Province</option>
                                                <option value="Eastern Cape">Eastern Cape</option>
                                                <option value="Free State">Free State</option>
                                                <option value="Gauteng">Gauteng</option>
                                                <option value="KwaZulu-Natal">KwaZulu-Natal</option>
                                                <option value="Limpopo">Limpopo</option>
                                                <option value="Mpumalanga">Mpumalanga</option>
                                                <option value="Northern Cape">Northern Cape</option>
                                                <option value="North West">North West</option>
                                                <option value="Western Cape">Western Cape</option>
                                                <option value="Other">Other</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- City, Branch, Mileage, Hours -->
                                <div class="row" data-aos="fade-up" data-aos-delay="400">
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-map-marker-alt"></i>City</label>
                                            <input type="text" class="form-control" name="city" required placeholder="Enter city">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-building"></i>Branch</label>
                                            <select class="form-control" name="branch_id">
                                                <option value="">Select Branch (Optional)</option>
                                                <?php foreach ($dealer_branches as $branch): ?>
                                                    <option value="<?= $branch['branch_id'] ?>"><?= htmlspecialchars($branch['branch_name']) ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3" id="mileage-field">
                                        <div class="mb-3">
                                            <label class="form-label" id="mileage-label"><i class="fas fa-tachometer-alt"></i>Mileage (KM)</label>
                                            <input type="number" class="form-control" name="mileage" placeholder="0">
                                        </div>
                                    </div>
                                    <div class="col-md-3" id="hours-field" style="display: none;">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-clock"></i>Hours Used</label>
                                            <input type="number" class="form-control" name="hours_used" placeholder="0">
                                        </div>
                                    </div>
                                </div>

                                <!-- Engine, Horsepower, Transmission, Fuel -->
                                <div class="row" data-aos="fade-up" data-aos-delay="500">
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-cog"></i>Engine Type</label>
                                            <input type="text" class="form-control" name="engine_type" placeholder="e.g., V8 Turbo">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-horse"></i>Horsepower</label>
                                            <input type="number" class="form-control" name="horsepower" placeholder="0">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-exchange-alt"></i>Transmission</label>
                                            <select class="form-control" name="transmission">
                                                <option value="">Select Transmission</option>
                                                <option value="manual">Manual</option>
                                                <option value="automatic">Automatic</option>
                                                <option value="semi-automatic">Semi-Automatic</option>
                                                <option value="other">Other</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-gas-pump"></i>Fuel Type</label>
                                            <select class="form-control" name="fuel_type">
                                                <option value="">Select Fuel Type</option>
                                                <option value="diesel">Diesel</option>
                                                <option value="petrol">Petrol</option>
                                                <option value="electric">Electric</option>
                                                <option value="hybrid">Hybrid</option>
                                                <option value="lpg">LPG</option>
                                                <option value="cng">CNG</option>
                                                <option value="other">Other</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- Color and Vehicle Identification -->
                                <div class="row" data-aos="fade-up" data-aos-delay="600">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-palette"></i>Color</label>
                                            <input type="text" class="form-control" name="color" placeholder="e.g., White">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-barcode"></i>VIN Number</label>
                                            <input type="text" class="form-control" name="vin_number" placeholder="Optional">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-id-card"></i>Registration Number</label>
                                            <input type="text" class="form-control" name="registration_number" placeholder="Optional">
                                        </div>
                                    </div>
                                </div>

                                <!-- FIXED: Enhanced Featured Listing Option with Real-time Validation -->
                                <div class="featured-toggle mb-4" data-aos="fade-up" data-aos-delay="700">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-check mb-3">
                                                <input class="form-check-input" type="checkbox" name="featured" id="featured_checkbox_new">
                                                <label class="form-check-label" for="featured_checkbox_new">
                                                    <strong><i class="fas fa-fire me-2"></i>Make this a Featured Listing</strong><br>
                                                    <small class="text-muted">Enhanced visibility and priority placement</small>
                                                </label>
                                            </div>
                                            <div class="featured-limit-info" id="new-listing-featured-info" style="font-size: 0.9rem;">
                                                <i class="fas fa-info-circle me-1"></i>
                                                Featured slots: <span id="current-count-display"><?= $featured_info['current'] ?></span> / 
                                                <span id="limit-display"><?= $featured_info['limit'] == -1 ? 'Unlimited' : $featured_info['limit'] ?></span>
                                                <?php if ($featured_info['limit'] != -1): ?>
                                                    <br><small>Remaining: <span id="remaining-display"><?= max(0, $featured_info['limit'] - $featured_info['current']) ?></span></small>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">Featured Until (Optional)</label>
                                            <input type="date" class="form-control" name="featured_until" 
                                                   min="<?= date('Y-m-d') ?>" 
                                                   value="<?= date('Y-m-d', strtotime('+30 days')) ?>">
                                            <small class="text-muted">Leave empty for unlimited featured status</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Hire/Auction Specific Fields -->
                                <div id="hire-fields" style="display: none;" data-aos="fade-up" data-aos-delay="800">
                                    <div class="card mb-4">
                                        <div class="card-header">
                                            <h6><i class="fas fa-calendar-alt me-2"></i>Hire/Rental Rates</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label class="form-label">Daily Rate (R)</label>
                                                        <input type="number" class="form-control" name="daily_rate" step="0.01" placeholder="0.00">
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label class="form-label">Weekly Rate (R)</label>
                                                        <input type="number" class="form-control" name="weekly_rate" step="0.01" placeholder="0.00">
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label class="form-label">Monthly Rate (R)</label>
                                                        <input type="number" class="form-control" name="monthly_rate" step="0.01" placeholder="0.00">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div id="auction-fields" style="display: none;" data-aos="fade-up" data-aos-delay="900">
                                    <div class="card mb-4">
                                        <div class="card-header">
                                            <h6><i class="fas fa-gavel me-2"></i>Auction Details</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label class="form-label">Auction Start Date</label>
                                                        <input type="datetime-local" class="form-control" name="auction_start_date">
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label class="form-label">Auction End Date</label>
                                                        <input type="datetime-local" class="form-control" name="auction_end_date">
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label class="form-label">Reserve Price (R)</label>
                                                        <input type="number" class="form-control" name="reserve_price" step="0.01" placeholder="0.00">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Description Section -->
                                <div class="card mb-4" data-aos="fade-up" data-aos-delay="1000">
                                    <div class="card-header">
                                        <h6><i class="fas fa-align-left me-2"></i>Description & Features</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">Detailed Description</label>
                                                    <textarea class="form-control" id="description" name="description" rows="6" maxlength="3000" oninput="updateCharCounter(this)" placeholder="Describe your vehicle in detail..."></textarea>
                                                    <div id="char-counter" class="char-counter">3000 characters remaining</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">Key Features</label>
                                                    <textarea class="form-control" name="features" rows="6" placeholder="List key features, one per line..."></textarea>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="mb-3">
                                                    <label class="form-label"><i class="fab fa-youtube"></i>YouTube Video URL</label>
                                                    <input type="url" class="form-control" name="youtube_video_url" placeholder="https://www.youtube.com/watch?v=...">
                                                    <small class="text-muted">Add a YouTube link to showcase your vehicle</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Enhanced File Upload Section -->
                                <div class="card mb-4" data-aos="fade-up" data-aos-delay="1100">
                                    <div class="card-header">
                                        <h6><i class="fas fa-camera me-2"></i>Professional Media Upload</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="file-upload-area">
                                                    <i class="fas fa-images fa-3x mb-3"></i>
                                                    <h6><strong>Upload Photos</strong></h6>
                                                    <input type="file" class="form-control" id="images" name="images[]" multiple accept="image/*,.webp">
                                                    <div class="file-upload-limits">
                                                        <strong>Max:</strong> 40 photos, 2MB each<br>
                                                        <strong>Formats:</strong> JPG, PNG, GIF, WebP
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="file-upload-area">
                                                    <i class="fas fa-video fa-3x mb-3"></i>
                                                    <h6><strong>Upload Videos</strong></h6>
                                                    <input type="file" class="form-control" id="videos" name="videos[]" multiple accept="video/*">
                                                    <div class="file-upload-limits">
                                                        <strong>Max:</strong> 5 videos, 50MB each<br>
                                                        <strong>Formats:</strong> MP4, AVI, MOV, WMV, FLV
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="file-upload-area">
                                                    <i class="fas fa-file-pdf fa-3x mb-3"></i>
                                                    <h6><strong>Upload Documents</strong></h6>
                                                    <input type="file" class="form-control" id="documents" name="documents[]" multiple accept=".pdf">
                                                    <div class="file-upload-limits">
                                                        <strong>Max:</strong> 10MB each<br>
                                                        <strong>Format:</strong> PDF only
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Additional Features -->
                                <div class="card mb-4" data-aos="fade-up" data-aos-delay="1200">
                                    <div class="card-header">
                                        <h6><i class="fas fa-star me-2"></i>Additional Features & Benefits</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="no_accidents" id="no_accidents">
                                                    <label class="form-check-label w-100" for="no_accidents">
                                                        <i class="fas fa-shield-alt text-success me-2"></i>
                                                        <strong>No Accidents</strong><br>
                                                        <small class="text-muted">Clean accident history</small>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="warranty" id="warranty">
                                                    <label class="form-check-label w-100" for="warranty">
                                                        <i class="fas fa-certificate text-primary me-2"></i>
                                                        <strong>Warranty</strong><br>
                                                        <small class="text-muted">Warranty included</small>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="finance_available" id="finance_available">
                                                    <label class="form-check-label w-100" for="finance_available">
                                                        <i class="fas fa-credit-card text-info me-2"></i>
                                                        <strong>Finance Available</strong><br>
                                                        <small class="text-muted">Financing options</small>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="trade_in" id="trade_in">
                                                    <label class="form-check-label w-100" for="trade_in">
                                                        <i class="fas fa-exchange-alt text-warning me-2"></i>
                                                        <strong>Trade In</strong><br>
                                                        <small class="text-muted">Trade-in accepted</small>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="service_history" id="service_history">
                                                    <label class="form-check-label w-100" for="service_history">
                                                        <i class="fas fa-wrench text-secondary me-2"></i>
                                                        <strong>Service History</strong><br>
                                                        <small class="text-muted">Full service records</small>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="roadworthy" id="roadworthy">
                                                    <label class="form-check-label w-100" for="roadworthy">
                                                        <i class="fas fa-road text-success me-2"></i>
                                                        <strong>Roadworthy</strong><br>
                                                        <small class="text-muted">Roadworthy certificate</small>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-4" data-aos="fade-up" data-aos-delay="1300">
                                    <div class="d-flex gap-3 justify-content-center">
                                        <button type="submit" class="btn btn-primary btn-lg px-5">
                                            <i class="fas fa-plus me-2"></i>Create Professional Listing
                                        </button>
                                        <a href="dealer.php" class="btn btn-outline-secondary btn-lg px-5">
                                            <i class="fas fa-times me-2"></i>Cancel
                                        </a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <?php
            break;

        case 'view_listing':
            $vehicle_id = (int)($_GET['id'] ?? 0);
            
            // Get vehicle details with verification of ownership
            $stmt = $pdo->prepare("
                SELECT v.*, c.category_name, s.subcategory_name,
                       cm.make_name, cmod.model_name, cv.variant_name
                FROM vehicles v 
                LEFT JOIN categories c ON v.category = c.category_key
                LEFT JOIN subcategories s ON (s.category_id = c.category_id AND v.subcategory = s.subcategory_key)
                LEFT JOIN category_makes cm ON v.make = cm.make_name
                LEFT JOIN category_models cmod ON v.model = cmod.model_name
                LEFT JOIN category_variants cv ON v.variant = cv.variant_name
                WHERE v.vehicle_id = ? AND v.dealer_id = ?
            ");
            $stmt->execute([$vehicle_id, $_SESSION['user_id']]);
            $vehicle = $stmt->fetch();
            
            if (!$vehicle) {
                ob_end_clean();
                header("Location: dealer.php?error=vehicle_not_found");
                exit;
            }
            
            // Get vehicle images
            $stmt = $pdo->prepare("SELECT * FROM vehicle_images WHERE vehicle_id = ? ORDER BY is_primary DESC, image_order");
            $stmt->execute([$vehicle_id]);
            $vehicle_images = $stmt->fetchAll();
            
            // Get vehicle videos
            $stmt = $pdo->prepare("SELECT * FROM vehicle_videos WHERE vehicle_id = ? ORDER BY video_id");
            $stmt->execute([$vehicle_id]);
            $vehicle_videos = $stmt->fetchAll();
            
            // Get vehicle documents
            $stmt = $pdo->prepare("SELECT * FROM vehicle_documents WHERE vehicle_id = ? ORDER BY document_id");
            $stmt->execute([$vehicle_id]);
            $vehicle_documents = $stmt->fetchAll();
            ?>
            
            <div class="page-header" data-aos="fade-down">
                <div class="container">
                    <div class="d-flex justify-content-between align-items-center flex-wrap">
                        <div>
                            <h1><i class="fas fa-eye me-3"></i>Vehicle Details</h1>
                            <p class="mb-0"><?= htmlspecialchars($vehicle['make'] . ' ' . $vehicle['model']) ?></p>
                        </div>
                        <div class="btn-group">
                            <a href="dealer.php" class="btn btn-outline-light">
                                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                            </a>
                            <a href="?action=edit_vehicle&id=<?= $vehicle_id ?>" class="btn btn-warning">
                                <i class="fas fa-edit me-2"></i>Edit Listing
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Vehicle Images -->
                <div class="col-md-8 mb-4" data-aos="fade-right">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-images me-2"></i>Photos (<?= count($vehicle_images) ?>)</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($vehicle_images)): ?>
                                <div class="empty-state">
                                    <i class="fas fa-image fa-3x mb-3"></i>
                                    <h4>No Photos</h4>
                                    <p>No photos have been uploaded for this vehicle.</p>
                                </div>
                            <?php else: ?>
                                <div class="row">
                                    <?php foreach ($vehicle_images as $index => $image): ?>
                                    <div class="col-md-4 mb-3">
                                        <div class="position-relative">
                                            <img src="<?= htmlspecialchars($image['image_path']) ?>" 
                                                 class="img-fluid rounded" 
                                                 alt="Vehicle Image" 
                                                 style="height: 200px; width: 100%; object-fit: cover;">
                                            <?php if ($image['is_primary']): ?>
                                                <span class="badge bg-primary position-absolute top-0 start-0 m-2">Primary</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Vehicle Information -->
                <div class="col-md-4 mb-4" data-aos="fade-left">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-info-circle me-2"></i>Vehicle Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <strong>Category:</strong> <?= htmlspecialchars($vehicle['category_name'] ?? $vehicle['category']) ?>
                            </div>
                            <?php if ($vehicle['subcategory_name']): ?>
                            <div class="mb-3">
                                <strong>Subcategory:</strong> <?= htmlspecialchars($vehicle['subcategory_name']) ?>
                            </div>
                            <?php endif; ?>
                            <div class="mb-3">
                                <strong>Make:</strong> <?= htmlspecialchars($vehicle['make']) ?>
                            </div>
                            <div class="mb-3">
                                <strong>Model:</strong> <?= htmlspecialchars($vehicle['model']) ?>
                            </div>
                            <?php if ($vehicle['variant']): ?>
                            <div class="mb-3">
                                <strong>Variant:</strong> <?= htmlspecialchars($vehicle['variant']) ?>
                            </div>
                            <?php endif; ?>
                            <?php if ($vehicle['year']): ?>
                            <div class="mb-3">
                                <strong>Year:</strong> <?= htmlspecialchars($vehicle['year']) ?>
                            </div>
                            <?php endif; ?>
                            <div class="mb-3">
                                <strong>Condition:</strong> <?= ucfirst($vehicle['condition_type']) ?>
                            </div>
                            <div class="mb-3">
                                <strong>Price:</strong> R<?= number_format($vehicle['price'], 2) ?>
                            </div>
                            <?php if ($vehicle['mileage']): ?>
                            <div class="mb-3">
                                <strong>Mileage:</strong> <?= number_format($vehicle['mileage']) ?> km
                            </div>
                            <?php endif; ?>
                            <?php if ($vehicle['hours_used']): ?>
                            <div class="mb-3">
                                <strong>Hours:</strong> <?= number_format($vehicle['hours_used']) ?> hours
                            </div>
                            <?php endif; ?>
                            <div class="mb-3">
                                <strong>Location:</strong> <?= htmlspecialchars($vehicle['city'] . ', ' . $vehicle['region']) ?>
                            </div>
                            <div class="mb-3">
                                <strong>Listing Type:</strong> 
                                <span class="badge bg-primary"><?= ucfirst(str_replace('-', ' ', $vehicle['listing_type'])) ?></span>
                            </div>
                            <?php if ($vehicle['featured']): ?>
                            <div class="mb-3">
                                <span class="badge bg-danger">
                                    <i class="fas fa-fire me-1"></i>FEATURED
                                </span>
                                <?php if ($vehicle['featured_until']): ?>
                                    <br><small>Until: <?= date('Y-m-d', strtotime($vehicle['featured_until'])) ?></small>
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Additional Features -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5><i class="fas fa-star me-2"></i>Features</h5>
                        </div>
                        <div class="card-body">
                            <?php
                            $features = [];
                            if ($vehicle['no_accidents']) $features[] = '<i class="fas fa-shield-alt text-success me-2"></i>No Accidents';
                            if ($vehicle['warranty']) $features[] = '<i class="fas fa-certificate text-primary me-2"></i>Warranty';
                            if ($vehicle['finance_available']) $features[] = '<i class="fas fa-credit-card text-info me-2"></i>Finance Available';
                            if ($vehicle['trade_in']) $features[] = '<i class="fas fa-exchange-alt text-warning me-2"></i>Trade In';
                            if ($vehicle['service_history']) $features[] = '<i class="fas fa-wrench text-secondary me-2"></i>Service History';
                            if ($vehicle['roadworthy']) $features[] = '<i class="fas fa-road text-success me-2"></i>Roadworthy';
                            ?>
                            
                            <?php if (!empty($features)): ?>
                                <?php foreach ($features as $feature): ?>
                                    <div class="mb-2"><?= $feature ?></div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <p class="text-muted">No additional features specified.</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Description -->
                <?php if ($vehicle['description']): ?>
                <div class="col-12 mb-4" data-aos="fade-up">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-align-left me-2"></i>Description</h5>
                        </div>
                        <div class="card-body">
                            <p><?= nl2br(htmlspecialchars($vehicle['description'])) ?></p>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Key Features -->
                <?php if ($vehicle['features']): ?>
                <div class="col-12 mb-4" data-aos="fade-up">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-list me-2"></i>Key Features</h5>
                        </div>
                        <div class="card-body">
                            <pre><?= htmlspecialchars($vehicle['features']) ?></pre>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Videos -->
                <?php if (!empty($vehicle_videos)): ?>
                <div class="col-12 mb-4" data-aos="fade-up">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-video me-2"></i>Videos (<?= count($vehicle_videos) ?>)</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <?php foreach ($vehicle_videos as $video): ?>
                                <div class="col-md-6 mb-3">
                                    <video controls class="w-100 rounded" style="max-height: 300px;">
                                        <source src="<?= htmlspecialchars($video['video_path']) ?>" type="video/mp4">
                                        Your browser does not support the video tag.
                                    </video>
                                    <p class="mt-2"><?= htmlspecialchars($video['video_title']) ?></p>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Documents -->
                <?php if (!empty($vehicle_documents)): ?>
                <div class="col-12 mb-4" data-aos="fade-up">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-file-pdf me-2"></i>Documents (<?= count($vehicle_documents) ?>)</h5>
                        </div>
                        <div class="card-body">
                            <div class="list-group">
                                <?php foreach ($vehicle_documents as $document): ?>
                                <a href="<?= htmlspecialchars($document['document_path']) ?>" 
                                   class="list-group-item list-group-item-action d-flex justify-content-between align-items-center"
                                   target="_blank">
                                    <div>
                                        <i class="fas fa-file-pdf text-danger me-2"></i>
                                        <?= htmlspecialchars($document['document_name']) ?>
                                    </div>
                                    <span class="badge bg-secondary"><?= round($document['file_size'] / 1024 / 1024, 2) ?>MB</span>
                                </a>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
            <?php
            break;

        case 'edit_vehicle':
            $vehicle_id = (int)($_GET['id'] ?? 0);
            
            // Get vehicle details with verification of ownership
            $stmt = $pdo->prepare("
                SELECT v.*, c.category_id
                FROM vehicles v 
                LEFT JOIN categories c ON v.category = c.category_key
                WHERE v.vehicle_id = ? AND v.dealer_id = ?
            ");
            $stmt->execute([$vehicle_id, $_SESSION['user_id']]);
            $vehicle = $stmt->fetch();
            
            if (!$vehicle) {
                ob_end_clean();
                header("Location: dealer.php?error=vehicle_not_found");
                exit;
            }

            // Get system years for dropdown
            $stmt = $pdo->query("SELECT year_value FROM system_years WHERE status = 'active' ORDER BY year_value DESC");
            $system_years = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            // If no system years, create default range
            if (empty($system_years)) {
                $current_year = date('Y');
                $system_years = range($current_year + 1, 1900);
            }

            // Get dealer branches
            $stmt = $pdo->prepare("SELECT * FROM dealer_branches WHERE dealer_id = ? AND status = 'active'");
            $stmt->execute([$_SESSION['user_id']]);
            $dealer_branches = $stmt->fetchAll();

            // Get categories for the dropdown
            $stmt = $pdo->query("SELECT category_id, category_key, category_name FROM categories WHERE status = 'active' ORDER BY category_order, category_name");
            $categories = $stmt->fetchAll();

            // Get dealer's featured limit
            $featured_info = getDealerFeaturedLimit($pdo, $_SESSION['user_id']);
            ?>
           
            <div class="page-header" data-aos="fade-down">
                <div class="container">
                    <div class="d-flex justify-content-between align-items-center flex-wrap">
                        <div>
                            <h1><i class="fas fa-edit me-3"></i>Edit Vehicle Listing</h1>
                            <p class="mb-0">Update your vehicle listing details</p>
                        </div>
                        <div class="btn-group">
                            <a href="dealer.php" class="btn btn-outline-light">
                                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                            </a>
                            <a href="?action=view_listing&id=<?= $vehicle_id ?>" class="btn btn-info">
                                <i class="fas fa-eye me-2"></i>View Listing
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row justify-content-center">
                <div class="col-md-12">
                    <div class="card" data-aos="fade-up">
                        <div class="card-header">
                            <h4><i class="fas fa-car me-2"></i>Edit Vehicle Listing Form</h4>
                            <p class="mb-0 text-light">Update your vehicle details</p>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="dealer.php" enctype="multipart/form-data" id="edit-vehicle-form">
                                <input type="hidden" name="action" value="update_vehicle">
                                <input type="hidden" name="vehicle_id" value="<?= $vehicle_id ?>">
                                
                                <!-- Listing Type Selection -->
                                <div class="row mb-4" data-aos="fade-up" data-aos-delay="100">
                                    <div class="col-md-12">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6><i class="fas fa-tags me-2"></i>Listing Type Selection</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-3">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="radio" name="listing_type" value="sale" id="edit_listing_sale" <?= $vehicle['listing_type'] === 'sale' ? 'checked' : '' ?>>
                                                            <label class="form-check-label w-100" for="edit_listing_sale">
                                                                <i class="fas fa-shopping-cart text-primary me-2"></i>
                                                                <strong>For Sale</strong><br>
                                                                <small class="text-muted">Standard sale listing</small>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="radio" name="listing_type" value="rent-to-own" id="edit_listing_rent" <?= $vehicle['listing_type'] === 'rent-to-own' ? 'checked' : '' ?>>
                                                            <label class="form-check-label w-100" for="edit_listing_rent">
                                                                <i class="fas fa-key text-success me-2"></i>
                                                                <strong>Rent-to-Own</strong><br>
                                                                <small class="text-muted">Rental with ownership option</small>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="radio" name="listing_type" value="hire" id="edit_listing_hire" <?= $vehicle['listing_type'] === 'hire' ? 'checked' : '' ?>>
                                                            <label class="form-check-label w-100" for="edit_listing_hire">
                                                                <i class="fas fa-calendar text-warning me-2"></i>
                                                                <strong>Hire/Rental</strong><br>
                                                                <small class="text-muted">Short-term rental</small>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="radio" name="listing_type" value="auction" id="edit_listing_auction" <?= $vehicle['listing_type'] === 'auction' ? 'checked' : '' ?>>
                                                            <label class="form-check-label w-100" for="edit_listing_auction">
                                                                <i class="fas fa-gavel text-danger me-2"></i>
                                                                <strong>Auction</strong><br>
                                                                <small class="text-muted">Bid-based sale</small>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Basic Vehicle Details -->
                                <div class="row" data-aos="fade-up" data-aos-delay="200">
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-tags"></i>Category</label>
                                            <select class="form-control" id="edit_category" name="category" required>
                                                <option value="">Select Category</option>
                                                <?php foreach ($categories as $cat): ?>
                                                    <option value="<?= $cat['category_key'] ?>" 
                                                            data-id="<?= $cat['category_id'] ?>"
                                                            <?= $vehicle['category'] === $cat['category_key'] ? 'selected' : '' ?>>
                                                        <?= htmlspecialchars($cat['category_name']) ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-industry"></i>Make</label>
                                            <input type="text" class="form-control" name="make" value="<?= htmlspecialchars($vehicle['make']) ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-car"></i>Model</label>
                                            <input type="text" class="form-control" name="model" value="<?= htmlspecialchars($vehicle['model']) ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-cogs"></i>Variant</label>
                                            <input type="text" class="form-control" name="variant" value="<?= htmlspecialchars($vehicle['variant']) ?>">
                                        </div>
                                    </div>
                                </div>

                                <!-- Year, Condition, Price, Location -->
                                <div class="row" data-aos="fade-up" data-aos-delay="300">
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-calendar"></i>Year</label>
                                            <select class="form-control" name="year">
                                                <option value="">Select Year</option>
                                                <?php foreach ($system_years as $year): ?>
                                                    <option value="<?= $year ?>" <?= $vehicle['year'] == $year ? 'selected' : '' ?>><?= $year ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-star"></i>Condition</label>
                                            <select class="form-control" name="condition_type">
                                                <option value="used" <?= $vehicle['condition_type'] === 'used' ? 'selected' : '' ?>>Used</option>
                                                <option value="new" <?= $vehicle['condition_type'] === 'new' ? 'selected' : '' ?>>New</option>
                                                <option value="refurbished" <?= $vehicle['condition_type'] === 'refurbished' ? 'selected' : '' ?>>Refurbished</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-dollar-sign"></i>Price (R)</label>
                                            <input type="number" class="form-control" name="price" step="0.01" value="<?= $vehicle['price'] ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-map"></i>Province</label>
                                            <select class="form-control" name="region" required>
                                                <option value="">Select Province</option>
                                                <option value="Eastern Cape" <?= $vehicle['region'] === 'Eastern Cape' ? 'selected' : '' ?>>Eastern Cape</option>
                                                <option value="Free State" <?= $vehicle['region'] === 'Free State' ? 'selected' : '' ?>>Free State</option>
                                                <option value="Gauteng" <?= $vehicle['region'] === 'Gauteng' ? 'selected' : '' ?>>Gauteng</option>
                                                <option value="KwaZulu-Natal" <?= $vehicle['region'] === 'KwaZulu-Natal' ? 'selected' : '' ?>>KwaZulu-Natal</option>
                                                <option value="Limpopo" <?= $vehicle['region'] === 'Limpopo' ? 'selected' : '' ?>>Limpopo</option>
                                                <option value="Mpumalanga" <?= $vehicle['region'] === 'Mpumalanga' ? 'selected' : '' ?>>Mpumalanga</option>
                                                <option value="Northern Cape" <?= $vehicle['region'] === 'Northern Cape' ? 'selected' : '' ?>>Northern Cape</option>
                                                <option value="North West" <?= $vehicle['region'] === 'North West' ? 'selected' : '' ?>>North West</option>
                                                <option value="Western Cape" <?= $vehicle['region'] === 'Western Cape' ? 'selected' : '' ?>>Western Cape</option>
                                                <option value="Other" <?= $vehicle['region'] === 'Other' ? 'selected' : '' ?>>Other</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- City, Mileage, Hours, Color -->
                                <div class="row" data-aos="fade-up" data-aos-delay="400">
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-map-marker-alt"></i>City</label>
                                            <input type="text" class="form-control" name="city" value="<?= htmlspecialchars($vehicle['city']) ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-tachometer-alt"></i>Mileage (KM)</label>
                                            <input type="number" class="form-control" name="mileage" value="<?= $vehicle['mileage'] ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-clock"></i>Hours Used</label>
                                            <input type="number" class="form-control" name="hours_used" value="<?= $vehicle['hours_used'] ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-palette"></i>Color</label>
                                            <input type="text" class="form-control" name="color" value="<?= htmlspecialchars($vehicle['color']) ?>">
                                        </div>
                                    </div>
                                </div>

                                <!-- Enhanced Featured Listing Option -->
                                <div class="featured-toggle mb-4" data-aos="fade-up" data-aos-delay="500">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-check mb-3">
                                                <input class="form-check-input" type="checkbox" name="featured" 
                                                       id="edit_featured_checkbox" <?= $vehicle['featured'] ? 'checked' : '' ?>>
                                                <label class="form-check-label" for="edit_featured_checkbox">
                                                    <strong><i class="fas fa-fire me-2"></i>Featured Listing</strong><br>
                                                    <small class="text-muted">Enhanced visibility and priority placement</small>
                                                </label>
                                            </div>
                                            <div class="featured-limit-info" style="font-size: 0.9rem;">
                                                <i class="fas fa-info-circle me-1"></i>
                                                Featured slots: <?= $featured_info['current'] ?> / <?= $featured_info['limit'] == -1 ? 'Unlimited' : $featured_info['limit'] ?>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">Featured Until (Optional)</label>
                                            <input type="date" class="form-control" name="featured_until" 
                                                   min="<?= date('Y-m-d') ?>" 
                                                   value="<?= $vehicle['featured_until'] ? date('Y-m-d', strtotime($vehicle['featured_until'])) : '' ?>">
                                            <small class="text-muted">Leave empty for unlimited featured status</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Description Section -->
                                <div class="card mb-4" data-aos="fade-up" data-aos-delay="600">
                                    <div class="card-header">
                                        <h6><i class="fas fa-align-left me-2"></i>Description & Features</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">Detailed Description</label>
                                                    <textarea class="form-control" id="edit_description" name="description" rows="6" maxlength="3000" oninput="updateCharCounter(this)"><?= htmlspecialchars($vehicle['description']) ?></textarea>
                                                    <div id="edit-char-counter" class="char-counter">3000 characters remaining</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">Key Features</label>
                                                    <textarea class="form-control" name="features" rows="6"><?= htmlspecialchars($vehicle['features']) ?></textarea>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="mb-3">
                                                    <label class="form-label"><i class="fab fa-youtube"></i>YouTube Video URL</label>
                                                    <input type="url" class="form-control" name="youtube_video_url" value="<?= htmlspecialchars($vehicle['youtube_video_url']) ?>">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Additional Features -->
                                <div class="card mb-4" data-aos="fade-up" data-aos-delay="700">
                                    <div class="card-header">
                                        <h6><i class="fas fa-star me-2"></i>Additional Features & Benefits</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="no_accidents" id="edit_no_accidents" <?= $vehicle['no_accidents'] ? 'checked' : '' ?>>
                                                    <label class="form-check-label w-100" for="edit_no_accidents">
                                                        <i class="fas fa-shield-alt text-success me-2"></i>
                                                        <strong>No Accidents</strong><br>
                                                        <small class="text-muted">Clean accident history</small>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="warranty" id="edit_warranty" <?= $vehicle['warranty'] ? 'checked' : '' ?>>
                                                    <label class="form-check-label w-100" for="edit_warranty">
                                                        <i class="fas fa-certificate text-primary me-2"></i>
                                                        <strong>Warranty</strong><br>
                                                        <small class="text-muted">Warranty included</small>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="finance_available" id="edit_finance_available" <?= $vehicle['finance_available'] ? 'checked' : '' ?>>
                                                    <label class="form-check-label w-100" for="edit_finance_available">
                                                        <i class="fas fa-credit-card text-info me-2"></i>
                                                        <strong>Finance Available</strong><br>
                                                        <small class="text-muted">Financing options</small>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="trade_in" id="edit_trade_in" <?= $vehicle['trade_in'] ? 'checked' : '' ?>>
                                                    <label class="form-check-label w-100" for="edit_trade_in">
                                                        <i class="fas fa-exchange-alt text-warning me-2"></i>
                                                        <strong>Trade In</strong><br>
                                                        <small class="text-muted">Trade-in accepted</small>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="service_history" id="edit_service_history" <?= $vehicle['service_history'] ? 'checked' : '' ?>>
                                                    <label class="form-check-label w-100" for="edit_service_history">
                                                        <i class="fas fa-wrench text-secondary me-2"></i>
                                                        <strong>Service History</strong><br>
                                                        <small class="text-muted">Full service records</small>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="roadworthy" id="edit_roadworthy" <?= $vehicle['roadworthy'] ? 'checked' : '' ?>>
                                                    <label class="form-check-label w-100" for="edit_roadworthy">
                                                        <i class="fas fa-road text-success me-2"></i>
                                                        <strong>Roadworthy</strong><br>
                                                        <small class="text-muted">Roadworthy certificate</small>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-4" data-aos="fade-up" data-aos-delay="800">
                                    <div class="d-flex gap-3 justify-content-center">
                                        <button type="submit" class="btn btn-primary btn-lg px-5">
                                            <i class="fas fa-save me-2"></i>Update Vehicle Listing
                                        </button>
                                        <a href="dealer.php" class="btn btn-outline-secondary btn-lg px-5">
                                            <i class="fas fa-times me-2"></i>Cancel
                                        </a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <?php
            break;

        case 'manage_branches':
            // Get dealer's branches
            $stmt = $pdo->prepare("SELECT * FROM dealer_branches WHERE dealer_id = ? ORDER BY is_main_branch DESC, branch_name");
            $stmt->execute([$_SESSION['user_id']]);
            $branches = $stmt->fetchAll();
            
            // Get main branch
            $stmt = $pdo->prepare("SELECT branch_id FROM dealer_branches WHERE dealer_id = ? AND is_main_branch = 1");
            $stmt->execute([$_SESSION['user_id']]);
            $main_branch_id = $stmt->fetchColumn();
            ?>
            
            <div class="page-header" data-aos="fade-down">
                <div class="container">
                    <div class="d-flex justify-content-between align-items-center flex-wrap">
                        <div>
                            <h1><i class="fas fa-store me-3"></i>Branch Management</h1>
                            <p class="mb-0">Manage your dealership branches and locations</p>
                        </div>
                        <a href="dealer.php" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4" data-aos="fade-right">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-plus-circle me-2"></i>Add New Branch</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="dealer.php">
                                <input type="hidden" name="action" value="add_branch">
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-store"></i>Branch Name</label>
                                    <input type="text" class="form-control" name="branch_name" required placeholder="e.g., Main Branch">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-map-marker-alt"></i>Address</label>
                                    <textarea class="form-control" name="address" rows="3" required placeholder="Full physical address"></textarea>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-city"></i>City</label>
                                            <input type="text" class="form-control" name="city" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-map"></i>Province</label>
                                            <select class="form-control" name="region" required>
                                                <option value="">Select Province</option>
                                                <option value="Eastern Cape">Eastern Cape</option>
                                                <option value="Free State">Free State</option>
                                                <option value="Gauteng">Gauteng</option>
                                                <option value="KwaZulu-Natal">KwaZulu-Natal</option>
                                                <option value="Limpopo">Limpopo</option>
                                                <option value="Mpumalanga">Mpumalanga</option>
                                                <option value="Northern Cape">Northern Cape</option>
                                                <option value="North West">North West</option>
                                                <option value="Western Cape">Western Cape</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-mail-bulk"></i>Postal Code</label>
                                    <input type="text" class="form-control" name="postal_code" placeholder="e.g., 2000">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-phone"></i>Phone</label>
                                    <input type="tel" class="form-control" name="phone" placeholder="Phone number">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-envelope"></i>Email</label>
                                    <input type="email" class="form-control" name="email" placeholder="Email address">
                                </div>
                                
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="is_main_branch" id="is_main_branch">
                                    <label class="form-check-label" for="is_main_branch">
                                        <strong>Set as Main Branch</strong><br>
                                        <small class="text-muted">This will be your primary business location</small>
                                    </label>
                                </div>
                                
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-save me-2"></i>Save Branch
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-8" data-aos="fade-left">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-list me-2"></i>Your Branches (<?= count($branches) ?>)</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($branches)): ?>
                                <div class="empty-state">
                                    <i class="fas fa-store-alt-slash fa-3x mb-3"></i>
                                    <h4>No Branches Added Yet</h4>
                                    <p>Add your first branch using the form on the left.</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Branch</th>
                                                <th>Location</th>
                                                <th>Contact</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($branches as $branch): ?>
                                            <tr>
                                                <td>
                                                    <strong><?= htmlspecialchars($branch['branch_name']) ?></strong>
                                                    <?php if ($branch['is_main_branch']): ?>
                                                        <span class="badge bg-primary ms-2">Main</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?= htmlspecialchars($branch['city']) ?>, <?= htmlspecialchars($branch['region']) ?>
                                                    <?php if ($branch['postal_code']): ?>
                                                        <br><small class="text-muted"><?= htmlspecialchars($branch['postal_code']) ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($branch['phone']): ?>
                                                        <div><i class="fas fa-phone me-2"></i><?= htmlspecialchars($branch['phone']) ?></div>
                                                    <?php endif; ?>
                                                    <?php if ($branch['email']): ?>
                                                        <div><i class="fas fa-envelope me-2"></i><?= htmlspecialchars($branch['email']) ?></div>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="badge bg-<?= $branch['status'] === 'active' ? 'success' : 'secondary' ?>">
                                                        <?= ucfirst($branch['status']) ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#editBranchModal<?= $branch['branch_id'] ?>">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <form method="POST" action="dealer.php" style="display:inline;">
                                                            <input type="hidden" name="action" value="delete_branch">
                                                            <input type="hidden" name="branch_id" value="<?= $branch['branch_id'] ?>">
                                                            <button type="submit" class="btn btn-outline-danger" onclick="return confirm('Delete this branch?')">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </form>
                                                    </div>
                                                    
                                                    <!-- Edit Branch Modal -->
                                                    <div class="modal fade" id="editBranchModal<?= $branch['branch_id'] ?>" tabindex="-1" aria-hidden="true">
                                                        <div class="modal-dialog">
                                                            <div class="modal-content">
                                                                <div class="modal-header">
                                                                    <h5 class="modal-title">Edit Branch</h5>
                                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                </div>
                                                                <form method="POST" action="dealer.php">
                                                                    <input type="hidden" name="action" value="update_branch">
                                                                    <input type="hidden" name="branch_id" value="<?= $branch['branch_id'] ?>">
                                                                    <div class="modal-body">
                                                                        <div class="mb-3">
                                                                            <label class="form-label">Branch Name</label>
                                                                            <input type="text" class="form-control" name="branch_name" value="<?= htmlspecialchars($branch['branch_name']) ?>" required>
                                                                        </div>
                                                                        
                                                                        <div class="mb-3">
                                                                            <label class="form-label">Address</label>
                                                                            <textarea class="form-control" name="address" rows="3" required><?= htmlspecialchars($branch['address']) ?></textarea>
                                                                        </div>
                                                                        
                                                                        <div class="row">
                                                                            <div class="col-md-6">
                                                                                <div class="mb-3">
                                                                                    <label class="form-label">City</label>
                                                                                    <input type="text" class="form-control" name="city" value="<?= htmlspecialchars($branch['city']) ?>" required>
                                                                                </div>
                                                                            </div>
                                                                            <div class="col-md-6">
                                                                                <div class="mb-3">
                                                                                    <label class="form-label">Province</label>
                                                                                    <select class="form-control" name="region" required>
                                                                                        <option value="Eastern Cape" <?= $branch['region'] === 'Eastern Cape' ? 'selected' : '' ?>>Eastern Cape</option>
                                                                                        <option value="Free State" <?= $branch['region'] === 'Free State' ? 'selected' : '' ?>>Free State</option>
                                                                                        <option value="Gauteng" <?= $branch['region'] === 'Gauteng' ? 'selected' : '' ?>>Gauteng</option>
                                                                                        <option value="KwaZulu-Natal" <?= $branch['region'] === 'KwaZulu-Natal' ? 'selected' : '' ?>>KwaZulu-Natal</option>
                                                                                        <option value="Limpopo" <?= $branch['region'] === 'Limpopo' ? 'selected' : '' ?>>Limpopo</option>
                                                                                        <option value="Mpumalanga" <?= $branch['region'] === 'Mpumalanga' ? 'selected' : '' ?>>Mpumalanga</option>
                                                                                        <option value="Northern Cape" <?= $branch['region'] === 'Northern Cape' ? 'selected' : '' ?>>Northern Cape</option>
                                                                                        <option value="North West" <?= $branch['region'] === 'North West' ? 'selected' : '' ?>>North West</option>
                                                                                        <option value="Western Cape" <?= $branch['region'] === 'Western Cape' ? 'selected' : '' ?>>Western Cape</option>
                                                                                    </select>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        
                                                                        <div class="mb-3">
                                                                            <label class="form-label">Postal Code</label>
                                                                            <input type="text" class="form-control" name="postal_code" value="<?= htmlspecialchars($branch['postal_code']) ?>">
                                                                        </div>
                                                                        
                                                                        <div class="mb-3">
                                                                            <label class="form-label">Phone</label>
                                                                            <input type="tel" class="form-control" name="phone" value="<?= htmlspecialchars($branch['phone']) ?>">
                                                                        </div>
                                                                        
                                                                        <div class="mb-3">
                                                                            <label class="form-label">Email</label>
                                                                            <input type="email" class="form-control" name="email" value="<?= htmlspecialchars($branch['email']) ?>">
                                                                        </div>
                                                                        
                                                                        <div class="form-check mb-3">
                                                                            <input class="form-check-input" type="checkbox" name="is_main_branch" id="is_main_branch_<?= $branch['branch_id'] ?>" <?= $branch['is_main_branch'] ? 'checked' : '' ?>>
                                                                            <label class="form-check-label" for="is_main_branch_<?= $branch['branch_id'] ?>">
                                                                                Set as Main Branch
                                                                            </label>
                                                                        </div>
                                                                        
                                                                        <div class="mb-3">
                                                                            <label class="form-label">Status</label>
                                                                            <select class="form-control" name="status">
                                                                                <option value="active" <?= $branch['status'] === 'active' ? 'selected' : '' ?>>Active</option>
                                                                                <option value="inactive" <?= $branch['status'] === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                                                                            </select>
                                                                        </div>
                                                                    </div>
                                                                    <div class="modal-footer">
                                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                                        <button type="submit" class="btn btn-primary">Save Changes</button>
                                                                    </div>
                                                                </form>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php
            break;

        case 'manage_sales_team':
            // Get dealer's sales team
            $stmt = $pdo->prepare("SELECT * FROM dealer_sales_team WHERE dealer_id = ? ORDER BY created_at DESC");
            $stmt->execute([$_SESSION['user_id']]);
            $sales_team = $stmt->fetchAll();
            ?>
            
            <div class="page-header" data-aos="fade-down">
                <div class="container">
                    <div class="d-flex justify-content-between align-items-center flex-wrap">
                        <div>
                            <h1><i class="fas fa-users me-3"></i>Sales Team Management</h1>
                            <p class="mb-0">Build and manage your professional sales team</p>
                        </div>
                        <a href="dealer.php" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4" data-aos="fade-right">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-user-plus me-2"></i>Add Team Member</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="dealer.php" enctype="multipart/form-data">
                                <input type="hidden" name="action" value="add_sales_team">
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-user"></i>Full Name</label>
                                    <input type="text" class="form-control" name="name" required placeholder="Enter full name">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-briefcase"></i>Position</label>
                                    <input type="text" class="form-control" name="position" placeholder="e.g., Sales Manager">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-phone"></i>Phone</label>
                                    <input type="tel" class="form-control" name="phone" placeholder="Phone number">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-envelope"></i>Email</label>
                                    <input type="email" class="form-control" name="email" placeholder="Email address">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fab fa-whatsapp"></i>WhatsApp</label>
                                    <input type="tel" class="form-control" name="whatsapp" placeholder="WhatsApp number">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-camera"></i>Photo</label>
                                    <input type="file" class="form-control" name="photo" accept="image/*">
                                </div>
                                
                                <!-- Social Media URLs -->
                                <div class="mb-3">
                                    <label class="form-label"><i class="fab fa-facebook"></i>Facebook URL</label>
                                    <input type="url" class="form-control" name="facebook_url" placeholder="https://facebook.com/...">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fab fa-linkedin"></i>LinkedIn URL</label>
                                    <input type="url" class="form-control" name="linkedin_url" placeholder="https://linkedin.com/in/...">
                                </div>
                                
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-user-plus me-2"></i>Add Team Member
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-8" data-aos="fade-left">
                    <div class="mb-4">
                        <h5><i class="fas fa-users me-2"></i>Current Team Members (<?= count($sales_team) ?>)</h5>
                        
                        <?php if (empty($sales_team)): ?>
                            <div class="empty-state">
                                <i class="fas fa-users"></i>
                                <h4>No Team Members Yet</h4>
                                <p>Add your first team member using the form on the left.</p>
                            </div>
                        <?php else: ?>
                            <div class="row">
                                <?php foreach ($sales_team as $index => $member): ?>
                                <div class="col-md-6 mb-4" data-aos="fade-up" data-aos-delay="<?= $index * 100 ?>">
                                    <div class="card team-member-card">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center mb-3">
                                                <?php if ($member['photo']): ?>
                                                    <img src="<?= htmlspecialchars($member['photo']) ?>" 
                                                         alt="Photo" class="rounded-circle me-3" 
                                                         style="width: 70px; height: 70px; object-fit: cover; border: 3px solid var(--accent-light);">
                                                <?php else: ?>
                                                    <div class="rounded-circle me-3 d-flex align-items-center justify-content-center" 
                                                         style="width: 70px; height: 70px; background: var(--gradient-accent); border: 3px solid var(--accent-light);">
                                                        <i class="fas fa-user text-white fa-2x"></i>
                                                    </div>
                                                <?php endif; ?>
                                                <div>
                                                    <h5 class="mb-1"><?= htmlspecialchars($member['name']) ?></h5>
                                                    <?php if ($member['position']): ?>
                                                        <p class="text-muted mb-0 fw-bold"><?= htmlspecialchars($member['position']) ?></p>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            
                                            <div class="contact-info text-center mb-3">
                                                <?php if ($member['phone']): ?>
                                                    <a href="tel:<?= htmlspecialchars($member['phone']) ?>" class="btn btn-outline-primary">
                                                        <i class="fas fa-phone"></i>
                                                    </a>
                                                <?php endif; ?>
                                                <?php if ($member['email']): ?>
                                                    <a href="mailto:<?= htmlspecialchars($member['email']) ?>" class="btn btn-outline-info">
                                                        <i class="fas fa-envelope"></i>
                                                    </a>
                                                <?php endif; ?>
                                                <?php if ($member['whatsapp']): ?>
                                                    <a href="https://wa.me/<?= htmlspecialchars($member['whatsapp']) ?>" 
                                                       class="btn btn-outline-success" target="_blank">
                                                        <i class="fab fa-whatsapp"></i>
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                            
                                            <div class="social-links text-center">
                                                <?php if ($member['facebook_url']): ?>
                                                    <a href="<?= htmlspecialchars($member['facebook_url']) ?>" 
                                                       class="btn btn-outline-primary" target="_blank">
                                                        <i class="fab fa-facebook"></i>
                                                    </a>
                                                <?php endif; ?>
                                                <?php if ($member['linkedin_url']): ?>
                                                    <a href="<?= htmlspecialchars($member['linkedin_url']) ?>" 
                                                       class="btn btn-outline-info" target="_blank">
                                                        <i class="fab fa-linkedin"></i>
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php
            break;
    }
    ?>
</div>

<!-- Enhanced JavaScript with FIXED Real-time Featured Slots Management -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.js"></script>
<script>
// Initialize AOS (Animate On Scroll) with optimized settings
AOS.init({
    duration: 600,
    easing: 'ease-in-out',
    once: true,
    offset: 50,
    disable: 'mobile' // Disable on mobile for better performance
});

// FIXED: Enhanced Professional AJAX System with Real-time Featured Slots Management
class ProfessionalVehicleFormManager {
    constructor() {
        this.featuredLimit = null;
        this.currentFeatured = 0;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupFormValidation();
        this.setupListingTypeToggle();
        this.setupCharacterCounter();
        this.setupFeaturedLimitCheck();
        this.setupAdvancedFeatures();
        this.setupRealTimeFeaturedValidation();
        console.log('🚀 TrucksONSale Professional System Initialized with Real-time Featured Slots');
    }

    setupEventListeners() {
        // Category change event
        const categorySelect = document.getElementById('category');
        if (categorySelect) {
            categorySelect.addEventListener('change', (e) => {
                const selectedOption = e.target.selectedOptions[0];
                const categoryId = selectedOption?.dataset.id;
                this.onCategoryChange(e.target.value, categoryId);
            });
        }

        // Make change event
        const makeSelect = document.getElementById('make');
        if (makeSelect) {
            makeSelect.addEventListener('change', (e) => {
                const selectedOption = e.target.selectedOptions[0];
                const makeId = selectedOption?.dataset.id;
                this.onMakeChange(e.target.value, makeId);
            });
        }

        // Model change event
        const modelSelect = document.getElementById('model');
        if (modelSelect) {
            modelSelect.addEventListener('change', (e) => {
                const selectedOption = e.target.selectedOptions[0];
                const modelId = selectedOption?.dataset.id;
                this.onModelChange(modelId);
            });
        }

        // File upload validation
        this.setupFileValidation();
    }

    // FIXED: Real-time Featured Slots Validation
    async setupRealTimeFeaturedValidation() {
        // For new listing form
        const newFeaturedCheckbox = document.getElementById('featured_checkbox_new');
        if (newFeaturedCheckbox) {
            newFeaturedCheckbox.addEventListener('change', async (e) => {
                if (e.target.checked) {
                    const canAdd = await this.checkFeaturedLimit();
                    if (!canAdd) {
                        e.target.checked = false;
                        this.showNotification('You have reached your featured listing limit. Please contact admin to increase your limit.', 'warning');
                    }
                }
            });
        }

        // For edit listing form
        const editFeaturedCheckbox = document.getElementById('edit_featured_checkbox');
        if (editFeaturedCheckbox) {
            editFeaturedCheckbox.addEventListener('change', async (e) => {
                if (e.target.checked) {
                    // Get the vehicle ID from the form
                    const vehicleIdInput = document.querySelector('#edit-vehicle-form input[name="vehicle_id"]');
                    const vehicleId = vehicleIdInput ? vehicleIdInput.value : null;
                    const canAdd = await this.checkFeaturedLimit(vehicleId);
                    if (!canAdd) {
                        e.target.checked = false;
                        this.showNotification('You have reached your featured listing limit. Please contact admin to increase your limit.', 'warning');
                    }
                }
            });
        }

        // For existing vehicle cards
        document.querySelectorAll('.featured-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', async (e) => {
                if (e.target.checked) {
                    const vehicleId = e.target.dataset.vehicleId;
                    const canAdd = await this.checkFeaturedLimit(vehicleId);
                    if (!canAdd) {
                        e.target.checked = false;
                        this.showNotification('You have reached your featured listing limit. Please contact admin to increase your limit.', 'warning');
                    }
                }
            });
        });
    }

    // FIXED: Real-time Featured Limit Check
    async checkFeaturedLimit(excludeVehicleId = null) {
        try {
            let url = '?ajax=check_featured_limit';
            if (excludeVehicleId) {
                url += `&exclude_vehicle_id=${excludeVehicleId}`;
            }
            
            const response = await fetch(url);
            const data = await response.json();
            
            if (data.success) {
                this.featuredLimit = data.data.limit;
                this.currentFeatured = data.data.current;
                
                // Update UI elements with real-time data
                this.updateFeaturedDisplays(data.data);
                
                return data.data.can_add;
            }
            return false;
        } catch (error) {
            console.error('❌ Error checking featured limit:', error);
            return false;
        }
    }

    // FIXED: Update all featured displays with real-time data
    updateFeaturedDisplays(featuredData) {
        // Update main dashboard display
        const featuredLimitDisplay = document.getElementById('featured-limit-display');
        const currentFeaturedCount = document.getElementById('current-featured-count');
        const featuredLimit = document.getElementById('featured-limit');
        const remainingSlots = document.getElementById('remaining-slots');
        const featuredCountDisplay = document.getElementById('featured-count-display');
        
        if (currentFeaturedCount) {
            currentFeaturedCount.textContent = featuredData.current;
        }
        
        if (featuredLimit) {
            featuredLimit.textContent = featuredData.limit === -1 ? 'Unlimited' : featuredData.limit;
        }
        
        if (remainingSlots) {
            remainingSlots.textContent = featuredData.remaining;
        }
        
        if (featuredCountDisplay) {
            featuredCountDisplay.textContent = featuredData.current;
        }

        // Update new listing form display
        const currentCountDisplay = document.getElementById('current-count-display');
        const limitDisplay = document.getElementById('limit-display');
        const remainingDisplay = document.getElementById('remaining-display');
        const newListingFeaturedInfo = document.getElementById('new-listing-featured-info');
        
        if (currentCountDisplay) {
            currentCountDisplay.textContent = featuredData.current;
        }
        
        if (limitDisplay) {
            limitDisplay.textContent = featuredData.limit === -1 ? 'Unlimited' : featuredData.limit;
        }
        
        if (remainingDisplay) {
            remainingDisplay.textContent = featuredData.remaining;
        }

        // Update styling based on limit status
        if (featuredLimitDisplay && featuredData.limit !== -1) {
            featuredLimitDisplay.className = 'featured-limit-info';
            
            const percentage = (featuredData.current / featuredData.limit) * 100;
            if (percentage >= 100) {
                featuredLimitDisplay.classList.add('danger');
            } else if (percentage >= 80) {
                featuredLimitDisplay.classList.add('warning');
            }
        }

        if (newListingFeaturedInfo && featuredData.limit !== -1) {
            newListingFeaturedInfo.className = 'featured-limit-info';
            
            const percentage = (featuredData.current / featuredData.limit) * 100;
            if (percentage >= 100) {
                newListingFeaturedInfo.classList.add('danger');
            } else if (percentage >= 80) {
                newListingFeaturedInfo.classList.add('warning');
            }
        }
    }

    async onCategoryChange(categoryKey, categoryId) {
        console.log('📂 Category changed:', categoryKey, 'ID:', categoryId);
        
        // Reset dependent dropdowns with loading states
        this.resetSelect('subcategory', 'Loading...');
        this.resetSelect('make', 'Loading...');
        this.resetSelect('model', 'Select Model');
        this.resetSelect('variant', 'Select Variant');

        if (!categoryId) return;

        // Load data with optimized parallel requests
        try {
            const [subcategoriesResult, makesResult, categoryInfoResult] = await Promise.allSettled([
                this.loadSubcategories(categoryId),
                this.loadMakes(categoryId),
                this.loadCategorySettings(categoryId)
            ]);
            
            // Handle any failed requests gracefully
            if (subcategoriesResult.status === 'rejected') {
                console.warn('Subcategories failed to load:', subcategoriesResult.reason);
            }
            if (makesResult.status === 'rejected') {
                console.warn('Makes failed to load:', makesResult.reason);
            }
        } catch (error) {
            console.error('Error in category change:', error);
            this.showNotification('Error loading category data', 'error');
        }
    }

    async onMakeChange(makeName, makeId) {
        console.log(' Make changed:', makeName, 'ID:', makeId);
        
        this.resetSelect('model', 'Loading models...');
        this.resetSelect('variant', 'Select Variant');

        if (!makeId) return;

        await this.loadModels(makeId);
    }

    async onModelChange(modelId) {
        console.log('🚗 Model changed:', 'ID:', modelId);
        
        this.resetSelect('variant', 'Loading variants...');

        if (!modelId) return;

        await this.loadVariants(modelId);
    }

    async loadSubcategories(categoryId) {
        this.showLoading('subcategory-spinner');
        
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout
            
            const response = await fetch(`?ajax=get_subcategories&category_id=${categoryId}`, {
                signal: controller.signal
            });
            clearTimeout(timeoutId);
            
            const data = await response.json();
            
            if (data.success) {
                const select = document.getElementById('subcategory');
                if (select) {
                    const fragment = document.createDocumentFragment();
                    const defaultOption = this.createOption('', 'Select Subcategory');
                    fragment.appendChild(defaultOption);
                    
                    data.data.forEach(sub => {
                        fragment.appendChild(this.createOption(sub.subcategory_key, sub.subcategory_name));
                    });
                    fragment.appendChild(this.createOption('other', 'Other'));
                    
                    select.innerHTML = '';
                    select.appendChild(fragment);
                    select.disabled = false;
                }
                console.log(' Subcategories loaded:', data.data.length);
            } else {
                throw new Error(data.error || 'Failed to load subcategories');
            }
        } catch (error) {
            if (error.name === 'AbortError') {
                console.warn('Subcategories request timed out');
            } else {
                console.error('❌ Error loading subcategories:', error);
            }
            this.showNotification('Error loading subcategories', 'error');
        } finally {
            this.hideLoading('subcategory-spinner');
        }
    }

    async loadMakes(categoryId) {
        this.showLoading('make-spinner');
        
        try {
            const response = await fetch(`?ajax=get_makes&category_id=${categoryId}`);
            const data = await response.json();
            
            if (data.success) {
                const select = document.getElementById('make');
                if (select) {
                    select.innerHTML = '<option value="">Select Make</option>';
                    data.data.forEach(make => {
                        const option = this.createOption(make.make_name, make.make_name);
                        option.dataset.id = make.make_id;
                        select.appendChild(option);
                    });
                    select.disabled = false;
                }
                console.log('🏭 Makes loaded:', data.data.length);
            } else {
                console.error(' Error loading makes:', data.error);
                this.showNotification('Error loading makes', 'error');
            }
        } catch (error) {
            console.error('❌ Network error loading makes:', error);
            this.showNotification('Network error loading makes', 'error');
        } finally {
            this.hideLoading('make-spinner');
        }
    }

    async loadModels(makeId) {
        this.showLoading('model-spinner');
        
        try {
            const response = await fetch(`?ajax=get_models&make_id=${makeId}`);
            const data = await response.json();
            
            if (data.success) {
                const select = document.getElementById('model');
                if (select) {
                    select.innerHTML = '<option value="">Select Model</option>';
                    data.data.forEach(model => {
                        const option = this.createOption(model.model_name, model.model_name);
                        option.dataset.id = model.model_id;
                        select.appendChild(option);
                    });
                    select.disabled = false;
                }
                console.log('✅ Models loaded:', data.data.length);
            } else {
                console.error('❌ Error loading models:', data.error);
                this.showNotification('Error loading models', 'error');
            }
        } catch (error) {
            console.error(' Network error loading models:', error);
            this.showNotification('Network error loading models', 'error');
        } finally {
            this.hideLoading('model-spinner');
        }
    }

    async loadVariants(modelId) {
        this.showLoading('variant-spinner');
        
        try {
            const response = await fetch(`?ajax=get_variants&model_id=${modelId}`);
            const data = await response.json();
            
            if (data.success) {
                const select = document.getElementById('variant');
                if (select) {
                    select.innerHTML = '<option value="">Select Variant</option>';
                    data.data.forEach(variant => {
                        select.appendChild(this.createOption(variant.variant_name, variant.variant_name));
                    });
                    select.appendChild(this.createOption('other', 'Other'));
                    select.disabled = false;
                }
                console.log('✅ Variants loaded:', data.data.length);
            } else {
                console.error('❌ Error loading variants:', data.error);
                this.showNotification('Error loading variants', 'error');
            }
        } catch (error) {
            console.error('❌ Network error loading variants:', error);
            this.showNotification('Network error loading variants', 'error');
        } finally {
            this.hideLoading('variant-spinner');
        }
    }

    async loadCategorySettings(categoryId) {
        try {
            const response = await fetch(`?ajax=get_category_info&category_id=${categoryId}`);
            const data = await response.json();
            
            if (data.success && data.data) {
                const info = data.data;
                
                // Show/hide hours field with smooth animation
                const hoursField = document.getElementById('hours-field');
                const mileageField = document.getElementById('mileage-field');
                const mileageLabel = document.getElementById('mileage-label');
                
                if (hoursField && mileageField && mileageLabel) {
                    if (info.show_hours === '1' || info.show_hours === 1) {
                        hoursField.style.display = 'block';
                        hoursField.style.opacity = '0';
                        setTimeout(() => { hoursField.style.opacity = '1'; }, 100);
                        mileageLabel.innerHTML = '<i class="fas fa-tachometer-alt me-2"></i>Mileage (Optional)';
                    } else {
                        hoursField.style.display = 'none';
                        mileageLabel.innerHTML = `<i class="fas fa-tachometer-alt me-2"></i>${info.mileage_label || 'Mileage (KM)'}`;
                    }
                }
                
                console.log('✅ Category settings loaded:', info);
            }
        } catch (error) {
            console.error('❌ Error loading category settings:', error);
        }
    }

    async setupFeaturedLimitCheck() {
        try {
            const canAdd = await this.checkFeaturedLimit();
            console.log(' Featured limit check completed:', { canAdd, current: this.currentFeatured, limit: this.featuredLimit });
        } catch (error) {
            console.error('❌ Error checking featured limit:', error);
        }
    }

    setupAdvancedFeatures() {
        // Add real-time price formatting
        const priceInput = document.querySelector('input[name="price"]');
        if (priceInput) {
            priceInput.addEventListener('input', (e) => {
                const value = e.target.value.replace(/[^\d.]/g, '');
                if (value && !isNaN(value)) {
                    // Add visual feedback for valid price
                    e.target.style.borderColor = 'var(--success-green)';
                } else {
                    e.target.style.borderColor = '';
                }
            });
        }

        // Add smart city suggestions based on province
        const provinceSelect = document.querySelector('select[name="region"]');
        const cityInput = document.querySelector('input[name="city"]');
        
        if (provinceSelect && cityInput) {
            const citySuggestions = {
                'Gauteng': ['Johannesburg', 'Pretoria', 'Midrand', 'Sandton', 'Roodepoort'],
                'Western Cape': ['Cape Town', 'Stellenbosch', 'Paarl', 'George', 'Hermanus'],
                'KwaZulu-Natal': ['Durban', 'Pietermaritzburg', 'Richards Bay', 'Newcastle', 'Ladysmith'],
                'Eastern Cape': ['Port Elizabeth', 'East London', 'Uitenhage', 'King Williams Town', 'Grahamstown'],
                'Free State': ['Bloemfontein', 'Welkom', 'Kroonstad', 'Sasolburg', 'Bethlehem'],
                'Mpumalanga': ['Nelspruit', 'Witbank', 'Middelburg', 'Secunda', 'Standerton'],
                'Limpopo': ['Polokwane', 'Tzaneen', 'Mokopane', 'Thohoyandou', 'Giyani'],
                'North West': ['Rustenburg', 'Klerksdorp', 'Potchefstroom', 'Mahikeng', 'Brits'],
                'Northern Cape': ['Kimberley', 'Upington', 'Springbok', 'De Aar', 'Kuruman']
            };

            provinceSelect.addEventListener('change', (e) => {
                const suggestions = citySuggestions[e.target.value] || [];
                if (suggestions.length > 0) {
                    cityInput.placeholder = `e.g., ${suggestions.slice(0, 3).join(', ')}`;
                }
            });
        }
    }

    resetSelect(selectId, placeholder = '') {
        const select = document.getElementById(selectId);
        if (select) {
            const placeholderText = placeholder || `Select ${selectId.charAt(0).toUpperCase() + selectId.slice(1)}`;
            select.innerHTML = `<option value="">${placeholderText}</option>`;
            select.disabled = true;
        }
    }

    createOption(value, text) {
        const option = document.createElement('option');
        option.value = value;
        option.textContent = text;
        return option;
    }

    showLoading(spinnerId) {
        const spinner = document.getElementById(spinnerId);
        if (spinner) {
            spinner.style.display = 'inline-block';
        }
    }

    hideLoading(spinnerId) {
        const spinner = document.getElementById(spinnerId);
        if (spinner) {
            spinner.style.display = 'none';
        }
    }

    showNotification(message, type = 'info') {
        // Create toast notification
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show toast-notification`;
        toast.innerHTML = `
            <i class="fas fa-${type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(toast);
        
        // Trigger animation
        setTimeout(() => {
            toast.classList.add('show');
        }, 100);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.classList.remove('show');
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.remove();
                    }
                }, 300);
            }
        }, 5000);
    }

    setupCharacterCounter() {
        const descriptionTextarea = document.getElementById('description');
        if (descriptionTextarea) {
            descriptionTextarea.addEventListener('input', (e) => {
                this.updateCharCounter(e.target);
            });
            this.updateCharCounter(descriptionTextarea);
        }

        // Also setup for edit form
        const editDescriptionTextarea = document.getElementById('edit_description');
        if (editDescriptionTextarea) {
            editDescriptionTextarea.addEventListener('input', (e) => {
                this.updateEditCharCounter(e.target);
            });
            this.updateEditCharCounter(editDescriptionTextarea);
        }
    }

    updateCharCounter(textarea) {
        const maxLength = 3000;
        const currentLength = textarea.value.length;
        const remaining = maxLength - currentLength;
        const counter = document.getElementById('char-counter');
        
        if (counter) {
            counter.textContent = `${remaining} characters remaining`;
            counter.className = 'char-counter';
            
            if (remaining < 300) {
                counter.className += ' warning';
            }
            if (remaining < 100) {
                counter.className += ' danger';
            }
        }
    }

    updateEditCharCounter(textarea) {
        const maxLength = 3000;
        const currentLength = textarea.value.length;
        const remaining = maxLength - currentLength;
        const counter = document.getElementById('edit-char-counter');
        
        if (counter) {
            counter.textContent = `${remaining} characters remaining`;
            counter.className = 'char-counter';
            
            if (remaining < 300) {
                counter.className += ' warning';
            }
            if (remaining < 100) {
                counter.className += ' danger';
            }
        }
    }

    setupListingTypeToggle() {
        const listingTypeRadios = document.querySelectorAll('input[name="listing_type"]');
        const hireFields = document.getElementById('hire-fields');
        const auctionFields = document.getElementById('auction-fields');
        
        listingTypeRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                // Hide all fields first
                if (hireFields) {
                    hireFields.style.display = 'none';
                    hireFields.style.opacity = '0';
                }
                if (auctionFields) {
                    auctionFields.style.display = 'none';
                    auctionFields.style.opacity = '0';
                }
                
                // Show relevant fields with animation
                setTimeout(() => {
                    if (this.value === 'hire' || this.value === 'rent-to-own') {
                        if (hireFields) {
                            hireFields.style.display = 'block';
                            setTimeout(() => { hireFields.style.opacity = '1'; }, 50);
                        }
                    } else if (this.value === 'auction') {
                        if (auctionFields) {
                            auctionFields.style.display = 'block';
                            setTimeout(() => { auctionFields.style.opacity = '1'; }, 50);
                        }
                    }
                }, 100);
            });
        });
    }

    setupFileValidation() {
        const imageInput = document.getElementById('images');
        const videoInput = document.getElementById('videos');
        const documentInput = document.getElementById('documents');
        
        if (imageInput) {
            imageInput.addEventListener('change', (e) => {
                this.validateFileUpload(e.target, 40, 2 * 1024 * 1024, ['jpg', 'jpeg', 'png', 'gif', 'webp']);
                this.showFilePreview(e.target, 'image');
            });
        }
        
        if (videoInput) {
            videoInput.addEventListener('change', (e) => {
                this.validateFileUpload(e.target, 5, 50 * 1024 * 1024, ['mp4', 'avi', 'mov', 'wmv', 'flv']);
            });
        }
        
        if (documentInput) {
            documentInput.addEventListener('change', (e) => {
                this.validateFileUpload(e.target, 10, 10 * 1024 * 1024, ['pdf']);
            });
        }
    }

    validateFileUpload(input, maxFiles, maxSize, allowedTypes) {
        const files = input.files;
        
        if (files.length > maxFiles) {
            this.showNotification(`Maximum ${maxFiles} files allowed.`, 'error');
            input.value = '';
            return false;
        }
        
        for (let i = 0; i < files.length; i++) {
            if (files[i].size > maxSize) {
                this.showNotification(`File size must be less than ${Math.round(maxSize / 1024 / 1024)}MB.`, 'error');
                input.value = '';
                return false;
            }
            
            const fileExtension = files[i].name.split('.').pop().toLowerCase();
            if (!allowedTypes.includes(fileExtension)) {
                this.showNotification(`Only ${allowedTypes.join(', ')} files are allowed.`, 'error');
                input.value = '';
                return false;
            }
        }
        
        this.showNotification(`${files.length} file(s) selected successfully!`, 'success');
        return true;
    }

    showFilePreview(input, type) {
        // This could be enhanced to show image previews
        if (type === 'image' && input.files.length > 0) {
            console.log(`📸 ${input.files.length} image(s) selected`);
        }
    }

    setupFormValidation() {
        // Setup validation for new vehicle form
        const form = document.getElementById('vehicle-form');
        if (form) {
            form.addEventListener('submit', (e) => {
                if (!this.validateForm()) {
                    e.preventDefault();
                    return false;
                }
                
                // Show loading state
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating Listing...';
                    submitBtn.disabled = true;
                }
            });
        }

        // Setup validation for edit vehicle form
        const editForm = document.getElementById('edit-vehicle-form');
        if (editForm) {
            editForm.addEventListener('submit', (e) => {
                if (!this.validateEditForm()) {
                    e.preventDefault();
                    return false;
                }
                
                // Show loading state
                const submitBtn = editForm.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating Listing...';
                    submitBtn.disabled = true;
                }
            });
        }
    }

    validateForm() {
        const requiredFields = ['category', 'make', 'model', 'price', 'region', 'city'];
        let isValid = true;
        const errors = [];
        
        requiredFields.forEach(fieldName => {
            const field = document.querySelector(`[name="${fieldName}"]`);
            if (field && !field.value.trim()) {
                field.classList.add('is-invalid');
                field.style.borderColor = 'var(--accent-coral)';
                errors.push(`${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`);
                isValid = false;
            } else if (field) {
                field.classList.remove('is-invalid');
                field.style.borderColor = 'var(--success-green)';
            }
        });
        
        if (!isValid) {
            this.showNotification(`Please complete all required fields: ${errors.join(', ')}`, 'error');
        }
        
        return isValid;
    }

    validateEditForm() {
        const requiredFields = ['category', 'make', 'model', 'price', 'region', 'city'];
        let isValid = true;
        const errors = [];
        
        requiredFields.forEach(fieldName => {
            const field = document.querySelector(`#edit-vehicle-form [name="${fieldName}"]`);
            if (field && !field.value.trim()) {
                field.classList.add('is-invalid');
                field.style.borderColor = 'var(--accent-coral)';
                errors.push(`${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`);
                isValid = false;
            } else if (field) {
                field.classList.remove('is-invalid');
                field.style.borderColor = 'var(--success-green)';
            }
        });
        
        if (!isValid) {
            this.showNotification(`Please complete all required fields: ${errors.join(', ')}`, 'error');
        }
        
        return isValid;
    }
}

// Global character counter function
function updateCharCounter(textarea) {
    if (window.professionalVehicleForm) {
        if (textarea.id === 'edit_description') {
            window.professionalVehicleForm.updateEditCharCounter(textarea);
        } else {
            window.professionalVehicleForm.updateCharCounter(textarea);
        }
    }
}

// Initialize the professional system when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.professionalVehicleForm = new ProfessionalVehicleFormManager();
    
    // Optimized visual feedback with requestAnimationFrame
    const selectElements = document.querySelectorAll('select, input, textarea');
    selectElements.forEach(element => {
        element.addEventListener('focus', function() {
            requestAnimationFrame(() => {
                this.style.transform = 'translateY(-1px)';
                this.style.transition = 'all 0.2s ease';
            });
        });
        
        element.addEventListener('blur', function() {
            requestAnimationFrame(() => {
                this.style.transform = 'translateY(0)';
            });
        });
    });
    
    // Optimized button hover effects with throttling
    const buttons = document.querySelectorAll('.btn');
    let hoverTimeout;
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            clearTimeout(hoverTimeout);
            hoverTimeout = setTimeout(() => {
                this.style.transform = 'translateY(-2px)';
            }, 50);
        });
        
        button.addEventListener('mouseleave', function() {
            clearTimeout(hoverTimeout);
            this.style.transform = 'translateY(0)';
        });
    });
    
    // Optimized smooth scroll with passive listeners
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
        }, { passive: false });
    });
    
    console.log('🔥 TrucksONSale Professional System Ready - Real-time Featured Slots Enabled!');
}, { once: true });

// Enhanced global functions
function editFeatured(vehicleId) {
    const newDate = prompt('Enter new featured expiry date (YYYY-MM-DD) or leave empty for unlimited:');
    if (newDate !== null) {
        const formData = new FormData();
        formData.append('action', 'toggle_featured');
        formData.append('vehicle_id', vehicleId);
        formData.append('featured_until', newDate);
        formData.append('featured', 'on');
        
        fetch(window.location.href, {
            method: 'POST',
            body: formData
        })
        .then(response => response.text())
        .then(() => {
            location.reload();
        })
        .catch(error => {
            console.error('Error updating featured:', error);
            if (window.professionalVehicleForm) {
                window.professionalVehicleForm.showNotification('Error updating featured status', 'error');
            }
        });
    }
}

function removeFeatured(vehicleId) {
    if (confirm('Remove featured status from this listing?')) {
        const formData = new FormData();
        formData.append('action', 'toggle_featured');
        formData.append('vehicle_id', vehicleId);
        
        fetch(window.location.href, {
            method: 'POST',
            body: formData
        })
        .then(response => response.text())
        .then(() => {
            location.reload();
        })
        .catch(error => {
            console.error('Error removing featured:', error);
            if (window.professionalVehicleForm) {
                window.professionalVehicleForm.showNotification('Error removing featured status', 'error');
            }
        });
    }
}
</script>

</body>
</html>

<?php
// Clean output buffer and flush
ob_end_flush();
?>
