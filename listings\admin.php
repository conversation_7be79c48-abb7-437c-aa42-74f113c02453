<?php
// Fix headers already sent by using output buffering
ob_start();

session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if user is logged in and is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] != 'admin') {
    ob_end_clean();
    header("Location: login.php");
    exit;
}

// Database Configuration
$host = 'localhost';
$db_name = 'truc_tos';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$db_name;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Database Connection Error: " . $e->getMessage());
}

// DEFENSIVE COLUMN CHECKING AND CREATION
function ensureColumnExists($pdo, $table, $column, $definition = 'INT DEFAULT 0') {
    try {
        $stmt = $pdo->query("SHOW COLUMNS FROM `$table` LIKE '$column'");
        if ($stmt->rowCount() == 0) {
            $pdo->exec("ALTER TABLE `$table` ADD COLUMN `$column` $definition");
        }
    } catch(PDOException $e) {
        error_log("Column check/creation error: " . $e->getMessage());
    }
}

// CREATE ENHANCED ADMIN TABLES
try {
    // 1. Premium Ads Table (Admin Only)
    $pdo->exec("CREATE TABLE IF NOT EXISTS premium_ads (
        id INT PRIMARY KEY AUTO_INCREMENT,
        title VARCHAR(255) NOT NULL,
        image_url VARCHAR(500) NOT NULL,
        link_url VARCHAR(500),
        ad_type ENUM('banner', 'box') NOT NULL,
        position VARCHAR(100) DEFAULT 'homepage',
        status ENUM('active', 'inactive') DEFAULT 'active',
        display_order INT DEFAULT 0,
        start_date DATE NULL,
        end_date DATE NULL,
        click_count INT DEFAULT 0,
        impression_count INT DEFAULT 0,
        created_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_ad_type (ad_type),
        INDEX idx_status (status),
        INDEX idx_position (position),
        INDEX idx_display_order (display_order)
    )");

    // 2. Enhanced Dealership Limits Table
    $pdo->exec("CREATE TABLE IF NOT EXISTS dealership_limits (
        id INT PRIMARY KEY AUTO_INCREMENT,
        dealership_id INT NOT NULL,
        featured_limit INT DEFAULT 2,
        current_featured_count INT DEFAULT 0,
        premium_limit INT DEFAULT 1,
        current_premium_count INT DEFAULT 0,
        listing_limit INT DEFAULT 50,
        current_listing_count INT DEFAULT 0,
        updated_by INT,
        notes TEXT,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_dealership (dealership_id),
        INDEX idx_dealership (dealership_id)
    )");

    // Ensure all required columns exist in dealership_limits
    ensureColumnExists($pdo, 'dealership_limits', 'featured_limit', 'INT DEFAULT 2');
    ensureColumnExists($pdo, 'dealership_limits', 'current_featured_count', 'INT DEFAULT 0');
    ensureColumnExists($pdo, 'dealership_limits', 'premium_limit', 'INT DEFAULT 1');
    ensureColumnExists($pdo, 'dealership_limits', 'current_premium_count', 'INT DEFAULT 0');
    ensureColumnExists($pdo, 'dealership_limits', 'listing_limit', 'INT DEFAULT 50');
    ensureColumnExists($pdo, 'dealership_limits', 'current_listing_count', 'INT DEFAULT 0');

    // 3. System Settings Table
    $pdo->exec("CREATE TABLE IF NOT EXISTS system_settings (
        id INT PRIMARY KEY AUTO_INCREMENT,
        setting_key VARCHAR(100) NOT NULL UNIQUE,
        setting_value TEXT,
        setting_type ENUM('text', 'number', 'boolean', 'json') DEFAULT 'text',
        description TEXT,
        updated_by INT,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_key (setting_key)
    )");

    // 4. Admin Activity Log
    $pdo->exec("CREATE TABLE IF NOT EXISTS admin_activity_log (
        id INT PRIMARY KEY AUTO_INCREMENT,
        admin_id INT NOT NULL,
        action VARCHAR(100) NOT NULL,
        target_type VARCHAR(50),
        target_id INT,
        description TEXT,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_admin (admin_id),
        INDEX idx_action (action),
        INDEX idx_created (created_at)
    )");

    // Insert default system settings
    $pdo->exec("INSERT IGNORE INTO system_settings (setting_key, setting_value, setting_type, description) VALUES 
        ('site_name', 'TrucksONSale', 'text', 'Website name'),
        ('default_featured_limit', '2', 'number', 'Default featured listing limit for new dealers'),
        ('auto_approve_dealers', '0', 'boolean', 'Automatically approve new dealer registrations'),
        ('maintenance_mode', '0', 'boolean', 'Enable maintenance mode'),
        ('max_images_per_listing', '40', 'number', 'Maximum images per vehicle listing'),
        ('max_videos_per_listing', '5', 'number', 'Maximum videos per vehicle listing')");

} catch(PDOException $e) {
    error_log("Admin database setup error: " . $e->getMessage());
}

// Enhanced Helper Functions
function uploadAdImage($file, $upload_dir = 'uploads/ads') {
    if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
        return false;
    }
    
    $allowed_types = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    
    if (!in_array($file_extension, $allowed_types)) {
        return false;
    }
    
    if ($file['size'] > 5 * 1024 * 1024) { // 5MB limit for ads
        return false;
    }
    
    $unique_name = uniqid() . '_' . preg_replace('/[^a-zA-Z0-9._-]/', '', $file['name']);
    $upload_path = $upload_dir . '/' . $unique_name;
    
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    if (move_uploaded_file($file['tmp_name'], $upload_path)) {
        return $upload_path;
    }
    
    return false;
}

function logAdminActivity($pdo, $admin_id, $action, $target_type = null, $target_id = null, $description = null) {
    try {
        $stmt = $pdo->prepare("INSERT INTO admin_activity_log (admin_id, action, target_type, target_id, description, ip_address, user_agent) VALUES (?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute([
            $admin_id, 
            $action, 
            $target_type, 
            $target_id, 
            $description, 
            $_SERVER['REMOTE_ADDR'] ?? null, 
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);
    } catch(PDOException $e) {
        error_log("Failed to log admin activity: " . $e->getMessage());
    }
}

function updateDealerLimits($pdo, $dealer_id) {
    try {
        // Get current counts
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM vehicles WHERE dealer_id = ? AND featured = 1 AND (featured_until IS NULL OR featured_until >= CURDATE())");
        $stmt->execute([$dealer_id]);
        $featured_count = $stmt->fetchColumn();
        
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM vehicles WHERE dealer_id = ? AND premium_listing = 1 AND (premium_until IS NULL OR premium_until >= CURDATE())");
        $stmt->execute([$dealer_id]);
        $premium_count = $stmt->fetchColumn();
        
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM vehicles WHERE dealer_id = ?");
        $stmt->execute([$dealer_id]);
        $listing_count = $stmt->fetchColumn();
        
        // Update or insert dealer limits
        $stmt = $pdo->prepare("
            INSERT INTO dealership_limits (dealership_id, current_featured_count, current_premium_count, current_listing_count) 
            VALUES (?, ?, ?, ?) 
            ON DUPLICATE KEY UPDATE 
            current_featured_count = ?, 
            current_premium_count = ?, 
            current_listing_count = ?
        ");
        $stmt->execute([$dealer_id, $featured_count, $premium_count, $listing_count, $featured_count, $premium_count, $listing_count]);
        
    } catch(PDOException $e) {
        error_log("Error updating dealer limits: " . $e->getMessage());
    }
}

// DEFENSIVE DATABASE QUERYING
function safeGetDealerLimits($pdo) {
    try {
        // First check if table exists and has required columns
        $stmt = $pdo->query("SHOW TABLES LIKE 'dealership_limits'");
        if ($stmt->rowCount() == 0) {
            // Table doesn't exist, return basic data
            $stmt = $pdo->query("SELECT user_id, company_name, username FROM users WHERE user_type = 'dealer' AND status = 'active' ORDER BY company_name");
            $dealers = $stmt->fetchAll();
            foreach ($dealers as &$dealer) {
                $dealer['featured_limit'] = 2;
                $dealer['current_featured_count'] = 0;
                $dealer['premium_limit'] = 1;
                $dealer['current_premium_count'] = 0;
                $dealer['listing_limit'] = 50;
                $dealer['current_listing_count'] = 0;
                $dealer['notes'] = '';
                $dealer['limits_updated'] = null;
            }
            return $dealers;
        }

        // Table exists, do full query with error handling
        $stmt = $pdo->query("
            SELECT u.*, 
                   COALESCE(dl.featured_limit, 2) as featured_limit, 
                   COALESCE(dl.current_featured_count, 0) as current_featured_count, 
                   COALESCE(dl.premium_limit, 1) as premium_limit, 
                   COALESCE(dl.current_premium_count, 0) as current_premium_count,
                   COALESCE(dl.listing_limit, 50) as listing_limit, 
                   COALESCE(dl.current_listing_count, 0) as current_listing_count,
                   dl.notes, dl.updated_at as limits_updated
            FROM users u 
            LEFT JOIN dealership_limits dl ON u.user_id = dl.dealership_id 
            WHERE u.user_type = 'dealer' AND u.status = 'active'
            ORDER BY u.company_name
        ");
        return $stmt->fetchAll();
        
    } catch(PDOException $e) {
        error_log("Safe dealer limits query error: " . $e->getMessage());
        // Fallback to basic user data
        try {
            $stmt = $pdo->query("SELECT user_id, company_name, username FROM users WHERE user_type = 'dealer' AND status = 'active' ORDER BY company_name");
            $dealers = $stmt->fetchAll();
            foreach ($dealers as &$dealer) {
                $dealer['featured_limit'] = 2;
                $dealer['current_featured_count'] = 0;
                $dealer['premium_limit'] = 1;
                $dealer['current_premium_count'] = 0;
                $dealer['listing_limit'] = 50;
                $dealer['current_listing_count'] = 0;
                $dealer['notes'] = '';
                $dealer['limits_updated'] = null;
            }
            return $dealers;
        } catch(PDOException $e2) {
            return [];
        }
    }
}

// Handle POST requests
$action = $_POST['action'] ?? $_GET['action'] ?? 'admin_dashboard';
$message = '';
$error = '';

if ($_POST) {
    try {
        switch ($_POST['action'] ?? $action) {
            case 'add_category':
                $stmt = $pdo->prepare("INSERT INTO categories (category_key, category_name, listing_type, icon, listing_label, show_hours, mileage_label, engine_label, show_transmission, show_fuel_type, show_year) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute([
                    $_POST['category_key'],
                    $_POST['category_name'],
                    $_POST['listing_type'],
                    $_POST['icon'],
                    $_POST['listing_label'],
                    isset($_POST['show_hours']) ? 1 : 0,
                    $_POST['mileage_label'] ?? 'Mileage (KM)',
                    $_POST['engine_label'] ?? 'Engine Type',
                    isset($_POST['show_transmission']) ? 1 : 0,
                    isset($_POST['show_fuel_type']) ? 1 : 0,
                    isset($_POST['show_year']) ? 1 : 0
                ]);
                logAdminActivity($pdo, $_SESSION['user_id'], 'CREATE_CATEGORY', 'category', $pdo->lastInsertId(), "Created category: {$_POST['category_name']}");
                ob_end_clean();
                header("Location: admin.php?action=manage_categories&msg=added");
                exit;

            case 'add_premium_ad':
                $image_path = null;
                if (!empty($_FILES['ad_image']['name'])) {
                    $image_path = uploadAdImage($_FILES['ad_image']);
                    if (!$image_path) {
                        throw new Exception("Failed to upload ad image");
                    }
                }
                
                $stmt = $pdo->prepare("INSERT INTO premium_ads (title, image_url, link_url, ad_type, position, status, display_order, start_date, end_date, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute([
                    $_POST['title'],
                    $image_path,
                    $_POST['link_url'] ?: null,
                    $_POST['ad_type'],
                    $_POST['position'] ?? 'homepage',
                    $_POST['status'] ?? 'active',
                    $_POST['display_order'] ?? 0,
                    $_POST['start_date'] ?: null,
                    $_POST['end_date'] ?: null,
                    $_SESSION['user_id']
                ]);
                logAdminActivity($pdo, $_SESSION['user_id'], 'CREATE_AD', 'premium_ads', $pdo->lastInsertId(), "Created premium ad: {$_POST['title']}");
                ob_end_clean();
                header("Location: admin.php?action=manage_ads&msg=ad_added");
                exit;

            case 'update_dealer_limits':
                $dealer_id = $_POST['dealer_id'];
                $featured_limit = $_POST['featured_limit'] == -1 ? -1 : (int)$_POST['featured_limit'];
                $premium_limit = $_POST['premium_limit'] == -1 ? -1 : (int)$_POST['premium_limit'];
                $listing_limit = $_POST['listing_limit'] == -1 ? -1 : (int)$_POST['listing_limit'];
                $notes = $_POST['notes'] ?? '';
                
                $stmt = $pdo->prepare("
                    INSERT INTO dealership_limits (dealership_id, featured_limit, premium_limit, listing_limit, notes, updated_by) 
                    VALUES (?, ?, ?, ?, ?, ?) 
                    ON DUPLICATE KEY UPDATE 
                    featured_limit = ?, premium_limit = ?, listing_limit = ?, notes = ?, updated_by = ?
                ");
                $stmt->execute([$dealer_id, $featured_limit, $premium_limit, $listing_limit, $notes, $_SESSION['user_id'], $featured_limit, $premium_limit, $listing_limit, $notes, $_SESSION['user_id']]);
                
                // Update current counts
                updateDealerLimits($pdo, $dealer_id);
                
                logAdminActivity($pdo, $_SESSION['user_id'], 'UPDATE_DEALER_LIMITS', 'dealer', $dealer_id, "Updated limits for dealer ID: {$dealer_id}");
                ob_end_clean();
                header("Location: admin.php?action=dealer_limits&msg=limits_updated");
                exit;

            case 'approve_listing':
                $stmt = $pdo->prepare("UPDATE vehicles SET status = 'available' WHERE vehicle_id = ?");
                $stmt->execute([$_POST['vehicle_id']]);
                logAdminActivity($pdo, $_SESSION['user_id'], 'APPROVE_LISTING', 'vehicle', $_POST['vehicle_id'], "Approved vehicle listing");
                ob_end_clean();
                header("Location: admin.php?action=all_listings&msg=listing_approved");
                exit;

            case 'delete_ad':
                $ad_id = $_POST['ad_id'];
                
                // Get ad info for cleanup
                $stmt = $pdo->prepare("SELECT image_url FROM premium_ads WHERE id = ?");
                $stmt->execute([$ad_id]);
                $ad = $stmt->fetch();
                
                if ($ad && $ad['image_url'] && file_exists($ad['image_url'])) {
                    unlink($ad['image_url']);
                }
                
                $stmt = $pdo->prepare("DELETE FROM premium_ads WHERE id = ?");
                $stmt->execute([$ad_id]);
                
                logAdminActivity($pdo, $_SESSION['user_id'], 'DELETE_AD', 'premium_ads', $ad_id, "Deleted premium ad");
                ob_end_clean();
                header("Location: admin.php?action=manage_ads&msg=ad_deleted");
                exit;

            case 'add_subcategory':
                $stmt = $pdo->prepare("INSERT INTO subcategories (category_id, subcategory_key, subcategory_name) VALUES (?, ?, ?)");
                $stmt->execute([
                    $_POST['category_id'],
                    $_POST['subcategory_key'],
                    $_POST['subcategory_name']
                ]);
                logAdminActivity($pdo, $_SESSION['user_id'], 'CREATE_SUBCATEGORY', 'subcategory', $pdo->lastInsertId(), "Created subcategory: {$_POST['subcategory_name']}");
                ob_end_clean();
                header("Location: admin.php?action=manage_categories&msg=subcategory_added");
                exit;

            case 'add_make':
                $stmt = $pdo->prepare("INSERT INTO category_makes (category_id, make_name) VALUES (?, ?)");
                $stmt->execute([
                    $_POST['category_id'],
                    $_POST['make_name']
                ]);
                logAdminActivity($pdo, $_SESSION['user_id'], 'CREATE_MAKE', 'make', $pdo->lastInsertId(), "Created make: {$_POST['make_name']}");
                ob_end_clean();
                header("Location: admin.php?action=manage_makes&msg=added");
                exit;

            case 'add_model':
                $stmt = $pdo->prepare("INSERT INTO category_models (make_id, model_name) VALUES (?, ?)");
                $stmt->execute([
                    $_POST['make_id'],
                    $_POST['model_name']
                ]);
                logAdminActivity($pdo, $_SESSION['user_id'], 'CREATE_MODEL', 'model', $pdo->lastInsertId(), "Created model: {$_POST['model_name']}");
                ob_end_clean();
                header("Location: admin.php?action=manage_models&make_id=" . $_POST['make_id'] . "&msg=added");
                exit;

            case 'add_variant':
                $stmt = $pdo->prepare("INSERT INTO category_variants (model_id, variant_name, variant_description) VALUES (?, ?, ?)");
                $stmt->execute([
                    $_POST['model_id'],
                    $_POST['variant_name'],
                    $_POST['variant_description'] ?: null
                ]);
                logAdminActivity($pdo, $_SESSION['user_id'], 'CREATE_VARIANT', 'variant', $pdo->lastInsertId(), "Created variant: {$_POST['variant_name']}");
                ob_end_clean();
                header("Location: admin.php?action=manage_variants&model_id=" . $_POST['model_id'] . "&msg=variant_added");
                exit;

            case 'add_year':
                $stmt = $pdo->prepare("INSERT INTO system_years (year_value) VALUES (?)");
                $stmt->execute([$_POST['year_value']]);
                logAdminActivity($pdo, $_SESSION['user_id'], 'CREATE_YEAR', 'year', $pdo->lastInsertId(), "Added year: {$_POST['year_value']}");
                ob_end_clean();
                header("Location: admin.php?action=manage_years&msg=year_added");
                exit;

            case 'approve_dealer':
                $stmt = $pdo->prepare("UPDATE users SET status = 'active' WHERE user_id = ? AND user_type = 'dealer'");
                $stmt->execute([$_POST['dealer_id']]);
                
                // Create default limits for new dealer
                $stmt = $pdo->prepare("INSERT IGNORE INTO dealership_limits (dealership_id, featured_limit, premium_limit, listing_limit, updated_by) VALUES (?, 2, 1, 50, ?)");
                $stmt->execute([$_POST['dealer_id'], $_SESSION['user_id']]);
                
                logAdminActivity($pdo, $_SESSION['user_id'], 'APPROVE_DEALER', 'dealer', $_POST['dealer_id'], "Approved dealer registration");
                ob_end_clean();
                header("Location: admin.php?action=admin_dashboard&msg=approved");
                exit;

            case 'reject_dealer':
                $stmt = $pdo->prepare("UPDATE users SET status = 'rejected' WHERE user_id = ? AND user_type = 'dealer'");
                $stmt->execute([$_POST['dealer_id']]);
                logAdminActivity($pdo, $_SESSION['user_id'], 'REJECT_DEALER', 'dealer', $_POST['dealer_id'], "Rejected dealer registration");
                ob_end_clean();
                header("Location: admin.php?action=pending_dealers&msg=rejected");
                exit;

            case 'set_premium':
                $premium_until = $_POST['premium_until'] ? $_POST['premium_until'] : null;
                $stmt = $pdo->prepare("UPDATE vehicles SET premium_listing = ?, premium_until = ? WHERE vehicle_id = ?");
                $stmt->execute([
                    isset($_POST['premium_listing']) ? 1 : 0,
                    $premium_until,
                    $_POST['vehicle_id']
                ]);
                logAdminActivity($pdo, $_SESSION['user_id'], 'SET_PREMIUM', 'vehicle', $_POST['vehicle_id'], "Updated premium status");
                ob_end_clean();
                header("Location: admin.php?action=premium_management&msg=premium_updated");
                exit;

            case 'set_featured':
                $featured_until = $_POST['featured_until'] ? $_POST['featured_until'] : null;
                $stmt = $pdo->prepare("UPDATE vehicles SET featured = ?, featured_until = ? WHERE vehicle_id = ?");
                $stmt->execute([
                    isset($_POST['featured']) ? 1 : 0,
                    $featured_until,
                    $_POST['vehicle_id']
                ]);
                logAdminActivity($pdo, $_SESSION['user_id'], 'SET_FEATURED', 'vehicle', $_POST['vehicle_id'], "Updated featured status");
                ob_end_clean();
                header("Location: admin.php?action=featured_management&msg=featured_updated");
                exit;

            case 'update_settings':
                if (isset($_POST['settings'])) {
                    foreach ($_POST['settings'] as $key => $value) {
                        $stmt = $pdo->prepare("UPDATE system_settings SET setting_value = ?, updated_by = ? WHERE setting_key = ?");
                        $stmt->execute([$value, $_SESSION['user_id'], $key]);
                    }
                    logAdminActivity($pdo, $_SESSION['user_id'], 'UPDATE_SETTINGS', 'system', null, "Updated system settings");
                    ob_end_clean();
                    header("Location: admin.php?action=system_settings&msg=settings_updated");
                    exit;
                }
                break;
        }
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
        error_log("Admin action error: " . $e->getMessage());
    }
}

// Handle logout
if ($action == 'logout') {
    logAdminActivity($pdo, $_SESSION['user_id'], 'LOGOUT', null, null, "Admin logged out");
    session_destroy();
    ob_end_clean();
    header("Location: login.php");
    exit;
}

// Handle success messages
if (isset($_GET['msg'])) {
    $messages = [
        'approved' => 'Dealer approved successfully!',
        'rejected' => 'Dealer rejected successfully!',
        'added' => 'Record added successfully!',
        'updated' => 'Record updated successfully!',
        'deleted' => 'Record deleted successfully!',
        'premium_updated' => 'Premium listing status updated!',
        'featured_updated' => 'Featured listing status updated!',
        'variant_added' => 'Variant added successfully!',
        'year_added' => 'Year added successfully!',
        'subcategory_added' => 'Subcategory added successfully!',
        'ad_added' => 'Premium ad created successfully!',
        'ad_updated' => 'Premium ad updated successfully!',
        'ad_deleted' => 'Premium ad deleted successfully!',
        'limits_updated' => 'Dealer limits updated successfully!',
        'listing_approved' => 'Listing approved successfully!',
        'settings_updated' => 'System settings updated successfully!'
    ];
    $message = $messages[$_GET['msg']] ?? 'Action completed successfully!';
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TrucksONSale - Admin Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-blue: #0050B8;
            --primary-blue-light: #1E6FBA;
            --primary-blue-dark: #003D8F;
            --accent-blue: #4A90E2;
            --accent-light: #E6F3FF;
            --text-dark: #1F2937;
            --text-light: #FFFFFF;
            --gray-100: #F8F9FA;
            --gray-200: #E9ECEF;
            --gray-300: #DEE2E6;
            --gray-400: #CED4DA;
            --gray-500: #ADB5BD;
            --gray-600: #6C757D;
            --gray-700: #495057;
            --gray-800: #343A40;
            --gray-900: #212529;
            --white: #FFFFFF;
            --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
            --border-radius: 0.75rem;
            --border-radius-lg: 1rem;
            --border-radius-xl: 1.5rem;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --gradient-primary: linear-gradient(135deg, var(--primary-blue), var(--primary-blue-light));
            --gradient-accent: linear-gradient(135deg, var(--accent-blue), var(--accent-light));
        }
        
        * {
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: var(--text-dark);
            line-height: 1.5;
            margin: 0;
            padding: 0;
            font-weight: 400;
            font-size: 14px;
        }

        /* Navigation */
        .navbar-custom {
            background: var(--gradient-primary) !important;
            box-shadow: var(--shadow-lg);
            backdrop-filter: blur(20px);
            padding: 0.75rem 0;
            border-bottom: 2px solid var(--accent-blue);
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.25rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--text-light) !important;
            text-decoration: none;
            transition: var(--transition);
        }

        .navbar-brand:hover {
            transform: scale(1.02);
            color: var(--accent-light) !important;
        }

        .navbar-brand i {
            font-size: 1.5rem;
            color: var(--accent-light);
        }

        .navbar-text {
            color: var(--text-light) !important;
            font-weight: 500;
            font-size: 0.875rem;
        }

        /* Button System */
        .btn {
            font-weight: 600;
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius);
            transition: var(--transition);
            border: none;
            position: relative;
            overflow: hidden;
            text-transform: none;
            font-size: 0.875rem;
            box-shadow: var(--shadow-sm);
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: var(--text-light);
            border: 2px solid transparent;
        }
        
        .btn-primary:hover {
            background: var(--gradient-accent);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            color: var(--white);
        }

        .btn-outline-primary {
            border: 2px solid var(--primary-blue);
            color: var(--primary-blue);
            background: transparent;
        }
        
        .btn-outline-primary:hover {
            background: var(--gradient-primary);
            color: var(--text-light);
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        .btn-success {
            background: linear-gradient(135deg, #10B981, #4ECDC4);
            color: var(--white);
        }

        .btn-warning {
            background: linear-gradient(135deg, #F59E0B, #FFA726);
            color: var(--white);
        }

        .btn-danger {
            background: linear-gradient(135deg, #EF4444, #FF5722);
            color: var(--white);
        }

        .btn-info {
            background: var(--gradient-accent);
            color: var(--white);
        }

        .btn-light {
            background: var(--white);
            color: var(--text-dark);
            border: 2px solid var(--gray-200);
        }

        .btn-outline-light {
            border: 2px solid var(--text-light);
            color: var(--text-light);
            background: transparent;
        }

        .btn-outline-light:hover {
            background: var(--text-light);
            color: var(--primary-blue);
        }

        /* Card System */
        .card {
            border: none;
            box-shadow: var(--shadow);
            border-radius: var(--border-radius-lg);
            overflow: hidden;
            transition: var(--transition);
            background: var(--white);
            margin-bottom: 2rem;
            border: 1px solid var(--gray-200);
        }
        
        .card:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-4px);
        }
        
        .card-header {
            background: var(--gradient-primary);
            color: var(--text-light);
            border: none;
            padding: 1rem 1.5rem;
            font-weight: 700;
            font-size: 1rem;
            position: relative;
        }

        .card-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: var(--gradient-accent);
        }

        .card-body {
            padding: 1.5rem;
        }

        /* Form Elements */
        .form-control, .form-select {
            border: 2px solid var(--gray-300);
            border-radius: var(--border-radius);
            padding: 0.5rem 0.75rem;
            transition: var(--transition);
            font-size: 0.875rem;
            font-weight: 500;
            background: var(--white);
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--accent-blue);
            box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);
            outline: none;
            transform: translateY(-1px);
        }
        
        .form-label {
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
        }

        .form-label i {
            color: var(--accent-blue);
            font-size: 1rem;
        }

        /* File Upload */
        .file-upload-area {
            border: 2px dashed var(--gray-400);
            border-radius: var(--border-radius-lg);
            padding: 2rem 1.5rem;
            text-align: center;
            transition: var(--transition);
            background: var(--gray-100);
            margin-bottom: 1rem;
            position: relative;
            overflow: hidden;
        }
        
        .file-upload-area:hover {
            border-color: var(--accent-blue);
            background: linear-gradient(135deg, var(--accent-light), rgba(230, 243, 255, 0.3));
            transform: scale(1.01);
        }

        .file-upload-area i {
            color: var(--accent-blue);
            margin-bottom: 0.75rem;
            font-size: 2rem;
        }

        .file-upload-area h6 {
            font-size: 0.9rem;
            font-weight: 600;
        }

        /* Stats Cards */
        .stats-card {
            background: var(--gradient-primary);
            color: var(--text-light);
            text-align: center;
            padding: 1.5rem 1rem;
            border-radius: var(--border-radius-xl);
            box-shadow: var(--shadow-lg);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            transform: rotate(45deg);
            transition: var(--transition);
            opacity: 0;
        }

        .stats-card:hover::before {
            opacity: 1;
            animation: shimmer 2s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { transform: rotate(45deg) translate(-20%, -20%); }
            50% { transform: rotate(45deg) translate(20%, 20%); }
        }
        
        .stats-card:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 1rem 2rem rgba(0, 80, 184, 0.3);
        }
        
        .stats-card i {
            font-size: 2.5rem;
            margin-bottom: 0.75rem;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }
        
        .stats-card h4 {
            font-size: 2rem;
            font-weight: 700;
            margin: 0.5rem 0;
            position: relative;
            z-index: 2;
        }

        .stats-card p {
            font-size: 0.875rem;
            font-weight: 600;
            margin: 0;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }

        /* Color Variants for Stats Cards */
        .stats-card.bg-success {
            background: linear-gradient(135deg, #10B981, #4ECDC4);
        }

        .stats-card.bg-warning {
            background: linear-gradient(135deg, #F59E0B, #FFA726);
        }

        .stats-card.bg-danger {
            background: linear-gradient(135deg, #EF4444, #FF5722);
        }

        .stats-card.bg-info {
            background: var(--gradient-accent);
        }

        /* Page Header */
        .page-header {
            background: var(--gradient-primary);
            color: var(--text-light);
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: var(--border-radius-xl);
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .page-header .container {
            position: relative;
            z-index: 2;
        }

        .page-header h1 {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .page-header p {
            font-size: 0.9rem;
            opacity: 0.9;
            font-weight: 500;
        }

        /* Alert System */
        .alert {
            border: none;
            border-radius: var(--border-radius-lg);
            padding: 0.75rem 1rem;
            margin-bottom: 1.5rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            box-shadow: var(--shadow);
            border-left: 4px solid;
            font-size: 0.875rem;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
            border-left-color: #10B981;
        }
        
        .alert-danger {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
            border-left-color: #EF4444;
        }
        
        .alert-info {
            background: linear-gradient(135deg, var(--accent-light), rgba(230, 243, 255, 0.5));
            color: var(--text-dark);
            border-left-color: var(--accent-blue);
        }

        /* Admin Sidebar */
        .admin-sidebar {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 1.5rem;
            height: fit-content;
            position: sticky;
            top: 2rem;
        }
        
        .admin-sidebar .nav-link {
            color: var(--text-dark);
            padding: 0.75rem 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 0.5rem;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-weight: 500;
            font-size: 0.875rem;
        }
        
        .admin-sidebar .nav-link:hover {
            background: var(--accent-light);
            color: var(--primary-blue);
            transform: translateX(5px);
        }
        
        .admin-sidebar .nav-link.active {
            background: var(--primary-blue);
            color: white;
        }

        /* Tables */
        .table {
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow);
            background: white;
            font-size: 0.875rem;
        }
        
        .table thead th {
            background: var(--primary-blue);
            color: white;
            border: none;
            font-weight: 600;
            padding: 1rem;
            font-size: 0.875rem;
        }

        .table-hover tbody tr:hover {
            background-color: var(--accent-light);
            transform: translateX(5px);
            transition: all 0.3s ease;
        }

        /* Listing Type Badges */
        .listing-type-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .listing-type-sale { background: var(--primary-blue); color: white; }
        .listing-type-rent_to_own { background: #10B981; color: white; }
        .listing-type-hire { background: #F59E0B; color: white; }
        .listing-type-auction { background: #EF4444; color: white; }

        /* Premium Ad Cards */
        .ad-card {
            position: relative;
            overflow: hidden;
            border-radius: var(--border-radius-lg);
            transition: var(--transition);
        }

        .ad-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
        }

        .ad-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }

        .ad-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(0,0,0,0.7), rgba(0,0,0,0.3));
            display: flex;
            align-items: end;
            padding: 1rem;
            opacity: 0;
            transition: var(--transition);
        }

        .ad-card:hover .ad-overlay {
            opacity: 1;
        }

        /* Dealer Limits Management */
        .limits-control {
            background: var(--gray-100);
            border-radius: var(--border-radius);
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .limits-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
            margin: 0.25rem;
        }

        .limits-badge.unlimited {
            background: linear-gradient(135deg, #10B981, #4ECDC4);
            color: white;
        }

        .limits-badge.limited {
            background: linear-gradient(135deg, #F59E0B, #FFA726);
            color: white;
        }

        .limits-badge.exceeded {
            background: linear-gradient(135deg, #EF4444, #FF5722);
            color: white;
            animation: pulse 2s ease-in-out infinite;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .card-body {
                padding: 1rem;
            }
            
            .btn {
                padding: 0.4rem 0.8rem;
                font-size: 0.8rem;
            }
            
            .stats-card {
                padding: 1rem 0.75rem;
            }
            
            .stats-card h4 {
                font-size: 1.5rem;
            }

            .stats-card i {
                font-size: 2rem;
            }

            .page-header {
                padding: 1.5rem 0;
            }

            .page-header h1 {
                font-size: 1.5rem;
            }

            .navbar-brand {
                font-size: 1.1rem;
            }

            .admin-sidebar {
                margin-bottom: 2rem;
                position: static;
            }
        }

        /* Animation Classes */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .slide-in-left {
            animation: slideInLeft 0.5s ease-out;
        }
        
        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-30px); }
            to { opacity: 1; transform: translateX(0); }
        }

        /* Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--gray-200);
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--accent-blue), var(--accent-light));
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--primary-blue), var(--accent-blue));
        }
    </style>
</head>
<body>

<nav class="navbar navbar-expand-lg navbar-dark navbar-custom">
    <div class="container">
        <a class="navbar-brand" href="admin.php">
            <i class="fas fa-shield-alt"></i>
            <span>TrucksONSale Admin</span>
        </a>
        <div class="navbar-nav ms-auto">
            <span class="navbar-text me-4">
                <i class="fas fa-user-shield me-2"></i>
                Welcome, <strong><?php echo htmlspecialchars($_SESSION['username']); ?></strong>
            </span>
            <a href="admin.php?action=logout" class="btn btn-outline-light">
                <i class="fas fa-sign-out-alt me-2"></i>Logout
            </a>
        </div>
    </div>
</nav>

<div class="container mt-4">
    <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show fade-in">
            <i class="fas fa-check-circle"></i>
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show fade-in">
            <i class="fas fa-exclamation-circle"></i>
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php
    switch ($action) {
        case 'admin_dashboard':
        default:
            // Enhanced statistics with comprehensive data
            $stats = [];
            
            // Pending dealers
            $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE user_type = 'dealer' AND status = 'pending'");
            $stats['pending_dealers'] = $stmt->fetchColumn();
            
            // Active dealers
            $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE user_type = 'dealer' AND status = 'active'");
            $stats['active_dealers'] = $stmt->fetchColumn();
            
            // Total listings
            $stmt = $pdo->query("SELECT COUNT(*) FROM vehicles");
            $stats['total_listings'] = $stmt->fetchColumn();
            
            // Get stats by listing type
            try {
                $stmt = $pdo->query("SELECT listing_type, COUNT(*) as count FROM vehicles WHERE listing_type IS NOT NULL GROUP BY listing_type");
                $listing_type_stats = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
            } catch (PDOException $e) {
                error_log("Error getting listing stats: " . $e->getMessage());
                $listing_type_stats = [];
            }

            $listing_stats = [
                'sale' => $listing_type_stats['sale'] ?? 0,
                'rent-to-own' => $listing_type_stats['rent-to-own'] ?? 0,
                'hire' => $listing_type_stats['hire'] ?? 0,
                'auction' => $listing_type_stats['auction'] ?? 0
            ];
            
            // Premium listings
            $stmt = $pdo->query("SELECT COUNT(*) FROM vehicles WHERE premium_listing = 1 AND (premium_until IS NULL OR premium_until >= CURDATE())");
            $stats['premium_listings'] = $stmt->fetchColumn();

            // Featured listings
            $stmt = $pdo->query("SELECT COUNT(*) FROM vehicles WHERE featured = 1 AND (featured_until IS NULL OR featured_until >= CURDATE())");
            $stats['featured_listings'] = $stmt->fetchColumn();

            // Active premium ads
            try {
                $stmt = $pdo->query("SELECT COUNT(*) FROM premium_ads WHERE status = 'active'");
                $stats['active_ads'] = $stmt->fetchColumn();
            } catch (PDOException $e) {
                $stats['active_ads'] = 0;
            }
            
            // Get recent activity
            $stmt = $pdo->query("SELECT v.*, u.company_name FROM vehicles v JOIN users u ON v.dealer_id = u.user_id ORDER BY v.created_at DESC LIMIT 5");
            $recent_listings = $stmt->fetchAll();
            
            $stmt = $pdo->query("SELECT * FROM users WHERE user_type = 'dealer' AND status = 'pending' ORDER BY registered_at DESC LIMIT 5");
            $recent_pending = $stmt->fetchAll();

            // Get admin activity log
            try {
                $stmt = $pdo->query("SELECT a.*, u.username FROM admin_activity_log a JOIN users u ON a.admin_id = u.user_id ORDER BY a.created_at DESC LIMIT 10");
                $recent_activity = $stmt->fetchAll();
            } catch (PDOException $e) {
                $recent_activity = [];
            }
            ?>
            
            <div class="page-header fade-in">
                <div class="container">
                    <h1><i class="fas fa-tachometer-alt me-2"></i>Admin Dashboard</h1>
                    <p class="mb-0">Complete system management and monitoring</p>
                </div>
            </div>

            <div class="row">
                <div class="col-md-3">
                    <div class="admin-sidebar slide-in-left">
                        <h6 class="text-muted text-uppercase">ADMIN NAVIGATION</h6>
                        <nav class="nav flex-column mt-3">
                            <a class="nav-link active" href="?action=admin_dashboard">
                                <i class="fas fa-tachometer-alt"></i>Dashboard
                            </a>
                            <a class="nav-link" href="?action=manage_categories">
                                <i class="fas fa-tags"></i>Categories
                            </a>
                            <a class="nav-link" href="?action=manage_makes">
                                <i class="fas fa-industry"></i>Makes & Models
                            </a>
                            <a class="nav-link" href="?action=manage_years">
                                <i class="fas fa-calendar"></i>System Years
                            </a>
                            <a class="nav-link" href="?action=manage_ads">
                                <i class="fas fa-ad"></i>Premium Ads
                                <?php if ($stats['active_ads'] > 0): ?>
                                    <span class="badge bg-success"><?= $stats['active_ads'] ?></span>
                                <?php endif; ?>
                            </a>
                            <a class="nav-link" href="?action=dealer_limits">
                                <i class="fas fa-users-cog"></i>Dealer Limits
                            </a>
                            <a class="nav-link" href="?action=pending_dealers">
                                <i class="fas fa-clock"></i>Pending Dealers
                                <?php if ($stats['pending_dealers'] > 0): ?>
                                    <span class="badge bg-danger"><?= $stats['pending_dealers'] ?></span>
                                <?php endif; ?>
                            </a>
                            <a class="nav-link" href="?action=active_dealers">
                                <i class="fas fa-users"></i>Active Dealers
                            </a>
                            <a class="nav-link" href="?action=all_listings">
                                <i class="fas fa-list"></i>All Listings
                            </a>
                            <a class="nav-link" href="?action=featured_management">
                                <i class="fas fa-fire"></i>Featured Management
                            </a>
                            <a class="nav-link" href="?action=premium_management">
                                <i class="fas fa-star"></i>Premium Management
                            </a>
                            <a class="nav-link" href="?action=system_settings">
                                <i class="fas fa-cogs"></i>System Settings
                            </a>
                            <a class="nav-link" href="?action=admin_logs">
                                <i class="fas fa-history"></i>Activity Logs
                            </a>
                        </nav>
                    </div>
                </div>
                
                <div class="col-md-9">
                    <!-- Enhanced Stats Overview -->
                    <div class="row mb-4">
                        <div class="col-md-3 mb-3 fade-in" style="animation-delay: 0.1s;">
                            <div class="stats-card bg-warning">
                                <i class="fas fa-clock"></i>
                                <h4><?= $stats['pending_dealers'] ?></h4>
                                <p>Pending Dealers</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3 fade-in" style="animation-delay: 0.2s;">
                            <div class="stats-card bg-success">
                                <i class="fas fa-users"></i>
                                <h4><?= $stats['active_dealers'] ?></h4>
                                <p>Active Dealers</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3 fade-in" style="animation-delay: 0.3s;">
                            <div class="stats-card bg-info">
                                <i class="fas fa-list"></i>
                                <h4><?= $stats['total_listings'] ?></h4>
                                <p>Total Listings</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3 fade-in" style="animation-delay: 0.4s;">
                            <div class="stats-card bg-danger">
                                <i class="fas fa-ad"></i>
                                <h4><?= $stats['active_ads'] ?></h4>
                                <p>Active Ads</p>
                            </div>
                        </div>
                    </div>

                    <!-- Featured and Premium Stats -->
                    <div class="row mb-4">
                        <div class="col-md-6 mb-3 fade-in" style="animation-delay: 0.5s;">
                            <div class="card">
                                <div class="card-body text-center py-4">
                                    <i class="fas fa-fire fa-3x text-danger mb-3"></i>
                                    <h3 class="text-danger mb-2"><?= $stats['featured_listings'] ?></h3>
                                    <p class="text-muted mb-2"><strong>Featured Listings</strong></p>
                                    <small class="text-muted">Enhanced visibility and priority placement</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3 fade-in" style="animation-delay: 0.6s;">
                            <div class="card">
                                <div class="card-body text-center py-4">
                                    <i class="fas fa-crown fa-3x text-warning mb-3"></i>
                                    <h3 class="text-warning mb-2"><?= $stats['premium_listings'] ?></h3>
                                    <p class="text-muted mb-2"><strong>Premium Listings</strong></p>
                                    <small class="text-muted">Premium features and benefits</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Listing Types Stats -->
                    <div class="card mb-4 fade-in" style="animation-delay: 0.7s;">
                        <div class="card-header">
                            <h5><i class="fas fa-chart-pie me-2"></i>Listings by Type</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h3 class="text-primary"><?= $listing_stats['sale'] ?? 0 ?></h3>
                                        <p class="text-muted">For Sale</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h3 class="text-success"><?= $listing_stats['rent-to-own'] ?? 0 ?></h3>
                                        <p class="text-muted">Rent-to-Own</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h3 class="text-warning"><?= $listing_stats['hire'] ?? 0 ?></h3>
                                        <p class="text-muted">Hire/Rental</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h3 class="text-danger"><?= $listing_stats['auction'] ?? 0 ?></h3>
                                        <p class="text-muted">Auctions</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="row">
                        <div class="col-md-6 mb-4 fade-in" style="animation-delay: 0.8s;">
                            <div class="card">
                                <div class="card-header">
                                    <h6><i class="fas fa-plus me-2"></i>Recent Listings</h6>
                                </div>
                                <div class="card-body">
                                    <?php if (empty($recent_listings)): ?>
                                        <p class="text-muted text-center">No listings yet</p>
                                    <?php else: ?>
                                        <?php foreach ($recent_listings as $listing): ?>
                                        <div class="d-flex justify-content-between align-items-center mb-2 p-2 border-bottom">
                                            <div>
                                                <strong><?= htmlspecialchars($listing['make'] . ' ' . $listing['model']) ?></strong>
                                                <small class="d-block text-muted"><?= htmlspecialchars($listing['company_name']) ?></small>
                                                <div>
                                                    <span class="badge listing-type-<?= str_replace('-', '_', $listing['listing_type']) ?>"><?= strtoupper($listing['listing_type']) ?></span>
                                                    <?php if ($listing['featured']): ?>
                                                        <span class="badge bg-danger">FEATURED</span>
                                                    <?php endif; ?>
                                                    <?php if ($listing['premium_listing']): ?>
                                                        <span class="badge bg-warning">PREMIUM</span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            <div class="text-end">
                                                <div>R<?= number_format($listing['price'], 0) ?></div>
                                                <small class="text-muted"><?= date('M j', strtotime($listing['created_at'])) ?></small>
                                            </div>
                                        </div>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-4 fade-in" style="animation-delay: 0.9s;">
                            <div class="card">
                                <div class="card-header">
                                    <h6><i class="fas fa-history me-2"></i>Admin Activity</h6>
                                </div>
                                <div class="card-body">
                                    <?php if (empty($recent_activity)): ?>
                                        <p class="text-muted text-center">No recent activity</p>
                                    <?php else: ?>
                                        <?php foreach (array_slice($recent_activity, 0, 5) as $activity): ?>
                                        <div class="d-flex justify-content-between align-items-center mb-2 p-2 border-bottom">
                                            <div>
                                                <strong><?= htmlspecialchars($activity['username']) ?></strong>
                                                <small class="d-block text-muted"><?= htmlspecialchars($activity['action']) ?></small>
                                                <?php if ($activity['description']): ?>
                                                    <small class="text-muted"><?= htmlspecialchars($activity['description']) ?></small>
                                                <?php endif; ?>
                                            </div>
                                            <div class="text-end">
                                                <small class="text-muted"><?= date('M j, g:i A', strtotime($activity['created_at'])) ?></small>
                                            </div>
                                        </div>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </div>
                                <div class="card-footer text-center">
                                    <a href="?action=admin_logs" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-history me-1"></i>View All Logs
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php
            break;

        case 'manage_ads':
            // Get all premium ads
            try {
                $stmt = $pdo->query("SELECT * FROM premium_ads ORDER BY display_order, created_at DESC");
                $premium_ads = $stmt->fetchAll();
            } catch (PDOException $e) {
                $premium_ads = [];
            }
            ?>
            
            <div class="page-header fade-in">
                <div class="container">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1><i class="fas fa-ad me-2"></i>Premium Ads Management</h1>
                            <p class="mb-0">Manage banner and box advertisements for enhanced revenue</p>
                        </div>
                        <a href="admin.php" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="card slide-in-left">
                        <div class="card-header">
                            <h5><i class="fas fa-plus me-2"></i>Create Premium Ad</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="admin.php" enctype="multipart/form-data">
                                <input type="hidden" name="action" value="add_premium_ad">
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-heading"></i>Ad Title</label>
                                    <input type="text" class="form-control" name="title" required placeholder="Enter ad title">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-image"></i>Ad Image</label>
                                    <div class="file-upload-area">
                                        <i class="fas fa-upload fa-2x mb-2"></i>
                                        <h6>Upload Ad Image</h6>
                                        <input type="file" class="form-control" name="ad_image" accept="image/*" required>
                                        <small class="text-muted">Max: 5MB, JPG/PNG/GIF/WebP</small>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-link"></i>Link URL</label>
                                    <input type="url" class="form-control" name="link_url" placeholder="https://example.com">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-type"></i>Ad Type</label>
                                    <select class="form-control" name="ad_type" required>
                                        <option value="banner">Banner Ad</option>
                                        <option value="box">Box Ad</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-map-marker-alt"></i>Position</label>
                                    <select class="form-control" name="position">
                                        <option value="homepage">Homepage</option>
                                        <option value="listings">Listings Page</option>
                                        <option value="sidebar">Sidebar</option>
                                        <option value="footer">Footer</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-sort"></i>Display Order</label>
                                    <input type="number" class="form-control" name="display_order" value="0" min="0">
                                </div>
                                
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label"><i class="fas fa-calendar-plus"></i>Start Date</label>
                                        <input type="date" class="form-control" name="start_date">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label"><i class="fas fa-calendar-times"></i>End Date</label>
                                        <input type="date" class="form-control" name="end_date">
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-toggle-on"></i>Status</label>
                                    <select class="form-control" name="status">
                                        <option value="active">Active</option>
                                        <option value="inactive">Inactive</option>
                                    </select>
                                </div>
                                
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-plus"></i>Create Premium Ad
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-8">
                    <div class="card fade-in">
                        <div class="card-header">
                            <h5><i class="fas fa-list me-2"></i>Premium Ads (<?= count($premium_ads) ?>)</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($premium_ads)): ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-ad fa-4x text-muted mb-3"></i>
                                    <h4>No Premium Ads</h4>
                                    <p class="text-muted">Create your first premium ad to start generating revenue.</p>
                                </div>
                            <?php else: ?>
                                <div class="row">
                                    <?php foreach ($premium_ads as $ad): ?>
                                    <div class="col-md-6 mb-4">
                                        <div class="card ad-card">
                                            <?php if ($ad['image_url'] && file_exists($ad['image_url'])): ?>
                                                <img src="<?= htmlspecialchars($ad['image_url']) ?>" alt="Ad Image">
                                            <?php else: ?>
                                                <div class="bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                                    <i class="fas fa-image fa-3x text-muted"></i>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <div class="ad-overlay">
                                                <div class="text-white">
                                                    <h6><?= htmlspecialchars($ad['title']) ?></h6>
                                                    <div class="d-flex gap-2">
                                                        <button class="btn btn-warning btn-sm" onclick="editAd(<?= $ad['id'] ?>)">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <form method="POST" style="display: inline;">
                                                            <input type="hidden" name="action" value="delete_ad">
                                                            <input type="hidden" name="ad_id" value="<?= $ad['id'] ?>">
                                                            <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Delete this ad?')">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <h6 class="mb-0"><?= htmlspecialchars($ad['title']) ?></h6>
                                                    <span class="badge bg-<?= $ad['status'] == 'active' ? 'success' : 'secondary' ?>">
                                                        <?= ucfirst($ad['status']) ?>
                                                    </span>
                                                </div>
                                                
                                                <div class="row text-center mb-2">
                                                    <div class="col-4">
                                                        <small class="text-muted">Type</small>
                                                        <div><span class="badge bg-primary"><?= ucfirst($ad['ad_type']) ?></span></div>
                                                    </div>
                                                    <div class="col-4">
                                                        <small class="text-muted">Position</small>
                                                        <div><small><?= ucfirst($ad['position']) ?></small></div>
                                                    </div>
                                                    <div class="col-4">
                                                        <small class="text-muted">Order</small>
                                                        <div><strong><?= $ad['display_order'] ?></strong></div>
                                                    </div>
                                                </div>
                                                
                                                <div class="row text-center">
                                                    <div class="col-6">
                                                        <small class="text-muted">Clicks</small>
                                                        <div><strong><?= number_format($ad['click_count']) ?></strong></div>
                                                    </div>
                                                    <div class="col-6">
                                                        <small class="text-muted">Impressions</small>
                                                        <div><strong><?= number_format($ad['impression_count']) ?></strong></div>
                                                    </div>
                                                </div>
                                                
                                                <?php if ($ad['link_url']): ?>
                                                    <div class="mt-2">
                                                        <a href="<?= htmlspecialchars($ad['link_url']) ?>" target="_blank" class="btn btn-outline-primary btn-sm w-100">
                                                            <i class="fas fa-external-link-alt me-1"></i>View Target
                                                        </a>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php
            break;

        case 'dealer_limits':
            // Get all active dealers with their current limits using safe function
            $dealers = safeGetDealerLimits($pdo);

            // Update all dealer counts
            foreach ($dealers as $dealer) {
                updateDealerLimits($pdo, $dealer['user_id']);
            }

            // Refresh data after update
            $dealers = safeGetDealerLimits($pdo);
            ?>
            
            <div class="page-header fade-in">
                <div class="container">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1><i class="fas fa-users-cog me-2"></i>Dealer Limits Management</h1>
                            <p class="mb-0">Control dealer listing limits and track usage</p>
                        </div>
                        <a href="admin.php" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <div class="card fade-in">
                <div class="card-header">
                    <h5><i class="fas fa-table me-2"></i>Dealer Limits Overview (<?= count($dealers) ?> dealers)</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($dealers)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-4x text-muted mb-3"></i>
                            <h4>No Active Dealers</h4>
                            <p class="text-muted">No active dealers to manage limits for.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Dealer</th>
                                        <th>Featured Limits</th>
                                        <th>Premium Limits</th>
                                        <th>Listing Limits</th>
                                        <th>Last Updated</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($dealers as $dealer): ?>
                                    <tr>
                                        <td>
                                            <div>
                                                <strong><?= htmlspecialchars($dealer['company_name']) ?></strong>
                                                <br><small class="text-muted"><?= htmlspecialchars($dealer['username']) ?></small>
                                            </div>
                                        </td>
                                        <td>
                                            <?php 
                                            $featured_limit = $dealer['featured_limit'] ?? 2;
                                            $featured_current = $dealer['current_featured_count'] ?? 0;
                                            $featured_class = 'limited';
                                            if ($featured_limit == -1) {
                                                $featured_class = 'unlimited';
                                            } elseif ($featured_current >= $featured_limit) {
                                                $featured_class = 'exceeded';
                                            }
                                            ?>
                                            <div class="limits-badge <?= $featured_class ?>">
                                                <i class="fas fa-fire me-1"></i>
                                                <?= $featured_current ?> / <?= $featured_limit == -1 ? '∞' : $featured_limit ?>
                                            </div>
                                        </td>
                                        <td>
                                            <?php 
                                            $premium_limit = $dealer['premium_limit'] ?? 1;
                                            $premium_current = $dealer['current_premium_count'] ?? 0;
                                            $premium_class = 'limited';
                                            if ($premium_limit == -1) {
                                                $premium_class = 'unlimited';
                                            } elseif ($premium_current >= $premium_limit) {
                                                $premium_class = 'exceeded';
                                            }
                                            ?>
                                            <div class="limits-badge <?= $premium_class ?>">
                                                <i class="fas fa-crown me-1"></i>
                                                <?= $premium_current ?> / <?= $premium_limit == -1 ? '∞' : $premium_limit ?>
                                            </div>
                                        </td>
                                        <td>
                                            <?php 
                                            $listing_limit = $dealer['listing_limit'] ?? 50;
                                            $listing_current = $dealer['current_listing_count'] ?? 0;
                                            $listing_class = 'limited';
                                            if ($listing_limit == -1) {
                                                $listing_class = 'unlimited';
                                            } elseif ($listing_current >= $listing_limit) {
                                                $listing_class = 'exceeded';
                                            }
                                            ?>
                                            <div class="limits-badge <?= $listing_class ?>">
                                                <i class="fas fa-list me-1"></i>
                                                <?= $listing_current ?> / <?= $listing_limit == -1 ? '∞' : $listing_limit ?>
                                            </div>
                                        </td>
                                        <td>
                                            <?= $dealer['limits_updated'] ? date('M j, Y', strtotime($dealer['limits_updated'])) : 'Never' ?>
                                        </td>
                                        <td>
                                            <button class="btn btn-primary btn-sm" onclick="manageLimits(<?= $dealer['user_id'] ?>, '<?= htmlspecialchars($dealer['company_name']) ?>', <?= $featured_limit ?>, <?= $premium_limit ?>, <?= $listing_limit ?>, '<?= htmlspecialchars($dealer['notes'] ?? '') ?>')">
                                                <i class="fas fa-cogs"></i>Manage
                                            </button>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Limits Management Modal -->
            <div class="modal fade" id="limitsModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title"><i class="fas fa-cogs me-2"></i>Manage Dealer Limits</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <form method="POST" action="admin.php">
                            <input type="hidden" name="action" value="update_dealer_limits">
                            <input type="hidden" name="dealer_id" id="modal_dealer_id">
                            
                            <div class="modal-body">
                                <div class="mb-3">
                                    <label class="form-label"><strong>Dealer:</strong> <span id="modal_dealer_name"></span></label>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-fire text-danger"></i>Featured Limit</label>
                                            <select class="form-control" name="featured_limit" id="modal_featured_limit">
                                                <option value="0">None (0)</option>
                                                <option value="2">Basic (2)</option>
                                                <option value="5">Standard (5)</option>
                                                <option value="10">Premium (10)</option>
                                                <option value="25">Pro (25)</option>
                                                <option value="-1">Unlimited</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-crown text-warning"></i>Premium Limit</label>
                                            <select class="form-control" name="premium_limit" id="modal_premium_limit">
                                                <option value="0">None (0)</option>
                                                <option value="1">Basic (1)</option>
                                                <option value="3">Standard (3)</option>
                                                <option value="5">Premium (5)</option>
                                                <option value="10">Pro (10)</option>
                                                <option value="-1">Unlimited</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-list text-info"></i>Listing Limit</label>
                                            <select class="form-control" name="listing_limit" id="modal_listing_limit">
                                                <option value="10">Starter (10)</option>
                                                <option value="25">Basic (25)</option>
                                                <option value="50">Standard (50)</option>
                                                <option value="100">Premium (100)</option>
                                                <option value="250">Pro (250)</option>
                                                <option value="-1">Unlimited</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-sticky-note"></i>Notes</label>
                                    <textarea class="form-control" name="notes" id="modal_notes" rows="3" placeholder="Internal notes about this dealer's limits..."></textarea>
                                </div>
                            </div>
                            
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i>Update Limits
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <?php
            break;

        case 'pending_dealers':
            $stmt = $pdo->query("SELECT * FROM users WHERE user_type = 'dealer' AND status = 'pending' ORDER BY registered_at DESC");
            $pending_dealers = $stmt->fetchAll();
            ?>
            
            <div class="page-header fade-in">
                <div class="container">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1><i class="fas fa-clock me-2"></i>Pending Dealer Approvals</h1>
                            <p class="mb-0">Review and approve new dealer registrations</p>
                        </div>
                        <a href="admin.php" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <div class="card fade-in">
                <div class="card-header">
                    <div class="row">
                        <div class="col-md-6">
                            <h5><i class="fas fa-users me-2"></i>Dealer Approval Queue (<?= count($pending_dealers) ?>)</h5>
                        </div>
                        <div class="col-md-6 text-end">
                            <span class="badge bg-warning fs-6"><?= count($pending_dealers) ?> Pending</span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (empty($pending_dealers)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
                            <h4>All Caught Up!</h4>
                            <p class="text-muted">No dealers pending approval at this time.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Company Name</th>
                                        <th>Contact Person</th>
                                        <th>Email</th>
                                        <th>Phone</th>
                                        <th>Registration Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($pending_dealers as $dealer): ?>
                                    <tr>
                                        <td>
                                            <strong><?= htmlspecialchars($dealer['company_name']) ?></strong>
                                        </td>
                                        <td><?= htmlspecialchars($dealer['username']) ?></td>
                                        <td>
                                            <a href="mailto:<?= htmlspecialchars($dealer['email']) ?>">
                                                <?= htmlspecialchars($dealer['email']) ?>
                                            </a>
                                        </td>
                                        <td>
                                            <a href="tel:<?= htmlspecialchars($dealer['phone']) ?>">
                                                <?= htmlspecialchars($dealer['phone']) ?>
                                            </a>
                                        </td>
                                        <td><?= date('M j, Y g:i A', strtotime($dealer['registered_at'])) ?></td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="action" value="approve_dealer">
                                                    <input type="hidden" name="dealer_id" value="<?= $dealer['user_id'] ?>">
                                                    <button type="submit" class="btn btn-success btn-sm" onclick="return confirm('Approve this dealer?')">
                                                        <i class="fas fa-check"></i> Approve
                                                    </button>
                                                </form>
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="action" value="reject_dealer">
                                                    <input type="hidden" name="dealer_id" value="<?= $dealer['user_id'] ?>">
                                                    <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Reject this dealer?')">
                                                        <i class="fas fa-times"></i> Reject
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php
            break;

        case 'manage_categories':
            // Get all categories
            $stmt = $pdo->query("SELECT * FROM categories ORDER BY listing_type, category_order, category_name");
            $categories = $stmt->fetchAll();

            // Get subcategories
            $stmt = $pdo->query("
                SELECT s.*, c.category_name 
                FROM subcategories s 
                JOIN categories c ON s.category_id = c.category_id 
                ORDER BY c.category_name, s.subcategory_name
            ");
            $subcategories = $stmt->fetchAll();
            ?>
            
            <div class="page-header fade-in">
                <div class="container">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1><i class="fas fa-tags me-2"></i>Categories Management</h1>
                            <p class="mb-0">Manage vehicle categories and subcategories</p>
                        </div>
                        <a href="admin.php" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="card slide-in-left">
                        <div class="card-header">
                            <h5><i class="fas fa-plus me-2"></i>Add New Category</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="admin.php">
                                <input type="hidden" name="action" value="add_category">
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-key"></i>Category Key</label>
                                    <input type="text" class="form-control" name="category_key" required placeholder="e.g., trucks">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-tag"></i>Category Name</label>
                                    <input type="text" class="form-control" name="category_name" required placeholder="e.g., Trucks">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-list"></i>Listing Type</label>
                                    <select class="form-control" name="listing_type" required>
                                        <option value="sale">For Sale</option>
                                        <option value="rent-to-own">Rent-to-Own</option>
                                        <option value="hire">Hire/Rental</option>
                                        <option value="auction">Auction</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-icons"></i>Icon</label>
                                    <input type="text" class="form-control" name="icon" value="fas fa-tag" placeholder="e.g., fas fa-truck">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-label"></i>Listing Label</label>
                                    <input type="text" class="form-control" name="listing_label" required placeholder="e.g., List Your Truck">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-tachometer-alt"></i>Mileage Label</label>
                                    <input type="text" class="form-control" name="mileage_label" value="Mileage (KM)" placeholder="Custom mileage label">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-cog"></i>Engine Label</label>
                                    <input type="text" class="form-control" name="engine_label" value="Engine Type" placeholder="Custom engine label">
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" name="show_hours" id="show_hours">
                                        <label class="form-check-label" for="show_hours">
                                            <i class="fas fa-clock me-1"></i>Show Hours Field (for machinery)
                                        </label>
                                    </div>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" name="show_transmission" id="show_transmission" checked>
                                        <label class="form-check-label" for="show_transmission">
                                            <i class="fas fa-exchange-alt me-1"></i>Show Transmission Field
                                        </label>
                                    </div>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" name="show_fuel_type" id="show_fuel_type" checked>
                                        <label class="form-check-label" for="show_fuel_type">
                                            <i class="fas fa-gas-pump me-1"></i>Show Fuel Type Field
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="show_year" id="show_year" checked>
                                        <label class="form-check-label" for="show_year">
                                            <i class="fas fa-calendar me-1"></i>Show Year Field
                                        </label>
                                    </div>
                                </div>
                                
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-plus"></i>Add Category
                                </button>
                            </form>
                        </div>
                    </div>

                    <div class="card mt-4">
                        <div class="card-header">
                            <h5><i class="fas fa-plus me-2"></i>Add Subcategory</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="admin.php">
                                <input type="hidden" name="action" value="add_subcategory">
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-tags"></i>Category</label>
                                    <select class="form-control" name="category_id" required>
                                        <option value="">Select Category</option>
                                        <?php foreach ($categories as $cat): ?>
                                            <option value="<?= $cat['category_id'] ?>"><?= htmlspecialchars($cat['category_name']) ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-key"></i>Subcategory Key</label>
                                    <input type="text" class="form-control" name="subcategory_key" required placeholder="e.g., heavy_duty">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-tag"></i>Subcategory Name</label>
                                    <input type="text" class="form-control" name="subcategory_name" required placeholder="e.g., Heavy Duty">
                                </div>
                                
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-plus"></i>Add Subcategory
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-8">
                    <div class="card fade-in">
                        <div class="card-header">
                            <h5><i class="fas fa-list me-2"></i>Current Categories (<?= count($categories) ?>)</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($categories)): ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-tags fa-4x text-muted mb-3"></i>
                                    <h4>No Categories Yet</h4>
                                    <p class="text-muted">Add your first category using the form on the left.</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Category Name</th>
                                                <th>Type</th>
                                                <th>Icon</th>
                                                <th>Settings</th>
                                                <th>Created</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($categories as $category): ?>
                                            <tr>
                                                <td>
                                                    <strong><?= htmlspecialchars($category['category_name']) ?></strong>
                                                    <br><small class="text-muted"><?= htmlspecialchars($category['category_key']) ?></small>
                                                </td>
                                                <td>
                                                    <span class="badge listing-type-<?= str_replace('-', '_', $category['listing_type']) ?>">
                                                        <?= strtoupper($category['listing_type']) ?>
                                                    </span>
                                                </td>
                                                <td><i class="<?= htmlspecialchars($category['icon']) ?> fa-lg"></i></td>
                                                <td>
                                                    <div class="d-flex flex-wrap gap-1">
                                                        <?= $category['show_hours'] ? '<span class="badge bg-success">Hours</span>' : '' ?>
                                                        <?= $category['show_transmission'] ? '<span class="badge bg-info">Trans</span>' : '' ?>
                                                        <?= $category['show_fuel_type'] ? '<span class="badge bg-warning">Fuel</span>' : '' ?>
                                                        <?= $category['show_year'] ? '<span class="badge bg-primary">Year</span>' : '' ?>
                                                    </div>
                                                </td>
                                                <td><?= date('M j, Y', strtotime($category['created_at'])) ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="card mt-4">
                        <div class="card-header">
                            <h5><i class="fas fa-list me-2"></i>Subcategories (<?= count($subcategories) ?>)</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($subcategories)): ?>
                                <div class="text-center py-3">
                                    <p class="text-muted">No subcategories yet</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Category</th>
                                                <th>Subcategory Name</th>
                                                <th>Key</th>
                                                <th>Created</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($subcategories as $sub): ?>
                                            <tr>
                                                <td><?= htmlspecialchars($sub['category_name']) ?></td>
                                                <td><strong><?= htmlspecialchars($sub['subcategory_name']) ?></strong></td>
                                                <td><code><?= htmlspecialchars($sub['subcategory_key']) ?></code></td>
                                                <td><?= date('M j, Y', strtotime($sub['created_at'])) ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php
            break;

        case 'manage_makes':
            // Get all categories for dropdown
            $stmt = $pdo->query("SELECT category_id, category_name FROM categories ORDER BY category_name");
            $categories = $stmt->fetchAll();

            // Get makes with category info
            $stmt = $pdo->query("
                SELECT m.*, c.category_name 
                FROM category_makes m 
                JOIN categories c ON m.category_id = c.category_id 
                ORDER BY c.category_name, m.make_name
            ");
            $makes = $stmt->fetchAll();
            ?>
            
            <div class="page-header fade-in">
                <div class="container">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1><i class="fas fa-industry me-2"></i>Makes Management</h1>
                            <p class="mb-0">Manage vehicle makes for all categories</p>
                        </div>
                        <a href="admin.php" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="card slide-in-left">
                        <div class="card-header">
                            <h5><i class="fas fa-plus me-2"></i>Add New Make</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="admin.php">
                                <input type="hidden" name="action" value="add_make">
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-tags"></i>Category</label>
                                    <select class="form-control" name="category_id" required>
                                        <option value="">Select Category</option>
                                        <?php foreach ($categories as $cat): ?>
                                            <option value="<?= $cat['category_id'] ?>"><?= htmlspecialchars($cat['category_name']) ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-industry"></i>Make Name</label>
                                    <input type="text" class="form-control" name="make_name" required placeholder="e.g., Mercedes Benz">
                                </div>
                                
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-plus"></i>Add Make
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-8">
                    <div class="card fade-in">
                        <div class="card-header">
                            <h5><i class="fas fa-list me-2"></i>Current Makes (<?= count($makes) ?>)</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($makes)): ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-industry fa-4x text-muted mb-3"></i>
                                    <h4>No Makes Yet</h4>
                                    <p class="text-muted">Add your first vehicle make using the form on the left.</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Category</th>
                                                <th>Make Name</th>
                                                <th>Created</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($makes as $make): ?>
                                            <tr>
                                                <td>
                                                    <span class="badge bg-primary"><?= htmlspecialchars($make['category_name']) ?></span>
                                                </td>
                                                <td>
                                                    <strong><?= htmlspecialchars($make['make_name']) ?></strong>
                                                </td>
                                                <td><?= date('M j, Y', strtotime($make['created_at'])) ?></td>
                                                <td>
                                                    <a href="?action=manage_models&make_id=<?= $make['make_id'] ?>" class="btn btn-info btn-sm">
                                                        <i class="fas fa-car"></i> Manage Models
                                                    </a>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php
            break;

        case 'manage_models':
            $make_id = $_GET['make_id'] ?? 0;
            
            // Get make info
            $stmt = $pdo->prepare("SELECT m.*, c.category_name FROM category_makes m JOIN categories c ON m.category_id = c.category_id WHERE m.make_id = ?");
            $stmt->execute([$make_id]);
            $make = $stmt->fetch();
            
            if (!$make) {
                header("Location: admin.php?action=manage_makes");
                exit;
            }
            
            // Get models for this make
            $stmt = $pdo->prepare("SELECT * FROM category_models WHERE make_id = ? ORDER BY model_name");
            $stmt->execute([$make_id]);
            $models = $stmt->fetchAll();
            ?>
            
            <div class="page-header fade-in">
                <div class="container">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1><i class="fas fa-car me-2"></i>Models for <?= htmlspecialchars($make['make_name']) ?></h1>
                            <p class="mb-0">Manage models for <?= htmlspecialchars($make['category_name']) ?> category</p>
                        </div>
                        <a href="admin.php?action=manage_makes" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left"></i>Back to Makes
                        </a>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="card slide-in-left">
                        <div class="card-header">
                            <h5><i class="fas fa-plus me-2"></i>Add New Model</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="admin.php">
                                <input type="hidden" name="action" value="add_model">
                                <input type="hidden" name="make_id" value="<?= $make_id ?>">
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-industry"></i>Make</label>
                                    <input type="text" class="form-control" value="<?= htmlspecialchars($make['make_name']) ?>" readonly>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-car"></i>Model Name</label>
                                    <input type="text" class="form-control" name="model_name" required placeholder="e.g., Actros">
                                </div>
                                
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-plus"></i>Add Model
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-8">
                    <div class="card fade-in">
                        <div class="card-header">
                            <h5><i class="fas fa-list me-2"></i>Models (<?= count($models) ?>)</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($models)): ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-car fa-4x text-muted mb-3"></i>
                                    <h4>No Models Yet</h4>
                                    <p class="text-muted">Add your first model for <?= htmlspecialchars($make['make_name']) ?> using the form on the left.</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Model Name</th>
                                                <th>Created</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($models as $model): ?>
                                            <tr>
                                                <td>
                                                    <strong><?= htmlspecialchars($model['model_name']) ?></strong>
                                                </td>
                                                <td><?= date('M j, Y', strtotime($model['created_at'])) ?></td>
                                                <td>
                                                    <a href="?action=manage_variants&model_id=<?= $model['model_id'] ?>" class="btn btn-info btn-sm">
                                                        <i class="fas fa-cogs"></i> Manage Variants
                                                    </a>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php
            break;

        case 'manage_variants':
            $model_id = $_GET['model_id'] ?? 0;
            
            // Get model info
            $stmt = $pdo->prepare("
                SELECT mo.*, ma.make_name, c.category_name 
                FROM category_models mo 
                JOIN category_makes ma ON mo.make_id = ma.make_id 
                JOIN categories c ON ma.category_id = c.category_id 
                WHERE mo.model_id = ?
            ");
            $stmt->execute([$model_id]);
            $model = $stmt->fetch();
            
            if (!$model) {
                header("Location: admin.php?action=manage_makes");
                exit;
            }
            
            // Get variants for this model
            $stmt = $pdo->prepare("SELECT * FROM category_variants WHERE model_id = ? ORDER BY variant_name");
            $stmt->execute([$model_id]);
            $variants = $stmt->fetchAll();
            ?>
            
            <div class="page-header fade-in">
                <div class="container">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1><i class="fas fa-cogs me-2"></i>Variants for <?= htmlspecialchars($model['make_name'] . ' ' . $model['model_name']) ?></h1>
                            <p class="mb-0">Manage variants for <?= htmlspecialchars($model['category_name']) ?> category</p>
                        </div>
                        <a href="admin.php?action=manage_models&make_id=<?= $model['make_id'] ?>" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left"></i>Back to Models
                        </a>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="card slide-in-left">
                        <div class="card-header">
                            <h5><i class="fas fa-plus me-2"></i>Add New Variant</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="admin.php">
                                <input type="hidden" name="action" value="add_variant">
                                <input type="hidden" name="model_id" value="<?= $model_id ?>">
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-car"></i>Model</label>
                                    <input type="text" class="form-control" value="<?= htmlspecialchars($model['make_name'] . ' ' . $model['model_name']) ?>" readonly>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-cogs"></i>Variant Name</label>
                                    <input type="text" class="form-control" name="variant_name" required placeholder="e.g., 1844 LS">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-info-circle"></i>Description</label>
                                    <textarea class="form-control" name="variant_description" rows="3" placeholder="Optional description..."></textarea>
                                </div>
                                
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-plus"></i>Add Variant
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-8">
                    <div class="card fade-in">
                        <div class="card-header">
                            <h5><i class="fas fa-list me-2"></i>Variants (<?= count($variants) ?>)</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($variants)): ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-cogs fa-4x text-muted mb-3"></i>
                                    <h4>No Variants Yet</h4>
                                    <p class="text-muted">Add your first variant for <?= htmlspecialchars($model['make_name'] . ' ' . $model['model_name']) ?> using the form on the left.</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Variant Name</th>
                                                <th>Description</th>
                                                <th>Created</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($variants as $variant): ?>
                                            <tr>
                                                <td>
                                                    <strong><?= htmlspecialchars($variant['variant_name']) ?></strong>
                                                </td>
                                                <td><?= htmlspecialchars($variant['variant_description'] ?? 'No description') ?></td>
                                                <td><?= date('M j, Y', strtotime($variant['created_at'])) ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php
            break;

        case 'manage_years':
            // Get all years
            $stmt = $pdo->query("SELECT * FROM system_years ORDER BY year_value DESC");
            $years = $stmt->fetchAll();
            ?>
            
            <div class="page-header fade-in">
                <div class="container">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1><i class="fas fa-calendar me-2"></i>System Years Management</h1>
                            <p class="mb-0">Manage available years for vehicle listings</p>
                        </div>
                        <a href="admin.php" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="card slide-in-left">
                        <div class="card-header">
                            <h5><i class="fas fa-plus me-2"></i>Add New Year</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="admin.php">
                                <input type="hidden" name="action" value="add_year">
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-calendar"></i>Year Value</label>
                                    <input type="number" class="form-control" name="year_value" required min="1900" max="<?= date('Y') + 2 ?>" placeholder="e.g., <?= date('Y') ?>">
                                </div>
                                
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-plus"></i>Add Year
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-8">
                    <div class="card fade-in">
                        <div class="card-header">
                            <h5><i class="fas fa-list me-2"></i>Available Years (<?= count($years) ?>)</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($years)): ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-calendar fa-4x text-muted mb-3"></i>
                                    <h4>No Years Configured</h4>
                                    <p class="text-muted">Add years to make them available for vehicle listings.</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Year</th>
                                                <th>Created</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($years as $year): ?>
                                            <tr>
                                                <td>
                                                    <strong><?= htmlspecialchars($year['year_value']) ?></strong>
                                                </td>
                                                <td><?= date('M j, Y', strtotime($year['created_at'])) ?></td>
                                                <td>
                                                    <span class="badge bg-success">Active</span>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php
            break;

        case 'active_dealers':
            $stmt = $pdo->query("SELECT * FROM users WHERE user_type = 'dealer' AND status = 'active' ORDER BY company_name");
            $active_dealers = $stmt->fetchAll();
            ?>
            
            <div class="page-header fade-in">
                <div class="container">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1><i class="fas fa-users me-2"></i>Active Dealers</h1>
                            <p class="mb-0">Monitor and manage all active dealer accounts</p>
                        </div>
                        <a href="admin.php" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <div class="card fade-in">
                <div class="card-header">
                    <div class="row">
                        <div class="col-md-6">
                            <h5><i class="fas fa-check-circle me-2"></i>Active Dealers (<?= count($active_dealers) ?>)</h5>
                        </div>
                        <div class="col-md-6 text-end">
                            <span class="badge bg-success fs-6"><?= count($active_dealers) ?> Active</span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (empty($active_dealers)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-4x text-muted mb-3"></i>
                            <h4>No Active Dealers</h4>
                            <p class="text-muted">No dealers are currently active in the system.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Company Name</th>
                                        <th>Contact Person</th>
                                        <th>Email</th>
                                        <th>Phone</th>
                                        <th>Registration Date</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($active_dealers as $dealer): ?>
                                    <tr>
                                        <td>
                                            <strong><?= htmlspecialchars($dealer['company_name']) ?></strong>
                                        </td>
                                        <td><?= htmlspecialchars($dealer['username']) ?></td>
                                        <td>
                                            <a href="mailto:<?= htmlspecialchars($dealer['email']) ?>">
                                                <?= htmlspecialchars($dealer['email']) ?>
                                            </a>
                                        </td>
                                        <td>
                                            <a href="tel:<?= htmlspecialchars($dealer['phone']) ?>">
                                                <?= htmlspecialchars($dealer['phone']) ?>
                                            </a>
                                        </td>
                                        <td><?= date('M j, Y', strtotime($dealer['registered_at'])) ?></td>
                                        <td>
                                            <span class="badge bg-success">Active</span>
                                        </td>
                                        <td>
                                            <a href="?action=dealer_limits&dealer_id=<?= $dealer['user_id'] ?>" class="btn btn-primary btn-sm">
                                                <i class="fas fa-cogs"></i> Manage Limits
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php
            break;

        case 'all_listings':
            // Get all listings with dealer info
            $stmt = $pdo->query("
                SELECT v.*, u.company_name, u.username 
                FROM vehicles v 
                JOIN users u ON v.dealer_id = u.user_id 
                ORDER BY v.created_at DESC 
                LIMIT 100
            ");
            $listings = $stmt->fetchAll();
            ?>
            
            <div class="page-header fade-in">
                <div class="container">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1><i class="fas fa-list me-2"></i>All Listings</h1>
                            <p class="mb-0">Monitor and manage all vehicle listings</p>
                        </div>
                        <a href="admin.php" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <div class="card fade-in">
                <div class="card-header">
                    <h5><i class="fas fa-table me-2"></i>Recent Listings (<?= count($listings) ?>)</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($listings)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-list fa-4x text-muted mb-3"></i>
                            <h4>No Listings Yet</h4>
                            <p class="text-muted">No vehicle listings have been created yet.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Vehicle</th>
                                        <th>Dealer</th>
                                        <th>Price</th>
                                        <th>Type</th>
                                        <th>Status</th>
                                        <th>Features</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($listings as $listing): ?>
                                    <tr>
                                        <td>
                                            <strong><?= htmlspecialchars($listing['make'] . ' ' . $listing['model']) ?></strong>
                                            <br><small class="text-muted"><?= htmlspecialchars($listing['year']) ?></small>
                                        </td>
                                        <td>
                                            <strong><?= htmlspecialchars($listing['company_name']) ?></strong>
                                            <br><small class="text-muted"><?= htmlspecialchars($listing['username']) ?></small>
                                        </td>
                                        <td>R<?= number_format($listing['price'], 0) ?></td>
                                        <td>
                                            <span class="badge listing-type-<?= str_replace('-', '_', $listing['listing_type']) ?>">
                                                <?= strtoupper($listing['listing_type']) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?= $listing['status'] == 'available' ? 'success' : ($listing['status'] == 'pending' ? 'warning' : 'secondary') ?>">
                                                <?= ucfirst($listing['status']) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="d-flex flex-wrap gap-1">
                                                <?php if ($listing['featured']): ?>
                                                    <span class="badge bg-danger">FEATURED</span>
                                                <?php endif; ?>
                                                <?php if ($listing['premium_listing']): ?>
                                                    <span class="badge bg-warning">PREMIUM</span>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td><?= date('M j, Y', strtotime($listing['created_at'])) ?></td>
                                        <td>
                                            <?php if ($listing['status'] == 'pending'): ?>
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="action" value="approve_listing">
                                                    <input type="hidden" name="vehicle_id" value="<?= $listing['vehicle_id'] ?>">
                                                    <button type="submit" class="btn btn-success btn-sm" onclick="return confirm('Approve this listing?')">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php
            break;

        case 'featured_management':
            // Get featured listings
            $stmt = $pdo->query("
                SELECT v.*, u.company_name, u.username 
                FROM vehicles v 
                JOIN users u ON v.dealer_id = u.user_id 
                WHERE v.featured = 1 
                ORDER BY v.featured_until ASC, v.created_at DESC
            ");
            $featured_listings = $stmt->fetchAll();
            ?>
            
            <div class="page-header fade-in">
                <div class="container">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1><i class="fas fa-fire me-2"></i>Featured Management</h1>
                            <p class="mb-0">Manage featured vehicle listings</p>
                        </div>
                        <a href="admin.php" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <div class="card fade-in">
                <div class="card-header">
                    <h5><i class="fas fa-fire me-2"></i>Featured Listings (<?= count($featured_listings) ?>)</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($featured_listings)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-fire fa-4x text-muted mb-3"></i>
                            <h4>No Featured Listings</h4>
                            <p class="text-muted">No vehicles are currently featured.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Vehicle</th>
                                        <th>Dealer</th>
                                        <th>Price</th>
                                        <th>Featured Until</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($featured_listings as $listing): ?>
                                    <tr>
                                        <td>
                                            <strong><?= htmlspecialchars($listing['make'] . ' ' . $listing['model']) ?></strong>
                                            <br><small class="text-muted"><?= htmlspecialchars($listing['year']) ?></small>
                                        </td>
                                        <td>
                                            <strong><?= htmlspecialchars($listing['company_name']) ?></strong>
                                            <br><small class="text-muted"><?= htmlspecialchars($listing['username']) ?></small>
                                        </td>
                                        <td>R<?= number_format($listing['price'], 0) ?></td>
                                        <td>
                                            <?php if ($listing['featured_until']): ?>
                                                <span class="badge bg-<?= strtotime($listing['featured_until']) < time() ? 'danger' : 'success' ?>">
                                                    <?= date('M j, Y', strtotime($listing['featured_until'])) ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-success">Unlimited</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?= $listing['status'] == 'available' ? 'success' : 'warning' ?>">
                                                <?= ucfirst($listing['status']) ?>
                                            </span>
                                        </td>
                                        <td><?= date('M j, Y', strtotime($listing['created_at'])) ?></td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-warning btn-sm" onclick="editFeatured(<?= $listing['vehicle_id'] ?>)">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-danger btn-sm" onclick="removeFeatured(<?= $listing['vehicle_id'] ?>)">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php
            break;

        case 'premium_management':
            // Get premium listings
            $stmt = $pdo->query("
                SELECT v.*, u.company_name, u.username 
                FROM vehicles v 
                JOIN users u ON v.dealer_id = u.user_id 
                WHERE v.premium_listing = 1 
                ORDER BY v.premium_until ASC, v.created_at DESC
            ");
            $premium_listings = $stmt->fetchAll();
            ?>
            
            <div class="page-header fade-in">
                <div class="container">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1><i class="fas fa-star me-2"></i>Premium Management</h1>
                            <p class="mb-0">Manage premium vehicle listings</p>
                        </div>
                        <a href="admin.php" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <div class="card fade-in">
                <div class="card-header">
                    <h5><i class="fas fa-star me-2"></i>Premium Listings (<?= count($premium_listings) ?>)</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($premium_listings)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-star fa-4x text-muted mb-3"></i>
                            <h4>No Premium Listings</h4>
                            <p class="text-muted">No vehicles are currently premium.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Vehicle</th>
                                        <th>Dealer</th>
                                        <th>Price</th>
                                        <th>Premium Until</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($premium_listings as $listing): ?>
                                    <tr>
                                        <td>
                                            <strong><?= htmlspecialchars($listing['make'] . ' ' . $listing['model']) ?></strong>
                                            <br><small class="text-muted"><?= htmlspecialchars($listing['year']) ?></small>
                                        </td>
                                        <td>
                                            <strong><?= htmlspecialchars($listing['company_name']) ?></strong>
                                            <br><small class="text-muted"><?= htmlspecialchars($listing['username']) ?></small>
                                        </td>
                                        <td>R<?= number_format($listing['price'], 0) ?></td>
                                        <td>
                                            <?php if ($listing['premium_until']): ?>
                                                <span class="badge bg-<?= strtotime($listing['premium_until']) < time() ? 'danger' : 'success' ?>">
                                                    <?= date('M j, Y', strtotime($listing['premium_until'])) ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-success">Unlimited</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?= $listing['status'] == 'available' ? 'success' : 'warning' ?>">
                                                <?= ucfirst($listing['status']) ?>
                                            </span>
                                        </td>
                                        <td><?= date('M j, Y', strtotime($listing['created_at'])) ?></td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-warning btn-sm" onclick="editPremium(<?= $listing['vehicle_id'] ?>)">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-danger btn-sm" onclick="removePremium(<?= $listing['vehicle_id'] ?>)">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php
            break;

        case 'system_settings':
            // Get all system settings
            try {
                $stmt = $pdo->query("SELECT * FROM system_settings ORDER BY setting_key");
                $settings = $stmt->fetchAll();
            } catch (PDOException $e) {
                $settings = [];
            }
            ?>
            
            <div class="page-header fade-in">
                <div class="container">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1><i class="fas fa-cogs me-2"></i>System Settings</h1>
                            <p class="mb-0">Configure global system settings and preferences</p>
                        </div>
                        <a href="admin.php" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <div class="card fade-in">
                        <div class="card-header">
                            <h5><i class="fas fa-sliders-h me-2"></i>System Configuration</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($settings)): ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-cogs fa-4x text-muted mb-3"></i>
                                    <h4>No Settings Configured</h4>
                                    <p class="text-muted">System settings will appear here once configured.</p>
                                </div>
                            <?php else: ?>
                                <form method="POST" action="admin.php">
                                    <input type="hidden" name="action" value="update_settings">
                                    
                                    <?php foreach ($settings as $setting): ?>
                                    <div class="mb-4 p-3 border rounded">
                                        <div class="row align-items-center">
                                            <div class="col-md-4">
                                                <label class="form-label">
                                                    <strong><?= ucwords(str_replace('_', ' ', $setting['setting_key'])) ?></strong>
                                                    <?php if ($setting['description']): ?>
                                                        <br><small class="text-muted"><?= htmlspecialchars($setting['description']) ?></small>
                                                    <?php endif; ?>
                                                </label>
                                            </div>
                                            <div class="col-md-8">
                                                <?php if ($setting['setting_type'] == 'boolean'): ?>
                                                    <select class="form-control" name="settings[<?= $setting['setting_key'] ?>]">
                                                        <option value="0" <?= $setting['setting_value'] == '0' ? 'selected' : '' ?>>Disabled</option>
                                                        <option value="1" <?= $setting['setting_value'] == '1' ? 'selected' : '' ?>>Enabled</option>
                                                    </select>
                                                <?php elseif ($setting['setting_type'] == 'number'): ?>
                                                    <input type="number" class="form-control" name="settings[<?= $setting['setting_key'] ?>]" value="<?= htmlspecialchars($setting['setting_value']) ?>">
                                                <?php else: ?>
                                                    <input type="text" class="form-control" name="settings[<?= $setting['setting_key'] ?>]" value="<?= htmlspecialchars($setting['setting_value']) ?>">
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                    
                                    <div class="text-center">
                                        <button type="submit" class="btn btn-primary btn-lg">
                                            <i class="fas fa-save me-2"></i>Save Settings
                                        </button>
                                    </div>
                                </form>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card slide-in-left">
                        <div class="card-header">
                            <h6><i class="fas fa-info-circle me-2"></i>Settings Guide</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <h6 class="text-primary">General Settings</h6>
                                <ul class="list-unstyled small">
                                    <li><strong>Site Name:</strong> Website display name</li>
                                    <li><strong>Maintenance Mode:</strong> Disable public access</li>
                                </ul>
                            </div>
                            
                            <div class="mb-3">
                                <h6 class="text-success">Dealer Settings</h6>
                                <ul class="list-unstyled small">
                                    <li><strong>Default Featured Limit:</strong> New dealer featured limit</li>
                                    <li><strong>Auto Approve:</strong> Skip manual approval</li>
                                </ul>
                            </div>
                            
                            <div class="mb-3">
                                <h6 class="text-warning">Listing Settings</h6>
                                <ul class="list-unstyled small">
                                    <li><strong>Max Images:</strong> Per listing limit</li>
                                    <li><strong>Max Videos:</strong> Per listing limit</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php
            break;

        case 'admin_logs':
            // Get admin activity logs with pagination
            $page = (int)($_GET['page'] ?? 1);
            $limit = 50;
            $offset = ($page - 1) * $limit;
            
            try {
                $stmt = $pdo->prepare("
                    SELECT a.*, u.username 
                    FROM admin_activity_log a 
                    JOIN users u ON a.admin_id = u.user_id 
                    ORDER BY a.created_at DESC 
                    LIMIT ? OFFSET ?
                ");
                $stmt->execute([$limit, $offset]);
                $logs = $stmt->fetchAll();
                
                // Get total count for pagination
                $stmt = $pdo->query("SELECT COUNT(*) FROM admin_activity_log");
                $total_logs = $stmt->fetchColumn();
                $total_pages = ceil($total_logs / $limit);
            } catch (PDOException $e) {
                $logs = [];
                $total_logs = 0;
                $total_pages = 0;
            }
            ?>
            
            <div class="page-header fade-in">
                <div class="container">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1><i class="fas fa-history me-2"></i>Admin Activity Logs</h1>
                            <p class="mb-0">Complete audit trail of administrative actions</p>
                        </div>
                        <a href="admin.php" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <div class="card fade-in">
                <div class="card-header">
                    <div class="row">
                        <div class="col-md-6">
                            <h5><i class="fas fa-table me-2"></i>Activity Log (<?= number_format($total_logs) ?> records)</h5>
                        </div>
                        <div class="col-md-6 text-end">
                            <?php if ($total_pages > 0): ?>
                                <span class="badge bg-info">Page <?= $page ?> of <?= $total_pages ?></span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (empty($logs)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-history fa-4x text-muted mb-3"></i>
                            <h4>No Activity Logs</h4>
                            <p class="text-muted">No administrative activity recorded yet.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Admin</th>
                                        <th>Action</th>
                                        <th>Target</th>
                                        <th>Description</th>
                                        <th>IP Address</th>
                                        <th>Date/Time</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($logs as $log): ?>
                                    <tr>
                                        <td>
                                            <strong><?= htmlspecialchars($log['username']) ?></strong>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?= 
                                                strpos($log['action'], 'CREATE') !== false ? 'success' : 
                                                (strpos($log['action'], 'DELETE') !== false ? 'danger' : 
                                                (strpos($log['action'], 'UPDATE') !== false ? 'warning' : 'info'))
                                            ?>">
                                                <?= htmlspecialchars($log['action']) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($log['target_type'] && $log['target_id']): ?>
                                                <small class="text-muted">
                                                    <?= htmlspecialchars($log['target_type']) ?> #<?= $log['target_id'] ?>
                                                </small>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?= $log['description'] ? htmlspecialchars($log['description']) : '-' ?>
                                        </td>
                                        <td>
                                            <small class="text-muted"><?= htmlspecialchars($log['ip_address']) ?></small>
                                        </td>
                                        <td>
                                            <?= date('M j, Y g:i A', strtotime($log['created_at'])) ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <?php if ($total_pages > 1): ?>
                            <nav aria-label="Activity log pagination">
                                <ul class="pagination justify-content-center">
                                    <?php if ($page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?action=admin_logs&page=<?= $page - 1 ?>">Previous</a>
                                        </li>
                                    <?php endif; ?>
                                    
                                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                        <li class="page-item <?= $i == $page ? 'active' : '' ?>">
                                            <a class="page-link" href="?action=admin_logs&page=<?= $i ?>"><?= $i ?></a>
                                        </li>
                                    <?php endfor; ?>
                                    
                                    <?php if ($page < $total_pages): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?action=admin_logs&page=<?= $page + 1 ?>">Next</a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
            <?php
            break;
    }
    ?>
</div>

<!-- JavaScript -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<script>
// Dealer Limits Management
function manageLimits(dealerId, dealerName, featuredLimit, premiumLimit, listingLimit, notes) {
    document.getElementById('modal_dealer_id').value = dealerId;
    document.getElementById('modal_dealer_name').textContent = dealerName;
    document.getElementById('modal_featured_limit').value = featuredLimit;
    document.getElementById('modal_premium_limit').value = premiumLimit;
    document.getElementById('modal_listing_limit').value = listingLimit;
    document.getElementById('modal_notes').value = notes;
    
    new bootstrap.Modal(document.getElementById('limitsModal')).show();
}

// Ad Management Functions
function editAd(adId) {
    console.log('Edit ad:', adId);
    // This would open an edit modal or redirect to edit page
    alert('Edit functionality would be implemented here for ad ID: ' + adId);
}

// Featured/Premium Management Functions
function editFeatured(vehicleId) {
    const newDate = prompt('Enter new featured expiry date (YYYY-MM-DD) or leave empty for unlimited:');
    if (newDate !== null) {
        const formData = new FormData();
        formData.append('action', 'set_featured');
        formData.append('vehicle_id', vehicleId);
        formData.append('featured_until', newDate);
        formData.append('featured', 'on');
        
        fetch(window.location.href, {
            method: 'POST',
            body: formData
        })
        .then(() => location.reload())
        .catch(error => console.error('Error:', error));
    }
}

function removeFeatured(vehicleId) {
    if (confirm('Are you sure you want to remove featured status from this listing?')) {
        const formData = new FormData();
        formData.append('action', 'set_featured');
        formData.append('vehicle_id', vehicleId);
        
        fetch(window.location.href, {
            method: 'POST',
            body: formData
        })
        .then(() => location.reload())
        .catch(error => console.error('Error:', error));
    }
}

function editPremium(vehicleId) {
    const newDate = prompt('Enter new premium expiry date (YYYY-MM-DD) or leave empty for unlimited:');
    if (newDate !== null) {
        const formData = new FormData();
        formData.append('action', 'set_premium');
        formData.append('vehicle_id', vehicleId);
        formData.append('premium_until', newDate);
        formData.append('premium_listing', 'on');
        
        fetch(window.location.href, {
            method: 'POST',
            body: formData
        })
        .then(() => location.reload())
        .catch(error => console.error('Error:', error));
    }
}

function removePremium(vehicleId) {
    if (confirm('Are you sure you want to remove premium status from this listing?')) {
        const formData = new FormData();
        formData.append('action', 'set_premium');
        formData.append('vehicle_id', vehicleId);
        
        fetch(window.location.href, {
            method: 'POST',
            body: formData
        })
        .then(() => location.reload())
        .catch(error => console.error('Error:', error));
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    })
    
    // Add hover effects to buttons
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
    
    console.log('🚛 TrucksONSale Admin System Ready!');
});
</script>

</body>
</html>

<?php
// Clean output buffer and flush
ob_end_flush();
?>