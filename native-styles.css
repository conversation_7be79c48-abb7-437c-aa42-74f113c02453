/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, #FF0000 0%, #CC0000 100%);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.logo h1 {
    font-size: 1.8rem;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.header-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.btn-primary {
    background-color: #FF8C00;
    color: white;
}

.btn-primary:hover {
    background-color: #FF7700;
    transform: translateY(-2px);
}

.btn-secondary {
    background-color: transparent;
    color: white;
    border: 2px solid white;
}

.btn-secondary:hover {
    background-color: white;
    color: #FF0000;
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

.btn-app {
    background-color: #333;
    color: white;
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

.btn-app:hover {
    background-color: #555;
}

/* Main Content */
.main {
    min-height: calc(100vh - 200px);
    padding: 2rem 0;
}

/* Vehicle Detail Styles */
.vehicle-detail {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.vehicle-header {
    padding: 2rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.vehicle-header h2 {
    font-size: 2rem;
    color: #333;
    margin: 0;
}

.price {
    font-size: 2rem;
    font-weight: bold;
    color: #FF0000;
}

/* Gallery Styles */
.vehicle-gallery {
    padding: 2rem;
}

.main-image {
    margin-bottom: 1rem;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.main-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    display: block;
}

.image-thumbnails {
    display: flex;
    gap: 0.5rem;
    overflow-x: auto;
    padding: 0.5rem 0;
}

.thumbnail {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: 6px;
    cursor: pointer;
    opacity: 0.7;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.thumbnail:hover,
.thumbnail.active {
    opacity: 1;
    border-color: #FF0000;
    transform: scale(1.05);
}

/* Vehicle Info Styles */
.vehicle-info {
    padding: 2rem;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #FF0000;
}

.info-item .label {
    font-weight: 600;
    color: #666;
}

.info-item .value {
    font-weight: bold;
    color: #333;
}

.description {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.description h3 {
    margin-bottom: 1rem;
    color: #333;
}

.dealer-info {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border-radius: 8px;
}

.dealer-info h3 {
    margin-bottom: 1rem;
    color: #333;
}

.dealer-details p {
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Search Results Styles */
.search-results {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.results-header {
    margin-bottom: 2rem;
    text-align: center;
}

.results-header h2 {
    font-size: 2rem;
    color: #333;
    margin-bottom: 0.5rem;
}

.results-header p {
    color: #666;
    font-size: 1.1rem;
}

.vehicles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.vehicle-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    cursor: pointer;
}

.vehicle-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.card-image {
    height: 200px;
    overflow: hidden;
}

.card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.vehicle-card:hover .card-image img {
    transform: scale(1.05);
}

.card-content {
    padding: 1.5rem;
}

.card-content h3 {
    font-size: 1.3rem;
    color: #333;
    margin-bottom: 0.5rem;
}

.card-content .model {
    color: #FF8C00;
    font-weight: 600;
    margin-bottom: 1rem;
}

.card-content .price {
    font-size: 1.5rem;
    font-weight: bold;
    color: #FF0000;
    margin-bottom: 1rem;
}

.card-content .location {
    color: #666;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.card-content .details {
    display: flex;
    gap: 1rem;
    color: #888;
    font-size: 0.9rem;
}

.no-results {
    text-align: center;
    padding: 3rem;
    color: #666;
}

.no-results i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #ddd;
}

.no-results h3 {
    margin-bottom: 1rem;
    color: #333;
}

/* Footer Styles */
.footer {
    background: #333;
    color: white;
    padding: 2rem 0 1rem;
    margin-top: 3rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    margin-bottom: 1rem;
    color: #FF8C00;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #FF8C00;
}

.app-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.footer-bottom {
    text-align: center;
    padding-top: 1rem;
    border-top: 1px solid #555;
    color: #ccc;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .header-content {
        flex-direction: column;
        text-align: center;
    }
    
    .vehicle-header {
        flex-direction: column;
        text-align: center;
    }
    
    .vehicle-header h2 {
        font-size: 1.5rem;
    }
    
    .price {
        font-size: 1.5rem;
    }
    
    .main-image img {
        height: 250px;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .vehicles-grid {
        grid-template-columns: 1fr;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .logo h1 {
        font-size: 1.4rem;
    }
    
    .btn {
        padding: 0.6rem 1rem;
        font-size: 0.8rem;
    }
    
    .vehicle-info,
    .vehicle-gallery {
        padding: 1rem;
    }
    
    .search-results {
        padding: 1rem;
    }
}
