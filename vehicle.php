<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database connection
$db = new mysqli('localhost', 'root', '', 'truc_tos');
if ($db->connect_error) {
    die("Connection failed: " . $db->connect_error);
}

// Handle form submission
$inquiry_success = false;
$inquiry_error = null;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_inquiry'])) {
    $vehicle_id = intval($_POST['vehicle_id']);
    $full_name = $db->real_escape_string($_POST['full_name']);
    $email = $db->real_escape_string($_POST['email']);
    $phone = $db->real_escape_string($_POST['phone']);
    $message = $db->real_escape_string($_POST['message']);
    $preferred_contact = $db->real_escape_string($_POST['preferred_contact'] ?? 'phone');
    $inquiry_type = $db->real_escape_string($_POST['inquiry_type'] ?? 'general');

    if (empty($full_name) || empty($email) || empty($phone) || empty($message)) {
        $inquiry_error = "Please fill in all required fields";
    } else {
        $query = "INSERT INTO inquiries (vehicle_id, full_name, email, phone, message, preferred_contact, inquiry_type) 
                  VALUES (?, ?, ?, ?, ?, ?, ?)";
        $stmt = $db->prepare($query);
        $stmt->bind_param("issssss", $vehicle_id, $full_name, $email, $phone, $message, $preferred_contact, $inquiry_type);
        
        if ($stmt->execute()) {
            $inquiry_success = true;
        } else {
            $inquiry_error = "Failed to submit inquiry: " . $stmt->error;
        }
    }
}

// Get vehicle ID with validation
$vehicle_id = isset($_GET['id']) ? intval($_GET['id']) : 0;
if ($vehicle_id <= 0) {
    header("HTTP/1.0 400 Bad Request");
    die('<div style="padding: 1rem; background-color: #f8d7da; border-left: 4px solid #dc3545; border-radius: 4px; margin: 1rem;">Invalid vehicle ID. Please provide a valid numeric ID in the URL (e.g., vehicle.php?id=123)</div>');
}

// Fetch vehicle details
$query = "SELECT 
            v.*, 
            vi.image_path AS main_image,
            CONCAT(v.region, ', ', v.city) AS location
          FROM vehicles v
          LEFT JOIN vehicle_images vi ON v.vehicle_id = vi.vehicle_id AND vi.is_primary = 1
          WHERE v.vehicle_id = ? AND v.dealer_id = 3";
          
$stmt = $db->prepare($query);
if (!$stmt) {
    die('<div style="padding: 1rem; background-color: #f8d7da; border-left: 4px solid #dc3545; border-radius: 4px; margin: 1rem;">Prepare failed: ' . htmlspecialchars($db->error) . '</div>');
}

$stmt->bind_param("i", $vehicle_id);
if (!$stmt->execute()) {
    die('<div style="padding: 1rem; background-color: #f8d7da; border-left: 4px solid #dc3545; border-radius: 4px; margin: 1rem;">Execute failed: ' . htmlspecialchars($stmt->error) . '</div>');
}

$result = $stmt->get_result();
$vehicle = $result->fetch_assoc();

if (!$vehicle) {
    // Check if vehicle exists but with different dealer_id
    $check_query = "SELECT vehicle_id, dealer_id FROM vehicles WHERE vehicle_id = ?";
    $check_stmt = $db->prepare($check_query);
    $check_stmt->bind_param("i", $vehicle_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    if ($check_row = $check_result->fetch_assoc()) {
        header("HTTP/1.0 403 Forbidden");
        die('<div style="padding: 1rem; background-color: #fff3cd; border-left: 4px solid #ffc107; border-radius: 4px; margin: 1rem;">Vehicle exists but belongs to dealer '.$check_row['dealer_id'].' (required dealer_id: 3)</div>');
    } else {
        header("HTTP/1.0 404 Not Found");
        die('<div style="padding: 1rem; background-color: #f8d7da; border-left: 4px solid #dc3545; border-radius: 4px; margin: 1rem;">Vehicle with ID '.$vehicle_id.' not found in database</div>');
    }
}

// Fetch all images
$images_query = "SELECT image_path FROM vehicle_images WHERE vehicle_id = ? ORDER BY is_primary DESC";
$stmt = $db->prepare($images_query);
$stmt->bind_param("i", $vehicle_id);
$stmt->execute();
$images = $stmt->get_result();

// Format price
$formatted_price = 'R' . number_format(floatval($vehicle['price']), 2);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars("{$vehicle['year']} {$vehicle['make']} {$vehicle['model']}") ?> - TrucksOnSale</title>
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?= htmlspecialchars("{$vehicle['year']} {$vehicle['make']} {$vehicle['model']}") ?>">
    <meta property="og:description" content="<?= htmlspecialchars("Available at TrucksOnSale - $formatted_price") ?>">
    <meta property="og:image" content="<?= htmlspecialchars("https://trucksonsale.co.za/{$vehicle['main_image']}") ?>">
    <meta property="og:url" content="<?= htmlspecialchars("https://trucksonsale.co.za/vehicle.php?id={$vehicle['vehicle_id']}") ?>">
    <meta property="og:type" content="website">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        :root {
            --dark-blue: #0d1b2a;
            --red: #e63946;
            --light-gray: #f8f9fa;
            --medium-gray: #e9ecef;
            --dark-gray: #6c757d;
        }
        
        body {
            background-color: var(--light-gray);
            color: #333;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .carousel-item img { 
            height: 500px; 
            object-fit: cover; 
        }
        
        .carousel-control-prev-icon,
        .carousel-control-next-icon {
            background-color: var(--dark-blue);
            border-radius: 50%;
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
        }
        
        .card-title {
            color: var(--dark-blue);
            font-weight: 700;
        }
        
        .price-display {
            background-color: var(--red);
            color: white;
            padding: 1.5rem;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 1.5rem;
            font-size: 2rem;
            font-weight: 700;
        }
        
        .btn-primary {
            background-color: var(--dark-blue);
            border-color: var(--dark-blue);
        }
        
        .btn-danger {
            background-color: var(--red);
            border-color: var(--red);
        }
        
        .btn-outline-primary {
            color: var(--dark-blue);
            border-color: var(--dark-blue);
        }
        
        .btn-outline-primary:hover {
            background-color: var(--dark-blue);
            color: white;
        }
        
        .specs-badge {
            margin-right: 5px;
            margin-bottom: 5px;
            background-color: var(--medium-gray);
            color: var(--dark-blue);
            font-weight: 600;
            padding: 0.5rem;
            border-radius: 6px;
        }
        
        .feature-badge {
            background-color: var(--red);
            color: white;
            padding: 0.35em 0.65em;
            border-radius: 50rem;
            font-size: 0.75rem;
            font-weight: 600;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            display: inline-block;
        }
        
        .inquiry-form {
            background-color: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        
        .sticky-sidebar {
            position: sticky;
            top: 20px;
        }
        
        .dealer-avatar {
            width: 60px;
            height: 60px;
            background-color: var(--dark-blue);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            font-weight: 700;
            margin: 0 auto 1rem;
        }
        
        .alert-success {
            background-color: #d1e7dd;
            border-left: 4px solid #198754;
            color: #0f5132;
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 1.5rem;
        }
        
        .alert-danger {
            background-color: #f8d7da;
            border-left: 4px solid #dc3545;
            color: #842029;
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 1.5rem;
        }
        
        .alert-warning {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            color: #664d03;
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 1.5rem;
        }
        
        h1, h2, h3, h4, h5, h6 {
            color: var(--dark-blue);
        }
        
        .text-red {
            color: var(--red);
        }
        
        .bg-dark-blue {
            background-color: var(--dark-blue);
        }
    </style>
</head>
<body>
    <div class="container py-4">
        <div class="row">
            <div class="col-lg-8">
                <!-- Image Carousel -->
                <?php if ($images->num_rows > 0): ?>
                <div id="vehicleCarousel" class="carousel slide mb-4" data-bs-ride="carousel">
                    <div class="carousel-inner rounded">
                        <?php $first = true; $images->data_seek(0); ?>
                        <?php while ($image = $images->fetch_assoc()): ?>
                            <div class="carousel-item <?= $first ? 'active' : '' ?>">
                                <img src="<?= htmlspecialchars($image['image_path']) ?>" class="d-block w-100" alt="Vehicle image">
                            </div>
                            <?php $first = false; ?>
                        <?php endwhile; ?>
                    </div>
                    <?php if ($images->num_rows > 1): ?>
                    <button class="carousel-control-prev" type="button" data-bs-target="#vehicleCarousel" data-bs-slide="prev">
                        <span class="carousel-control-prev-icon"></span>
                    </button>
                    <button class="carousel-control-next" type="button" data-bs-target="#vehicleCarousel" data-bs-slide="next">
                        <span class="carousel-control-next-icon"></span>
                    </button>
                    <?php endif; ?>
                </div>
                <?php else: ?>
                    <div class="alert alert-warning">No images available for this vehicle</div>
                <?php endif; ?>

                <!-- Vehicle Title and Price -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><?= htmlspecialchars("{$vehicle['year']} {$vehicle['make']} {$vehicle['model']}") ?></h1>
                    <div class="price-display"><?= $formatted_price ?></div>
                </div>
                
                <!-- Features Badges -->
                <div class="mb-4">
                    <?php if ($vehicle['no_accidents']): ?>
                        <span class="feature-badge"><i class="bi bi-check-circle"></i> No Accidents</span>
                    <?php endif; ?>
                    <?php if ($vehicle['warranty']): ?>
                        <span class="feature-badge"><i class="bi bi-shield-check"></i> Warranty</span>
                    <?php endif; ?>
                    <?php if ($vehicle['service_history']): ?>
                        <span class="feature-badge"><i class="bi bi-clipboard-check"></i> Service History</span>
                    <?php endif; ?>
                    <?php if ($vehicle['roadworthy']): ?>
                        <span class="feature-badge"><i class="bi bi-check2-all"></i> Roadworthy</span>
                    <?php endif; ?>
                </div>

                <!-- Specifications -->
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title"><i class="bi bi-gear text-red"></i> Specifications</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="specs-badge">
                                    <i class="bi bi-speedometer2 text-red"></i> 
                                    <strong>Mileage:</strong> <?= number_format($vehicle['mileage']) ?> km
                                </div>
                                <div class="specs-badge">
                                    <i class="bi bi-gear text-red"></i> 
                                    <strong>Engine:</strong> <?= htmlspecialchars($vehicle['engine_type'] ?? 'N/A') ?>
                                </div>
                                <div class="specs-badge">
                                    <i class="bi bi-calendar text-red"></i> 
                                    <strong>Year:</strong> <?= htmlspecialchars($vehicle['year']) ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="specs-badge">
                                    <i class="bi bi-gear-wide text-red"></i> 
                                    <strong>Transmission:</strong> <?= htmlspecialchars($vehicle['transmission'] ?? 'N/A') ?>
                                </div>
                                <div class="specs-badge">
                                    <i class="bi bi-fuel-pump text-red"></i> 
                                    <strong>Fuel Type:</strong> <?= htmlspecialchars($vehicle['fuel_type'] ?? 'N/A') ?>
                                </div>
                                <div class="specs-badge">
                                    <i class="bi bi-geo-alt text-red"></i> 
                                    <strong>Location:</strong> <?= htmlspecialchars($vehicle['location'] ?? 'N/A') ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Description -->
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title"><i class="bi bi-card-text text-red"></i> Description</h5>
                        <p><?= !empty($vehicle['description']) ? nl2br(htmlspecialchars($vehicle['description'])) : 'No description available' ?></p>
                    </div>
                </div>

                <!-- Inquiry Form -->
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title"><i class="bi bi-envelope-heart text-red"></i> Make an Inquiry</h5>
                        
                        <?php if ($inquiry_success): ?>
                            <div class="alert-success">
                                <i class="bi bi-check-circle"></i> Your inquiry has been sent successfully!
                            </div>
                        <?php elseif ($inquiry_error): ?>
                            <div class="alert-danger">
                                <i class="bi bi-exclamation-circle"></i> <?= htmlspecialchars($inquiry_error) ?>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" action="">
                            <input type="hidden" name="vehicle_id" value="<?= $vehicle['vehicle_id'] ?>">
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">Full Name *</label>
                                    <input type="text" class="form-control" name="full_name" required>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Email *</label>
                                    <input type="email" class="form-control" name="email" required>
                                </div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">Phone *</label>
                                    <input type="tel" class="form-control" name="phone" required>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Preferred Contact</label>
                                    <select class="form-select" name="preferred_contact">
                                        <option value="phone">Phone Call</option>
                                        <option value="email">Email</option>
                                        <option value="whatsapp">WhatsApp</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Inquiry Type</label>
                                <select class="form-select" name="inquiry_type">
                                    <option value="general">General Information</option>
                                    <option value="finance">Finance Options</option>
                                    <option value="trade_in">Trade-in Value</option>
                                    <option value="inspection">Schedule Inspection</option>
                                    <option value="test_drive">Test Drive</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Your Message *</label>
                                <textarea class="form-control" name="message" rows="4" required><?= "I am interested in this {$vehicle['year']} {$vehicle['make']} {$vehicle['model']}. Please contact me with more information." ?></textarea>
                            </div>
                            
                            <button type="submit" name="submit_inquiry" class="btn btn-danger w-100">
                                <i class="bi bi-send"></i> Send Inquiry
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Dealer Information -->
                <div class="card sticky-sidebar">
                    <div class="card-body text-center">
                        <div class="dealer-avatar">
                            <?= strtoupper(substr('TrucksOnSale', 0, 2)) ?>
                        </div>
                        <h5>TrucksOnSale</h5>
                        <p class="text-success small mb-4">
                            <i class="bi bi-patch-check-fill"></i> Verified Dealer
                        </p>
                        
                        <div class="d-grid gap-2 mb-4">
                            <a href="tel:+27123456789" class="btn btn-primary">
                                <i class="bi bi-telephone"></i> Call Dealer
                            </a>
                            <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#emailModal">
                                <i class="bi bi-envelope"></i> Email Dealer
                            </button>
                        </div>
                        
                        <div class="bg-light p-3 rounded">
                            <div class="d-flex align-items-center mb-2">
                                <i class="bi bi-geo-alt text-primary me-2"></i>
                                <span><?= htmlspecialchars($vehicle['location'] ?? 'N/A') ?></span>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <i class="bi bi-calendar text-info me-2"></i>
                                <span>Listed <?= date('M j, Y', strtotime($vehicle['created_at'])) ?></span>
                            </div>
                            <div class="d-flex align-items-center">
                                <i class="bi bi-eye text-secondary me-2"></i>
                                <span><?= number_format($vehicle['views'] ?? 0) ?> views</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Email Modal -->
    <div class="modal fade" id="emailModal" tabindex="-1" aria-labelledby="emailModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="emailModalLabel">Email Dealer</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>For inquiries about this vehicle, please use the inquiry form on the page or contact us directly at:</p>
                    <p class="text-center">
                        <a href="mailto:<EMAIL>" class="btn btn-primary">
                            <i class="bi bi-envelope"></i> <EMAIL>
                        </a>
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
<?php $db->close(); ?>