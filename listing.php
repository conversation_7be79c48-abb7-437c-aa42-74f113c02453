<?php
// Fix headers already sent by using output buffering
ob_start();

session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database Configuration
$host = 'localhost';
$db_name = 'truc_tos';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$db_name;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Enhanced database structure with all new requirements
    
    // Enhanced users table with password reset and status
    $pdo->exec("CREATE TABLE IF NOT EXISTS users (
        user_id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        phone VARCHAR(15) NOT NULL,
        company_name VARCHAR(100),
        company_registration VARCHAR(50),
        vat_number VARCHAR(20),
        physical_address TEXT,
        billing_address TEXT,
        user_type ENUM('customer', 'dealer', 'admin') DEFAULT 'customer',
        status ENUM('pending','active','suspended','rejected') DEFAULT 'pending',
        registered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_login TIMESTAMP NULL,
        profile_image VARCHAR(255),
        email_verified BOOLEAN DEFAULT FALSE,
        phone_verified BOOLEAN DEFAULT FALSE,
        password_reset_token VARCHAR(255) NULL,
        password_reset_expires TIMESTAMP NULL,
        listing_limit INT DEFAULT 10,
        premium_until DATE NULL,
        failed_login_attempts INT DEFAULT 0,
        locked_until TIMESTAMP NULL,
        INDEX idx_user_type (user_type),
        INDEX idx_status (status),
        INDEX idx_email (email),
        INDEX idx_reset_token (password_reset_token)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // Enhanced categories with listing types
    $pdo->exec("CREATE TABLE IF NOT EXISTS categories (
        category_id INT AUTO_INCREMENT PRIMARY KEY,
        category_key VARCHAR(50) UNIQUE NOT NULL,
        category_name VARCHAR(100) NOT NULL,
        parent_category VARCHAR(50) NULL,
        listing_type ENUM('sale', 'rent-to-own', 'hire', 'auction') DEFAULT 'sale',
        icon VARCHAR(100) NOT NULL DEFAULT 'fas fa-tag',
        listing_label VARCHAR(100) NOT NULL,
        mileage_label VARCHAR(50) DEFAULT 'Mileage (KM)',
        engine_label VARCHAR(50) DEFAULT 'Engine Type',
        show_transmission BOOLEAN DEFAULT TRUE,
        show_fuel_type BOOLEAN DEFAULT TRUE,
        show_year BOOLEAN DEFAULT TRUE,
        show_hours BOOLEAN DEFAULT FALSE,
        transmission_options TEXT,
        fuel_options TEXT,
        additional_fields TEXT,
        category_order INT DEFAULT 0,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_listing_type (listing_type),
        INDEX idx_parent_category (parent_category),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // Subcategories
    $pdo->exec("CREATE TABLE IF NOT EXISTS subcategories (
        subcategory_id INT AUTO_INCREMENT PRIMARY KEY,
        category_id INT NOT NULL,
        subcategory_key VARCHAR(50) NOT NULL,
        subcategory_name VARCHAR(100) NOT NULL,
        subcategory_order INT DEFAULT 0,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES categories(category_id) ON DELETE CASCADE,
        UNIQUE KEY unique_subcategory (category_id, subcategory_key),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // Enhanced makes system
    $pdo->exec("CREATE TABLE IF NOT EXISTS category_makes (
        make_id INT AUTO_INCREMENT PRIMARY KEY,
        category_id INT NOT NULL,
        make_name VARCHAR(100) NOT NULL,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES categories(category_id) ON DELETE CASCADE,
        UNIQUE KEY unique_make (category_id, make_name),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // Models system
    $pdo->exec("CREATE TABLE IF NOT EXISTS category_models (
        model_id INT AUTO_INCREMENT PRIMARY KEY,
        make_id INT NOT NULL,
        model_name VARCHAR(100) NOT NULL,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (make_id) REFERENCES category_makes(make_id) ON DELETE CASCADE,
        UNIQUE KEY unique_model (make_id, model_name),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // Variants system
    $pdo->exec("CREATE TABLE IF NOT EXISTS category_variants (
        variant_id INT AUTO_INCREMENT PRIMARY KEY,
        model_id INT NOT NULL,
        variant_name VARCHAR(100) NOT NULL,
        variant_description TEXT,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (model_id) REFERENCES category_models(model_id) ON DELETE CASCADE,
        UNIQUE KEY unique_variant (model_id, variant_name),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // System years
    $pdo->exec("CREATE TABLE IF NOT EXISTS system_years (
        year_id INT AUTO_INCREMENT PRIMARY KEY,
        year_value INT NOT NULL UNIQUE,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_year (year_value),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // Sales team management
    $pdo->exec("CREATE TABLE IF NOT EXISTS dealer_sales_team (
        team_id INT AUTO_INCREMENT PRIMARY KEY,
        dealer_id INT NOT NULL,
        name VARCHAR(100) NOT NULL,
        position VARCHAR(100),
        phone VARCHAR(15),
        email VARCHAR(100),
        whatsapp VARCHAR(15),
        photo VARCHAR(255),
        facebook_url VARCHAR(255),
        twitter_url VARCHAR(255),
        instagram_url VARCHAR(255),
        linkedin_url VARCHAR(255),
        youtube_url VARCHAR(255),
        tiktok_url VARCHAR(255),
        website_url VARCHAR(255),
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (dealer_id) REFERENCES users(user_id) ON DELETE CASCADE,
        INDEX idx_dealer (dealer_id),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // Dealer branches
    $pdo->exec("CREATE TABLE IF NOT EXISTS dealer_branches (
        branch_id INT AUTO_INCREMENT PRIMARY KEY,
        dealer_id INT NOT NULL,
        branch_name VARCHAR(100) NOT NULL,
        address TEXT NOT NULL,
        city VARCHAR(50) NOT NULL,
        region VARCHAR(50) NOT NULL,
        postal_code VARCHAR(10),
        phone VARCHAR(15),
        email VARCHAR(100),
        is_main_branch BOOLEAN DEFAULT FALSE,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (dealer_id) REFERENCES users(user_id) ON DELETE CASCADE,
        INDEX idx_dealer (dealer_id),
        INDEX idx_region (region),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // Enhanced vehicles table with all new fields
    $pdo->exec("CREATE TABLE IF NOT EXISTS vehicles (
        vehicle_id INT AUTO_INCREMENT PRIMARY KEY,
        dealer_id INT NOT NULL,
        category VARCHAR(50) NOT NULL,
        subcategory VARCHAR(50),
        listing_type ENUM('sale', 'rent-to-own', 'hire', 'auction') DEFAULT 'sale',
        condition_type ENUM('new', 'used', 'refurbished') DEFAULT 'used',
        make VARCHAR(50) NOT NULL,
        model VARCHAR(50) NOT NULL,
        variant VARCHAR(100),
        year INT,
        price DECIMAL(12,2) NOT NULL,
        mileage INT DEFAULT 0,
        hours_used INT DEFAULT 0,
        engine_type VARCHAR(50),
        engine_capacity VARCHAR(20),
        horsepower INT,
        transmission VARCHAR(50),
        fuel_type VARCHAR(50),
        color VARCHAR(30),
        vin_number VARCHAR(50),
        registration_number VARCHAR(20),
        region VARCHAR(50) NOT NULL,
        city VARCHAR(50) NOT NULL,
        branch_id INT NULL,
        condition_rating ENUM('excellent', 'very_good', 'good', 'fair', 'poor') DEFAULT 'good',
        no_accidents BOOLEAN DEFAULT FALSE,
        warranty BOOLEAN DEFAULT FALSE,
        warranty_details TEXT,
        finance_available BOOLEAN DEFAULT FALSE,
        trade_in BOOLEAN DEFAULT FALSE,
        service_history BOOLEAN DEFAULT FALSE,
        roadworthy BOOLEAN DEFAULT FALSE,
        description TEXT,
        features TEXT,
        youtube_video_url VARCHAR(255),
        daily_rate DECIMAL(8,2) NULL,
        weekly_rate DECIMAL(10,2) NULL,
        monthly_rate DECIMAL(12,2) NULL,
        auction_start_date DATETIME NULL,
        auction_end_date DATETIME NULL,
        reserve_price DECIMAL(12,2) NULL,
        current_bid DECIMAL(12,2) NULL,
        featured BOOLEAN DEFAULT FALSE,
        featured_until DATE NULL,
        premium_listing BOOLEAN DEFAULT FALSE,
        premium_until DATE NULL,
        status ENUM('available', 'sold', 'reserved', 'draft', 'pending_approval') DEFAULT 'pending_approval',
        views INT DEFAULT 0,
        leads_count INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        approved_at TIMESTAMP NULL,
        FOREIGN KEY (dealer_id) REFERENCES users(user_id) ON DELETE CASCADE,
        FOREIGN KEY (branch_id) REFERENCES dealer_branches(branch_id) ON DELETE SET NULL,
        INDEX idx_category (category),
        INDEX idx_listing_type (listing_type),
        INDEX idx_condition_type (condition_type),
        INDEX idx_status (status),
        INDEX idx_region (region),
        INDEX idx_dealer (dealer_id),
        INDEX idx_featured (featured),
        INDEX idx_premium (premium_listing),
        INDEX idx_created_at (created_at),
        INDEX idx_price (price),
        INDEX idx_year (year),
        FULLTEXT idx_search (make, model, description, features)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // Enhanced vehicle images with ordering
    $pdo->exec("CREATE TABLE IF NOT EXISTS vehicle_images (
        image_id INT AUTO_INCREMENT PRIMARY KEY,
        vehicle_id INT NOT NULL,
        image_path VARCHAR(255) NOT NULL,
        image_name VARCHAR(255),
        file_size INT,
        image_order INT DEFAULT 0,
        is_primary BOOLEAN DEFAULT FALSE,
        uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (vehicle_id) REFERENCES vehicles(vehicle_id) ON DELETE CASCADE,
        INDEX idx_vehicle (vehicle_id),
        INDEX idx_primary (is_primary),
        INDEX idx_order (image_order)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // Enhanced vehicle videos
    $pdo->exec("CREATE TABLE IF NOT EXISTS vehicle_videos (
        video_id INT AUTO_INCREMENT PRIMARY KEY,
        vehicle_id INT NOT NULL,
        video_path VARCHAR(255) NOT NULL,
        video_title VARCHAR(100),
        file_size BIGINT,
        duration_seconds INT,
        uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (vehicle_id) REFERENCES vehicles(vehicle_id) ON DELETE CASCADE,
        INDEX idx_vehicle (vehicle_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // Enhanced vehicle documents
    $pdo->exec("CREATE TABLE IF NOT EXISTS vehicle_documents (
        document_id INT AUTO_INCREMENT PRIMARY KEY,
        vehicle_id INT NOT NULL,
        document_path VARCHAR(255) NOT NULL,
        document_name VARCHAR(100) NOT NULL,
        document_type ENUM('pdf') NOT NULL DEFAULT 'pdf',
        file_size INT,
        uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (vehicle_id) REFERENCES vehicles(vehicle_id) ON DELETE CASCADE,
        INDEX idx_vehicle (vehicle_id),
        INDEX idx_type (document_type)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // Premium backgrounds for adverts
    $pdo->exec("CREATE TABLE IF NOT EXISTS premium_backgrounds (
        bg_id INT AUTO_INCREMENT PRIMARY KEY,
        image_path VARCHAR(255) NOT NULL,
        title VARCHAR(100),
        description TEXT,
        advertiser_name VARCHAR(100),
        advertiser_contact VARCHAR(100),
        advertiser_url VARCHAR(255),
        start_date DATE,
        end_date DATE,
        click_count INT DEFAULT 0,
        status ENUM('active', 'inactive', 'expired') DEFAULT 'active',
        priority INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_status (status),
        INDEX idx_dates (start_date, end_date)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // Auction bids
    $pdo->exec("CREATE TABLE IF NOT EXISTS auction_bids (
        bid_id INT AUTO_INCREMENT PRIMARY KEY,
        vehicle_id INT NOT NULL,
        bidder_name VARCHAR(100) NOT NULL,
        bidder_email VARCHAR(100) NOT NULL,
        bidder_phone VARCHAR(15) NOT NULL,
        bid_amount DECIMAL(12,2) NOT NULL,
        bid_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        status ENUM('active', 'outbid', 'winning', 'withdrawn') DEFAULT 'active',
        FOREIGN KEY (vehicle_id) REFERENCES vehicles(vehicle_id) ON DELETE CASCADE,
        INDEX idx_vehicle (vehicle_id),
        INDEX idx_bid_time (bid_time),
        INDEX idx_amount (bid_amount)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // Hire bookings
    $pdo->exec("CREATE TABLE IF NOT EXISTS hire_bookings (
        booking_id INT AUTO_INCREMENT PRIMARY KEY,
        vehicle_id INT NOT NULL,
        customer_name VARCHAR(100) NOT NULL,
        email VARCHAR(100) NOT NULL,
        phone VARCHAR(15) NOT NULL,
        start_date DATE NOT NULL,
        end_date DATE NOT NULL,
        pickup_location VARCHAR(255),
        daily_rate DECIMAL(8,2),
        total_cost DECIMAL(12,2),
        status ENUM('pending', 'confirmed', 'active', 'completed', 'cancelled') DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (vehicle_id) REFERENCES vehicles(vehicle_id) ON DELETE CASCADE,
        INDEX idx_vehicle (vehicle_id),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // Enhanced inquiries
    $pdo->exec("CREATE TABLE IF NOT EXISTS inquiries (
        inquiry_id INT AUTO_INCREMENT PRIMARY KEY,
        vehicle_id INT NOT NULL,
        dealer_id INT NOT NULL,
        inquiry_type ENUM('general', 'finance', 'trade_in', 'inspection', 'test_drive') DEFAULT 'general',
        full_name VARCHAR(100) NOT NULL,
        email VARCHAR(100) NOT NULL,
        phone VARCHAR(15) NOT NULL,
        message TEXT NOT NULL,
        preferred_contact ENUM('phone', 'email', 'whatsapp') DEFAULT 'phone',
        status ENUM('new', 'contacted', 'in_progress', 'closed') DEFAULT 'new',
        priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
        follow_up_date DATE NULL,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        responded_at TIMESTAMP NULL,
        FOREIGN KEY (vehicle_id) REFERENCES vehicles(vehicle_id) ON DELETE CASCADE,
        FOREIGN KEY (dealer_id) REFERENCES users(user_id) ON DELETE CASCADE,
        INDEX idx_vehicle (vehicle_id),
        INDEX idx_dealer (dealer_id),
        INDEX idx_status (status),
        INDEX idx_type (inquiry_type)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // Check if default admin exists
    $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE user_type = 'admin'");
    $admin_count = $stmt->fetchColumn();
        // Update any existing pending_approval vehicles to available
    try {
        $pdo->exec("UPDATE vehicles SET status = 'available' WHERE status = 'pending_approval'");
    } catch (PDOException $e) {
        // Table may not exist yet or column may not have pending_approval
    }
    if ($admin_count == 0) {
        // Create default admin user
        $stmt = $pdo->prepare("INSERT INTO users (username, email, password, phone, company_name, user_type, status) VALUES (?, ?, ?, ?, ?, 'admin', 'active')");
        $stmt->execute([
            'admin',
            'a@za',
            password_hash('000000', PASSWORD_DEFAULT),
            '0000000000',
            'TrucksONSale Admin'
        ]);
    }
    
    // Check if default categories exist, if not create them
    $stmt = $pdo->query("SELECT COUNT(*) FROM categories");
    $category_count = $stmt->fetchColumn();
    
    if ($category_count == 0) {
        // Create only sale categories first, then others
        $base_categories = [
            ['trucks', 'Trucks', 'fas fa-truck', 'List Your Truck'],
            ['trailers', 'Trailers', 'fas fa-trailer', 'List Your Trailer'],
            ['buses', 'Buses', 'fas fa-bus', 'List Your Bus'],
            ['commercial_vehicles', 'Commercial Vehicles', 'fas fa-van-shuttle', 'List Your Commercial Vehicle'],
            ['farm_equipment', 'Farm Equipment', 'fas fa-tractor', 'List Your Farm Equipment'],
            ['heavy_machinery', 'Heavy Machinery', 'fas fa-cogs', 'List Your Heavy Machinery'],
            ['animal_farming_equipment', 'Animal Farming Equipment', 'fas fa-cow', 'List Your Animal Farming Equipment']
        ];
        
        $listing_types = ['sale', 'rent-to-own', 'hire', 'auction'];
        $order = 1;
        
        foreach ($listing_types as $listing_type) {
            foreach ($base_categories as $category) {
                $prefix = ($listing_type === 'sale') ? '' : $listing_type . '_';
                $suffix = ($listing_type === 'sale') ? '' : ' (' . ucwords(str_replace('-', ' ', $listing_type)) . ')';
                
                $stmt = $pdo->prepare("INSERT INTO categories (category_key, category_name, listing_type, icon, listing_label, category_order, show_hours) VALUES (?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute([
                    $prefix . $category[0],
                    $category[1] . $suffix,
                    $listing_type,
                    $category[2],
                    str_replace('List Your', ($listing_type === 'sale' ? 'List Your' : ucwords(str_replace('-', ' ', $listing_type)) . ':'), $category[3]),
                    $order,
                    in_array($category[0], ['heavy_machinery', 'farm_equipment']) ? 1 : 0
                ]);
                $order++;
            }
        }
        
        // Insert sample makes and models for ALL categories
        foreach ($base_categories as $index => $category) {
            $category_id = $index + 1; // Get the first category for each type (sale)
            
            if ($category[0] === 'trucks') {
                $sample_makes = [
                    'Mercedes Benz' => ['Actros', 'Atego', 'Axor', 'Antos', 'Arocs'],
                    'Volvo' => ['FH', 'FM', 'FE', 'FL', 'VNL'],
                    'Scania' => ['R Series', 'S Series', 'P Series', 'G Series'],
                    'MAN' => ['TGX', 'TGS', 'TGM', 'TGL'],
                    'DAF' => ['XF', 'CF', 'LF', 'XG'],
                    'Isuzu' => ['NPR', 'NQR', 'FTR', 'FVR', 'GXR'],
                    'Iveco' => ['Stralis', 'Trakker', 'Eurocargo', 'Daily']
                ];
            } elseif ($category[0] === 'trailers') {
                $sample_makes = [
                    'Afrit' => ['Side Tipper', 'Tri-Axle', 'Interlink'],
                    'SA Truck Bodies' => ['Flatdeck', 'Curtain Side', 'Dropside'],
                    'Henred' => ['Fruehauf', 'Goliath', 'Maxitrans']
                ];
            } elseif ($category[0] === 'buses') {
                $sample_makes = [
                    'Mercedes Benz' => ['Sprinter', 'Vario', 'Marco Polo'],
                    'Iveco' => ['Daily Minibus', 'Crossway', 'Evadys'],
                    'Volkswagen' => ['Crafter', 'Amarok']
                ];
            } elseif ($category[0] === 'farm_equipment') {
                $sample_makes = [
                    'John Deere' => ['6R Series', '7R Series', '8R Series'],
                    'Case IH' => ['Magnum', 'Puma', 'Maxxum'],
                    'New Holland' => ['T6', 'T7', 'T8']
                ];
            } elseif ($category[0] === 'heavy_machinery') {
                $sample_makes = [
                    'Caterpillar' => ['320D', '330D', '336D'],
                    'Komatsu' => ['PC200', 'PC300', 'PC400'],
                    'JCB' => ['3CX', '4CX', 'JS130']
                ];
            } else {
                // Default makes for other categories
                $sample_makes = [
                    'Brand A' => ['Model 1', 'Model 2', 'Model 3'],
                    'Brand B' => ['Model X', 'Model Y', 'Model Z']
                ];
            }
            
            foreach ($sample_makes as $make_name => $models) {
                $stmt = $pdo->prepare("INSERT INTO category_makes (category_id, make_name) VALUES (?, ?)");
                $stmt->execute([$category_id, $make_name]);
                $make_id = $pdo->lastInsertId();
                
                foreach ($models as $model_name) {
                    $stmt = $pdo->prepare("INSERT INTO category_models (make_id, model_name) VALUES (?, ?)");
                    $stmt->execute([$make_id, $model_name]);
                    
                    // Add variants for first model of each make
                    if ($model_name === reset($models)) {
                        $model_id = $pdo->lastInsertId();
                        $variants = ['Standard', 'Premium', 'Deluxe'];
                        foreach ($variants as $variant) {
                            $stmt = $pdo->prepare("INSERT INTO category_variants (model_id, variant_name) VALUES (?, ?)");
                            $stmt->execute([$model_id, $variant]);
                        }
                    }
                }
            }
        }
        
        // Add some subcategories
        $stmt = $pdo->prepare("INSERT INTO subcategories (category_id, subcategory_key, subcategory_name) VALUES 
            (1, 'light_duty', 'Light Duty Trucks'),
            (1, 'heavy_duty', 'Heavy Duty Trucks'),
            (1, 'medium_duty', 'Medium Duty Trucks'),
            (2, 'flatbed', 'Flatbed Trailers'),
            (2, 'enclosed', 'Enclosed Trailers'),
            (2, 'refrigerated', 'Refrigerated Trailers'),
            (3, 'passenger', 'Passenger Buses'),
            (3, 'school', 'School Buses'),
            (3, 'charter', 'Charter Buses')");
        $stmt->execute();
    }
    
    // Insert system years if not exist
    $years_check = $pdo->query("SELECT COUNT(*) as count FROM system_years");
    $years_exist = $years_check->fetch()['count'] > 0;
    
    if (!$years_exist) {
        $current_year = date('Y');
        for ($year = 1970; $year <= $current_year + 1; $year++) {
            $pdo->exec("INSERT INTO system_years (year_value) VALUES ($year)");
        }
    }
    
    // Create uploads directory
    if (!file_exists('uploads')) {
        mkdir('uploads', 0755, true);
    }
    
} catch(PDOException $e) {
    die("Database Connection Error: " . $e->getMessage());
}

// Enhanced Helper Functions

// Password validation
function validatePassword($password) {
    return preg_match('/^(?=.*[A-Za-z])(?=.*\d)(?=.*[@$!%*#?&])[A-Za-z\d@$!%*#?&]{8,}$/', $password);
}

// Generate CAPTCHA
function generateCaptcha() {
    $num1 = rand(1, 20);
    $num2 = rand(1, 20);
    $operation = rand(0, 1) ? '+' : '-';
    
    if ($operation === '-' && $num1 < $num2) {
        $temp = $num1;
        $num1 = $num2;
        $num2 = $temp;
    }
    
    $question = "$num1 $operation $num2";
    $answer = $operation === '+' ? $num1 + $num2 : $num1 - $num2;
    
    $_SESSION['captcha_answer'] = $answer;
    return $question;
}

// Enhanced file upload with size limits
function uploadFile($file, $upload_dir = 'uploads', $max_size = null) {
    if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
        return false;
    }
    
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    
    // Set size limits based on file type
    $size_limits = [
        'image' => 2 * 1024 * 1024, // 2MB for images
        'video' => 50 * 1024 * 1024, // 50MB for videos
        'document' => 10 * 1024 * 1024 // 10MB for documents
    ];
    
    $allowed_types = [
        'image' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
        'video' => ['mp4', 'avi', 'mov', 'wmv', 'flv'],
        'document' => ['pdf'] // PDF only
    ];
    
    $file_type = '';
    foreach ($allowed_types as $type => $extensions) {
        if (in_array($file_extension, $extensions)) {
            $file_type = $type;
            break;
        }
    }
    
    if (empty($file_type)) {
        return false;
    }
    
    // Check file size
    $max_allowed = $max_size ?: $size_limits[$file_type];
    if ($file['size'] > $max_allowed) {
        return false;
    }
    
    // Generate unique filename
    $unique_name = uniqid() . '_' . preg_replace('/[^a-zA-Z0-9._-]/', '', $file['name']);
    $upload_path = $upload_dir . '/' . $unique_name;
    
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    if (move_uploaded_file($file['tmp_name'], $upload_path)) {
        return [
            'path' => $upload_path,
            'name' => $file['name'],
            'type' => $file_type,
            'size' => $file['size']
        ];
    }
    
    return false;
}

// WORKING AJAX Handlers - FIXED AND COMPLETE
if (isset($_GET['ajax'])) {
    header('Content-Type: application/json');
    
    switch ($_GET['ajax']) {
        case 'get_subcategories':
            $category_id = (int)($_GET['category_id'] ?? 0);
            try {
                $stmt = $pdo->prepare("
                    SELECT subcategory_key, subcategory_name 
                    FROM subcategories 
                    WHERE category_id = ? AND status = 'active' 
                    ORDER BY subcategory_order, subcategory_name
                ");
                $stmt->execute([$category_id]);
                $subcategories = $stmt->fetchAll(PDO::FETCH_ASSOC);
                echo json_encode(['success' => true, 'data' => $subcategories]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'error' => $e->getMessage()]);
            }
            exit;
            
        case 'get_makes':
            $category_id = (int)($_GET['category_id'] ?? 0);
            try {
                $stmt = $pdo->prepare("
                    SELECT make_id, make_name 
                    FROM category_makes 
                    WHERE category_id = ? AND status = 'active' 
                    ORDER BY make_name
                ");
                $stmt->execute([$category_id]);
                $makes = $stmt->fetchAll(PDO::FETCH_ASSOC);
                echo json_encode(['success' => true, 'data' => $makes]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'error' => $e->getMessage()]);
            }
            exit;
            
        case 'get_models':
            $make_id = (int)($_GET['make_id'] ?? 0);
            try {
                $stmt = $pdo->prepare("
                    SELECT model_id, model_name 
                    FROM category_models 
                    WHERE make_id = ? AND status = 'active' 
                    ORDER BY model_name
                ");
                $stmt->execute([$make_id]);
                $models = $stmt->fetchAll(PDO::FETCH_ASSOC);
                echo json_encode(['success' => true, 'data' => $models]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'error' => $e->getMessage()]);
            }
            exit;
            
        case 'get_variants':
            $model_id = (int)($_GET['model_id'] ?? 0);
            try {
                $stmt = $pdo->prepare("
                    SELECT variant_id, variant_name 
                    FROM category_variants 
                    WHERE model_id = ? AND status = 'active' 
                    ORDER BY variant_name
                ");
                $stmt->execute([$model_id]);
                $variants = $stmt->fetchAll(PDO::FETCH_ASSOC);
                echo json_encode(['success' => true, 'data' => $variants]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'error' => $e->getMessage()]);
            }
            exit;
            
        case 'get_category_info':
            $category_id = (int)($_GET['category_id'] ?? 0);
            try {
                $stmt = $pdo->prepare("
                    SELECT show_hours, mileage_label, engine_label, show_transmission, show_fuel_type, show_year 
                    FROM categories 
                    WHERE category_id = ?
                ");
                $stmt->execute([$category_id]);
                $category_info = $stmt->fetch(PDO::FETCH_ASSOC);
                echo json_encode(['success' => true, 'data' => $category_info]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'error' => $e->getMessage()]);
            }
            exit;
    }
    
    echo json_encode(['success' => false, 'error' => 'Invalid AJAX request']);
    exit;
}

// Handle POST requests
$action = $_POST['action'] ?? $_GET['action'] ?? 'home';
$message = '';
$error = '';

if ($_POST) {
    try {
        switch ($_POST['action'] ?? $action) {
            case 'forgot_password':
                $email = trim($_POST['email']);
                
                if ($email) {
                    $stmt = $pdo->prepare("SELECT user_id FROM users WHERE email = ?");
                    $stmt->execute([$email]);
                    
                    if ($stmt->fetch()) {
                        $reset_token = bin2hex(random_bytes(32));
                        $expires = date('Y-m-d H:i:s', strtotime('+1 hour'));
                        
                        $stmt = $pdo->prepare("UPDATE users SET password_reset_token = ?, password_reset_expires = ? WHERE email = ?");
                        $stmt->execute([$reset_token, $expires, $email]);
                        
                        $message = "Password reset link has been sent to your email address.";
                    } else {
                        $error = "Email address not found.";
                    }
                } else {
                    $error = "Please enter your email address.";
                }
                break;
                
            case 'reset_password':
                $token = $_POST['token'];
                $new_password = $_POST['new_password'];
                $confirm_password = $_POST['confirm_password'];
                
                if ($new_password !== $confirm_password) {
                    $error = "Passwords do not match.";
                } elseif (!validatePassword($new_password)) {
                    $error = "Password must be at least 8 characters long and contain at least one letter, one number, and one special character.";
                } else {
                    $stmt = $pdo->prepare("SELECT user_id FROM users WHERE password_reset_token = ? AND password_reset_expires > NOW()");
                    $stmt->execute([$token]);
                    
                    if ($stmt->fetch()) {
                        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                        $stmt = $pdo->prepare("UPDATE users SET password = ?, password_reset_token = NULL, password_reset_expires = NULL WHERE password_reset_token = ?");
                        $stmt->execute([$hashed_password, $token]);
                        
                        $message = "Password has been reset successfully. You can now login.";
                    } else {
                        $error = "Invalid or expired reset token.";
                    }
                }
                break;
                
            case 'login':
                $stmt = $pdo->prepare("SELECT user_id, username, email, password, user_type, status, failed_login_attempts, locked_until FROM users WHERE email = ?");
                $stmt->execute([$_POST['email']]);
                $user = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($user) {
                    // Check if account is locked
                    if ($user['locked_until'] && strtotime($user['locked_until']) > time()) {
                        $error = "Account is temporarily locked due to too many failed attempts.";
                        break;
                    }
                    
                    if (password_verify($_POST['password'], $user['password'])) {
                        if ($user['user_type'] == 'dealer' && $user['status'] != 'active') {
                            $messages = [
                                'pending' => 'Account pending admin approval',
                                'suspended' => 'Account has been suspended',
                                'rejected' => 'Account has been rejected'
                            ];
                            $error = $messages[$user['status']] ?? 'Account not active';
                        } else {
                            // Reset failed attempts on successful login
                            $pdo->prepare("UPDATE users SET failed_login_attempts = 0, locked_until = NULL, last_login = NOW() WHERE user_id = ?")->execute([$user['user_id']]);
                            
                            $_SESSION['user_id'] = $user['user_id'];
                            $_SESSION['username'] = $user['username'];
                            $_SESSION['user_type'] = $user['user_type'];
                            $_SESSION['status'] = $user['status'];
                            
                            ob_end_clean(); // Clear output buffer before redirect
                            header("Location: ?action=" . ($user['user_type'] == 'admin' ? 'admin_dashboard' : 'dealer_dashboard'));
                            exit;
                        }
                    } else {
                        // Increment failed attempts
                        $failed_attempts = $user['failed_login_attempts'] + 1;
                        $locked_until = null;
                        
                        if ($failed_attempts >= 5) {
                            $locked_until = date('Y-m-d H:i:s', strtotime('+30 minutes'));
                        }
                        
                        $pdo->prepare("UPDATE users SET failed_login_attempts = ?, locked_until = ? WHERE user_id = ?")->execute([$failed_attempts, $locked_until, $user['user_id']]);
                        
                        $error = "Invalid email or password.";
                        if ($failed_attempts >= 5) {
                            $error .= " Account locked for 30 minutes.";
                        }
                    }
                } else {
                    $error = "Invalid email or password.";
                }
                break;

            case 'register':
                $captcha_answer = (int)$_POST['captcha_answer'];
                
                if (!isset($_SESSION['captcha_answer']) || $captcha_answer !== $_SESSION['captcha_answer']) {
                    $error = "Invalid CAPTCHA answer.";
                    break;
                }
                
                if (!validatePassword($_POST['password'])) {
                    $error = "Password must be at least 8 characters long and contain at least one letter, one number, and one special character.";
                    break;
                }
                
                $stmt = $pdo->prepare("SELECT user_id FROM users WHERE email = ? OR username = ?");
                $stmt->execute([$_POST['email'], $_POST['username']]);
                if ($stmt->fetch()) {
                    $error = "Email or username already exists";
                } else {
                    $stmt = $pdo->prepare("INSERT INTO users (username, email, password, phone, company_name, user_type, status) VALUES (?, ?, ?, ?, ?, 'dealer', 'pending')");
                    $stmt->execute([
                        $_POST['username'],
                        $_POST['email'],
                        password_hash($_POST['password'], PASSWORD_DEFAULT),
                        $_POST['phone'],
                        $_POST['company_name']
                    ]);
                    $message = "Registration successful! Waiting for admin approval.";
                }
                unset($_SESSION['captcha_answer']);
                break;

            case 'add_vehicle':
                if (isset($_SESSION['user_type']) && $_SESSION['user_type'] == 'dealer' && $_SESSION['status'] == 'active') {
                    // Validate description length
                    if (strlen($_POST['description'] ?? '') > 3000) {
                        $error = "Description cannot exceed 3000 characters.";
                        break;
                    }
                    
                    // Insert vehicle with enhanced fields including featured status
                    $stmt = $pdo->prepare("INSERT INTO vehicles (dealer_id, category, subcategory, listing_type, condition_type, make, model, variant, year, price, mileage, hours_used, engine_type, horsepower, transmission, fuel_type, color, region, city, no_accidents, warranty, finance_available, trade_in, service_history, roadworthy, description, features, youtube_video_url, daily_rate, weekly_rate, monthly_rate, auction_start_date, auction_end_date, reserve_price, featured, featured_until) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                    
                    $stmt->execute([
                        $_SESSION['user_id'],
                        $_POST['category'],
                        $_POST['subcategory'] ?: null,
                        $_POST['listing_type'] ?? 'sale',
                        $_POST['condition_type'] ?? 'used',
                        $_POST['make'],
                        $_POST['model'],
                        $_POST['variant'] ?: null,
                        $_POST['year'] ?: null,
                        $_POST['price'],
                        $_POST['mileage'] ?: 0,
                        $_POST['hours_used'] ?: 0,
                        $_POST['engine_type'] ?: null,
                        $_POST['horsepower'] ?: null,
                        $_POST['transmission'] ?: null,
                        $_POST['fuel_type'] ?: null,
                        $_POST['color'] ?: null,
                        $_POST['region'],
                        $_POST['city'],
                        isset($_POST['no_accidents']) ? 1 : 0,
                        isset($_POST['warranty']) ? 1 : 0,
                        isset($_POST['finance_available']) ? 1 : 0,
                        isset($_POST['trade_in']) ? 1 : 0,
                        isset($_POST['service_history']) ? 1 : 0,
                        isset($_POST['roadworthy']) ? 1 : 0,
                        $_POST['description'] ?: null,
                        $_POST['features'] ?: null,
                        $_POST['youtube_video_url'] ?: null,
                        $_POST['daily_rate'] ?: null,
                        $_POST['weekly_rate'] ?: null,
                        $_POST['monthly_rate'] ?: null,
                        $_POST['auction_start_date'] ?: null,
                        $_POST['auction_end_date'] ?: null,
                        $_POST['reserve_price'] ?: null,
                        isset($_POST['featured']) ? 1 : 0,
                        $_POST['featured_until'] ?: null
                    ]);
                    
                    $vehicle_id = $pdo->lastInsertId();
                    
                    // Handle file uploads with enhanced limits
                    $image_count = 0;
                    $video_count = 0;
                    
                    // Handle images (max 40 images, 2MB each)
                    if (!empty($_FILES['images']['name'][0])) {
                        foreach ($_FILES['images']['name'] as $key => $name) {
                            if ($image_count >= 40) break; // Limit to 40 images
                            
                            if ($_FILES['images']['error'][$key] === UPLOAD_ERR_OK) {
                                $file = [
                                    'name' => $_FILES['images']['name'][$key],
                                    'tmp_name' => $_FILES['images']['tmp_name'][$key],
                                    'error' => $_FILES['images']['error'][$key],
                                    'size' => $_FILES['images']['size'][$key]
                                ];
                                
                                $upload_result = uploadFile($file);
                                if ($upload_result && $upload_result['type'] === 'image') {
                                    $is_primary = ($image_count === 0) ? 1 : 0;
                                    $stmt = $pdo->prepare("INSERT INTO vehicle_images (vehicle_id, image_path, image_name, file_size, image_order, is_primary) VALUES (?, ?, ?, ?, ?, ?)");
                                    $stmt->execute([$vehicle_id, $upload_result['path'], $upload_result['name'], $upload_result['size'], $image_count, $is_primary]);
                                    $image_count++;
                                }
                            }
                        }
                    }
                    
                    // Handle videos (max 5 videos, 50MB each)
                    if (!empty($_FILES['videos']['name'][0])) {
                        foreach ($_FILES['videos']['name'] as $key => $name) {
                            if ($video_count >= 5) break; // Limit to 5 videos
                            
                            if ($_FILES['videos']['error'][$key] === UPLOAD_ERR_OK) {
                                $file = [
                                    'name' => $_FILES['videos']['name'][$key],
                                    'tmp_name' => $_FILES['videos']['tmp_name'][$key],
                                    'error' => $_FILES['videos']['error'][$key],
                                    'size' => $_FILES['videos']['size'][$key]
                                ];
                                
                                $upload_result = uploadFile($file);
                                if ($upload_result && $upload_result['type'] === 'video') {
                                    $stmt = $pdo->prepare("INSERT INTO vehicle_videos (vehicle_id, video_path, video_title, file_size) VALUES (?, ?, ?, ?)");
                                    $stmt->execute([$vehicle_id, $upload_result['path'], $upload_result['name'], $upload_result['size']]);
                                    $video_count++;
                                }
                            }
                        }
                    }
                    
                    // Handle documents (PDF only)
                    if (!empty($_FILES['documents']['name'][0])) {
                        foreach ($_FILES['documents']['name'] as $key => $name) {
                            if ($_FILES['documents']['error'][$key] === UPLOAD_ERR_OK) {
                                $file = [
                                    'name' => $_FILES['documents']['name'][$key],
                                    'tmp_name' => $_FILES['documents']['tmp_name'][$key],
                                    'error' => $_FILES['documents']['error'][$key],
                                    'size' => $_FILES['documents']['size'][$key]
                                ];
                                
                                $upload_result = uploadFile($file);
                                if ($upload_result && $upload_result['type'] === 'document') {
                                    $stmt = $pdo->prepare("INSERT INTO vehicle_documents (vehicle_id, document_name, document_path, file_size) VALUES (?, ?, ?, ?)");
                                    $stmt->execute([$vehicle_id, $upload_result['name'], $upload_result['path'], $upload_result['size']]);
                                }
                            }
                        }
                    }
                    
                    ob_end_clean();
                    header("Location: ?action=dealer_dashboard&msg=added");
                    exit;
                }
                break;

            case 'toggle_featured':
                if (isset($_SESSION['user_type']) && $_SESSION['user_type'] == 'dealer' && $_SESSION['status'] == 'active') {
                    $vehicle_id = $_POST['vehicle_id'];
                    $featured = isset($_POST['featured']) ? 1 : 0;
                    $featured_until = $_POST['featured_until'] ?: null;
                    
                    // Verify ownership
                    $stmt = $pdo->prepare("SELECT vehicle_id FROM vehicles WHERE vehicle_id = ? AND dealer_id = ?");
                    $stmt->execute([$vehicle_id, $_SESSION['user_id']]);
                    
                    if ($stmt->fetch()) {
                        $stmt = $pdo->prepare("UPDATE vehicles SET featured = ?, featured_until = ? WHERE vehicle_id = ?");
                        $stmt->execute([$featured, $featured_until, $vehicle_id]);
                        
                        ob_end_clean();
                        header("Location: ?action=dealer_dashboard&msg=featured_updated");
                        exit;
                    } else {
                        $error = "Vehicle not found or access denied.";
                    }
                }
                break;

            case 'delete_vehicle':
                if (isset($_SESSION['user_type']) && $_SESSION['user_type'] == 'dealer' && $_SESSION['status'] == 'active') {
                    $vehicle_id = $_POST['vehicle_id'];
                    
                    // Verify ownership
                    $stmt = $pdo->prepare("SELECT vehicle_id FROM vehicles WHERE vehicle_id = ? AND dealer_id = ?");
                    $stmt->execute([$vehicle_id, $_SESSION['user_id']]);
                    
                    if ($stmt->fetch()) {
                        // Delete associated files first
                        $stmt = $pdo->prepare("SELECT image_path FROM vehicle_images WHERE vehicle_id = ?");
                        $stmt->execute([$vehicle_id]);
                        $images = $stmt->fetchAll(PDO::FETCH_COLUMN);
                        foreach ($images as $image_path) {
                            if (file_exists($image_path)) {
                                unlink($image_path);
                            }
                        }
                        
                        $stmt = $pdo->prepare("SELECT video_path FROM vehicle_videos WHERE vehicle_id = ?");
                        $stmt->execute([$vehicle_id]);
                        $videos = $stmt->fetchAll(PDO::FETCH_COLUMN);
                        foreach ($videos as $video_path) {
                            if (file_exists($video_path)) {
                                unlink($video_path);
                            }
                        }
                        
                        $stmt = $pdo->prepare("SELECT document_path FROM vehicle_documents WHERE vehicle_id = ?");
                        $stmt->execute([$vehicle_id]);
                        $documents = $stmt->fetchAll(PDO::FETCH_COLUMN);
                        foreach ($documents as $document_path) {
                            if (file_exists($document_path)) {
                                unlink($document_path);
                            }
                        }
                        
                        // Delete vehicle (cascade will handle related records)
                        $stmt = $pdo->prepare("DELETE FROM vehicles WHERE vehicle_id = ?");
                        $stmt->execute([$vehicle_id]);
                        
                        ob_end_clean();
                        header("Location: ?action=dealer_dashboard&msg=deleted");
                        exit;
                    } else {
                        $error = "Vehicle not found or access denied.";
                    }
                }
                break;

            case 'add_sales_team':
                if (isset($_SESSION['user_type']) && $_SESSION['user_type'] == 'dealer' && $_SESSION['status'] == 'active') {
                    $photo_path = null;
                    
                    // Handle photo upload
                    if (!empty($_FILES['photo']['name'])) {
                        $upload_result = uploadFile($_FILES['photo']);
                        if ($upload_result && $upload_result['type'] === 'image') {
                            $photo_path = $upload_result['path'];
                        }
                    }
                    
                    $stmt = $pdo->prepare("INSERT INTO dealer_sales_team (dealer_id, name, position, phone, email, whatsapp, photo, facebook_url, twitter_url, instagram_url, linkedin_url, youtube_url, tiktok_url, website_url) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                    $stmt->execute([
                        $_SESSION['user_id'],
                        $_POST['name'],
                        $_POST['position'] ?: null,
                        $_POST['phone'] ?: null,
                        $_POST['email'] ?: null,
                        $_POST['whatsapp'] ?: null,
                        $photo_path,
                        $_POST['facebook_url'] ?: null,
                        $_POST['twitter_url'] ?: null,
                        $_POST['instagram_url'] ?: null,
                        $_POST['linkedin_url'] ?: null,
                        $_POST['youtube_url'] ?: null,
                        $_POST['tiktok_url'] ?: null,
                        $_POST['website_url'] ?: null
                    ]);
                    
                    ob_end_clean();
                    header("Location: ?action=manage_sales_team&msg=added");
                    exit;
                }
                break;

            case 'add_category':
                if (isset($_SESSION['user_type']) && $_SESSION['user_type'] == 'admin') {
                    $stmt = $pdo->prepare("INSERT INTO categories (category_key, category_name, listing_type, icon, listing_label, show_hours) VALUES (?, ?, ?, ?, ?, ?)");
                    $stmt->execute([
                        $_POST['category_key'],
                        $_POST['category_name'],
                        $_POST['listing_type'],
                        $_POST['icon'],
                        $_POST['listing_label'],
                        isset($_POST['show_hours']) ? 1 : 0
                    ]);
                    ob_end_clean();
                    header("Location: ?action=manage_categories&msg=added");
                    exit;
                }
                break;

            case 'add_subcategory':
                if (isset($_SESSION['user_type']) && $_SESSION['user_type'] == 'admin') {
                    $stmt = $pdo->prepare("INSERT INTO subcategories (category_id, subcategory_key, subcategory_name) VALUES (?, ?, ?)");
                    $stmt->execute([
                        $_POST['category_id'],
                        $_POST['subcategory_key'],
                        $_POST['subcategory_name']
                    ]);
                    ob_end_clean();
                    header("Location: ?action=manage_categories&msg=subcategory_added");
                    exit;
                }
                break;

            case 'add_make':
                if (isset($_SESSION['user_type']) && $_SESSION['user_type'] == 'admin') {
                    $stmt = $pdo->prepare("INSERT INTO category_makes (category_id, make_name) VALUES (?, ?)");
                    $stmt->execute([
                        $_POST['category_id'],
                        $_POST['make_name']
                    ]);
                    ob_end_clean();
                    header("Location: ?action=manage_makes&msg=added");
                    exit;
                }
                break;

            case 'add_model':
                if (isset($_SESSION['user_type']) && $_SESSION['user_type'] == 'admin') {
                    $stmt = $pdo->prepare("INSERT INTO category_models (make_id, model_name) VALUES (?, ?)");
                    $stmt->execute([
                        $_POST['make_id'],
                        $_POST['model_name']
                    ]);
                    ob_end_clean();
                    header("Location: ?action=manage_models&make_id=" . $_POST['make_id'] . "&msg=added");
                    exit;
                }
                break;

            case 'add_variant':
                if (isset($_SESSION['user_type']) && $_SESSION['user_type'] == 'admin') {
                    $stmt = $pdo->prepare("INSERT INTO category_variants (model_id, variant_name, variant_description) VALUES (?, ?, ?)");
                    $stmt->execute([
                        $_POST['model_id'],
                        $_POST['variant_name'],
                        $_POST['variant_description'] ?: null
                    ]);
                    ob_end_clean();
                    header("Location: ?action=manage_variants&model_id=" . $_POST['model_id'] . "&msg=variant_added");
                    exit;
                }
                break;

            case 'add_year':
                if (isset($_SESSION['user_type']) && $_SESSION['user_type'] == 'admin') {
                    $stmt = $pdo->prepare("INSERT INTO system_years (year_value) VALUES (?)");
                    $stmt->execute([$_POST['year_value']]);
                    ob_end_clean();
                    header("Location: ?action=manage_years&msg=year_added");
                    exit;
                }
                break;

            case 'approve_dealer':
                if (isset($_SESSION['user_type']) && $_SESSION['user_type'] == 'admin') {
                    $stmt = $pdo->prepare("UPDATE users SET status = 'active' WHERE user_id = ? AND user_type = 'dealer'");
                    $stmt->execute([$_POST['dealer_id']]);
                    ob_end_clean();
                    header("Location: ?action=admin_dashboard&msg=approved");
                    exit;
                }
                break;

            case 'reject_dealer':
                if (isset($_SESSION['user_type']) && $_SESSION['user_type'] == 'admin') {
                    $stmt = $pdo->prepare("UPDATE users SET status = 'rejected' WHERE user_id = ? AND user_type = 'dealer'");
                    $stmt->execute([$_POST['dealer_id']]);
                    ob_end_clean();
                    header("Location: ?action=pending_dealers&msg=rejected");
                    exit;
                }
                break;

            case 'set_premium':
                if (isset($_SESSION['user_type']) && $_SESSION['user_type'] == 'admin') {
                    $premium_until = $_POST['premium_until'] ? $_POST['premium_until'] : null;
                    $stmt = $pdo->prepare("UPDATE vehicles SET premium_listing = ?, premium_until = ? WHERE vehicle_id = ?");
                    $stmt->execute([
                        isset($_POST['premium_listing']) ? 1 : 0,
                        $premium_until,
                        $_POST['vehicle_id']
                    ]);
                    ob_end_clean();
                    header("Location: ?action=premium_management&msg=premium_updated");
                    exit;
                }
                break;

            case 'set_featured':
                if (isset($_SESSION['user_type']) && $_SESSION['user_type'] == 'admin') {
                    $featured_until = $_POST['featured_until'] ? $_POST['featured_until'] : null;
                    $stmt = $pdo->prepare("UPDATE vehicles SET featured = ?, featured_until = ? WHERE vehicle_id = ?");
                    $stmt->execute([
                        isset($_POST['featured']) ? 1 : 0,
                        $featured_until,
                        $_POST['vehicle_id']
                    ]);
                    ob_end_clean();
                    header("Location: ?action=featured_management&msg=featured_updated");
                    exit;
                }
                break;
        }
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Handle logout
if ($action == 'logout') {
    session_destroy();
    ob_end_clean();
    header("Location: ?action=home");
    exit;
}

// Handle success messages
if (isset($_GET['msg'])) {
    $messages = [
        'approved' => 'Dealer approved successfully!',
        'rejected' => 'Dealer rejected successfully!',
        'added' => 'Record added successfully!',
        'updated' => 'Record updated successfully!',
        'deleted' => 'Record deleted successfully!',
        'premium_updated' => 'Premium listing status updated!',
        'featured_updated' => 'Featured listing status updated!',
        'variant_added' => 'Variant added successfully!',
        'year_added' => 'Year added successfully!',
        'subcategory_added' => 'Subcategory added successfully!'
    ];
    $message = $messages[$_GET['msg']] ?? 'Action completed successfully!';
}

// Generate CAPTCHA for forms
$captcha_question = generateCaptcha();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TrucksONSale - Complete Enhanced Professional Marketplace</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-navy: #0050B8;
            --primary-navy-light: #e3f2fd;
            --primary-navy-dark: #003d95;
            --primary-red: #DC2626;
            --primary-red-light: #FEE2E2;
            --success-green: #059669;
            --warning-orange: #F97316;
            --dark-gray: #111827;
            --medium-gray: #6B7280;
            --light-gray: #F9FAFB;
            --white: #ffffff;
            --shadow-light: 0 2px 8px rgba(0,0,0,0.08);
            --shadow-medium: 0 4px 16px rgba(0,0,0,0.12);
            --shadow-heavy: 0 8px 32px rgba(0,0,0,0.16);
            --border-radius: 12px;
            --border-radius-sm: 8px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        * {
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: var(--dark-gray);
            line-height: 1.6;
            margin: 0;
            padding: 0;
        }
        
        /* Enhanced Navbar with Navy Blue Theme */
        .navbar-custom {
            background: linear-gradient(135deg, var(--primary-navy), var(--primary-navy-dark)) !important;
            box-shadow: var(--shadow-medium);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: white !important;
        }
        
        /* Enhanced Buttons with Navy Theme */
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-navy), var(--primary-navy-dark));
            border: none;
            color: white;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            border-radius: var(--border-radius-sm);
            transition: var(--transition);
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-navy-dark), var(--primary-navy));
            transform: translateY(-1px);
            box-shadow: var(--shadow-medium);
            color: white;
        }
        
        .btn-outline-primary {
            border: 2px solid var(--primary-navy);
            color: var(--primary-navy);
            background: transparent;
            font-weight: 600;
        }
        
        .btn-outline-primary:hover {
            background: var(--primary-navy);
            color: white;
            transform: translateY(-1px);
        }
        
        /* Enhanced Cards */
        .card {
            border: none;
            box-shadow: var(--shadow-light);
            border-radius: var(--border-radius);
            overflow: hidden;
            transition: var(--transition);
            background: var(--white);
            margin-bottom: 1.5rem;
        }
        
        .card:hover {
            box-shadow: var(--shadow-medium);
            transform: translateY(-2px);
        }
        
        .card-header {
            background: linear-gradient(135deg, var(--primary-navy), var(--primary-navy-dark));
            color: white;
            border: none;
            padding: 1.25rem 1.5rem;
            font-weight: 600;
        }
        
        /* Enhanced Forms */
        .form-control, .form-select {
            border: 2px solid #e9ecef;
            border-radius: var(--border-radius-sm);
            padding: 0.75rem 1rem;
            transition: var(--transition);
            font-size: 1rem;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-navy);
            box-shadow: 0 0 0 0.2rem rgba(0, 80, 184, 0.25);
            outline: none;
        }
        
        .form-label {
            font-weight: 600;
            color: var(--dark-gray);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        /* Character Counter */
        .char-counter {
            font-size: 0.875rem;
            color: var(--medium-gray);
            text-align: right;
            margin-top: 0.25rem;
        }
        
        .char-counter.warning {
            color: var(--warning-orange);
        }
        
        .char-counter.danger {
            color: var(--primary-red);
        }
        
        /* Enhanced File Upload Areas */
        .file-upload-area {
            border: 3px dashed #cbd5e0;
            border-radius: var(--border-radius);
            padding: 2rem;
            text-align: center;
            transition: var(--transition);
            background: #f8fafc;
            margin-bottom: 1rem;
        }
        
        .file-upload-area:hover {
            border-color: var(--primary-navy);
            background: var(--primary-navy-light);
        }
        
        .file-upload-limits {
            font-size: 0.75rem;
            color: var(--medium-gray);
            margin-top: 0.5rem;
        }
        
        /* CAPTCHA Styling */
        .captcha-container {
            background: var(--light-gray);
            border: 2px solid #e5e7eb;
            border-radius: var(--border-radius-sm);
            padding: 1rem;
            text-align: center;
            margin-bottom: 1rem;
        }
        
        .captcha-question {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--primary-navy);
            margin-bottom: 0.5rem;
        }
        
        /* Listing Type Badges */
        .listing-type-badge {
            position: absolute;
            top: 1rem;
            left: 1rem;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            z-index: 2;
        }
        
        .listing-type-sale { background: var(--primary-navy); color: white; }
        .listing-type-rent_to_own { background: var(--success-green); color: white; }
        .listing-type-hire { background: var(--warning-orange); color: white; }
        .listing-type-auction { background: var(--primary-red); color: white; }
        
        /* Enhanced Tables */
        .table {
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow-light);
            background: white;
        }
        
        .table thead th {
            background: var(--primary-navy);
            color: white;
            border: none;
            font-weight: 600;
            padding: 1rem;
        }
        
        /* Premium and Featured Listing Indicators */
        .premium-badge {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #92400e;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            z-index: 2;
            box-shadow: var(--shadow-medium);
        }
        
        .featured-badge {
            position: absolute;
            top: 3.5rem;
            right: 1rem;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            z-index: 2;
            box-shadow: var(--shadow-medium);
        }
        
        /* Enhanced Stats Cards */
        .stats-card {
            background: linear-gradient(135deg, var(--primary-navy), var(--primary-navy-dark));
            color: white;
            text-align: center;
            padding: 2rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-medium);
            transition: var(--transition);
        }
        
        .stats-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-heavy);
        }
        
        .stats-card i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.8;
        }
        
        .stats-card h4 {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0.5rem 0;
        }
        
        /* Loading Spinner for AJAX */
        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid var(--primary-navy);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Loading overlay for select dropdowns */
        .select-loading {
            position: relative;
        }
        
        .select-loading::after {
            content: '';
            position: absolute;
            right: 30px;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid var(--primary-navy);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        /* Enhanced Alert Messages */
        .alert {
            border: none;
            border-radius: var(--border-radius);
            padding: 1rem 1.5rem;
            margin-bottom: 1.5rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
            border-left: 4px solid var(--success-green);
        }
        
        .alert-danger {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
            border-left: 4px solid var(--primary-red);
        }
        
        .alert-info {
            background: linear-gradient(135deg, var(--primary-navy-light), #bbdefb);
            color: #0c4a6e;
            border-left: 4px solid var(--primary-navy);
        }
        
        /* Admin Sidebar */
        .admin-sidebar {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-light);
            padding: 1.5rem;
            height: fit-content;
        }
        
        .admin-sidebar .nav-link {
            color: var(--dark-gray);
            padding: 0.75rem 1rem;
            border-radius: var(--border-radius-sm);
            margin-bottom: 0.5rem;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .admin-sidebar .nav-link:hover {
            background: var(--primary-navy-light);
            color: var(--primary-navy);
            transform: translateX(5px);
        }
        
        .admin-sidebar .nav-link.active {
            background: var(--primary-navy);
            color: white;
        }
        
        /* Vehicle Cards */
        .vehicle-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
        }
        
        .vehicle-image {
            height: 200px;
            overflow: hidden;
            position: relative;
        }
        
        .vehicle-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
        
        .vehicle-card:hover .vehicle-image img {
            transform: scale(1.05);
        }
        
        .hours-display {
            background: var(--warning-orange);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
        }
        
        /* Additional Professional Enhancements */
        .page-header {
            background: linear-gradient(135deg, var(--primary-navy), var(--primary-navy-dark));
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: var(--border-radius);
        }
        
        .feature-icon {
            width: 50px;
            height: 50px;
            background: var(--primary-navy-light);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-navy);
            font-size: 1.5rem;
            margin-right: 1rem;
        }
        
        .data-table-wrapper {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-light);
            overflow: hidden;
        }
        
        .table-filters {
            background: #f8fafc;
            padding: 1rem;
            border-bottom: 1px solid #e2e8f0;
        }
        
        /* Featured Toggle */
        .featured-toggle {
            background: #fee;
            border: 2px dashed #ff6b6b;
            border-radius: var(--border-radius-sm);
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .card-body {
                padding: 1rem;
            }
            
            .btn {
                padding: 0.5rem 1rem;
                font-size: 0.875rem;
            }
            
            .stats-card {
                padding: 1.5rem;
            }
            
            .stats-card h4 {
                font-size: 2rem;
            }
            
            .admin-sidebar {
                margin-bottom: 2rem;
            }
        }
        
        /* Animation Classes */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .slide-in-left {
            animation: slideInLeft 0.5s ease-out;
        }
        
        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-30px); }
            to { opacity: 1; transform: translateX(0); }
        }
    </style>
</head>
<body>

<?php if (isset($_SESSION['user_id'])): ?>
    <nav class="navbar navbar-expand-lg navbar-dark navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="?action=<?php echo $_SESSION['user_type']; ?>_dashboard">
                <i class="fas fa-truck"></i>
                <span>TrucksONSale <?php echo $_SESSION['user_type'] == 'admin' ? 'Admin' : 'Dealer'; ?></span>
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-2"></i>
                    Welcome, <strong><?php echo htmlspecialchars($_SESSION['username']); ?></strong>
                </span>
                <a href="?action=logout" class="btn btn-outline-light">
                    <i class="fas fa-sign-out-alt"></i>Logout
                </a>
            </div>
        </div>
    </nav>
<?php endif; ?>

<div class="container mt-4">
    <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show fade-in">
            <i class="fas fa-check-circle"></i>
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show fade-in">
            <i class="fas fa-exclamation-circle"></i>
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php
    switch ($action) {
        case 'home':
        case 'login':
            ?>
            <div class="login-container" style="min-height: 100vh; display: flex; align-items: center; justify-content: center;">
                <div class="row justify-content-center w-100">
                    <div class="col-md-6 col-lg-4">
                        <div class="text-center mb-4 fade-in">
                            <div style="font-size: 2.5rem; font-weight: 700; color: var(--primary-navy); margin-bottom: 0.5rem;">
                                <i class="fas fa-truck"></i> TrucksONSale
                            </div>
                            <p class="text-muted">Professional Complete Trading Platform</p>
                        </div>
                        <div class="card slide-in-left">
                            <div class="card-header text-center">
                                <h4><i class="fas fa-sign-in-alt me-2"></i>Dealer Login</h4>
                            </div>
                            <div class="card-body p-4">
                                <form method="POST" action="?action=login">
                                    <div class="mb-3">
                                        <label class="form-label"><i class="fas fa-envelope"></i>Email</label>
                                        <input type="email" class="form-control" name="email" required placeholder="Enter your email">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label"><i class="fas fa-lock"></i>Password</label>
                                        <input type="password" class="form-control" name="password" required placeholder="Enter your password">
                                    </div>
                                    <button type="submit" class="btn btn-primary w-100 mb-3">
                                        <i class="fas fa-sign-in-alt"></i>Login
                                    </button>
                                    <div class="text-center mb-3">
                                        <a href="?action=forgot_password" class="text-decoration-none">
                                            <i class="fas fa-key me-1"></i>Forgot Password?
                                        </a>
                                    </div>
                                    <div class="text-center">
                                        <a href="?action=register" class="btn btn-outline-primary">
                                            <i class="fas fa-user-plus"></i>Register as Dealer
                                        </a>
                                    </div>
                                </form>
                            </div>
                        </div>
                        
                 
                                    
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php
            break;

        case 'forgot_password':
            ?>
            <div class="login-container" style="min-height: 100vh; display: flex; align-items: center; justify-content: center;">
                <div class="row justify-content-center w-100">
                    <div class="col-md-6 col-lg-4">
                        <div class="card fade-in">
                            <div class="card-header text-center">
                                <h4><i class="fas fa-key me-2"></i>Reset Password</h4>
                            </div>
                            <div class="card-body p-4">
                                <form method="POST" action="?action=forgot_password">
                                    <div class="mb-3">
                                        <label class="form-label"><i class="fas fa-envelope"></i>Email Address</label>
                                        <input type="email" class="form-control" name="email" required placeholder="Enter your email address">
                                        <small class="text-muted">We'll send you a reset link if this email is registered.</small>
                                    </div>
                                    <button type="submit" class="btn btn-primary w-100 mb-3">
                                        <i class="fas fa-paper-plane"></i>Send Reset Link
                                    </button>
                                    <div class="text-center">
                                        <a href="?action=login" class="btn btn-outline-primary">
                                            <i class="fas fa-arrow-left"></i>Back to Login
                                        </a>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php
            break;

        case 'register':
            ?>
            <div class="login-container" style="min-height: 100vh; display: flex; align-items: center; justify-content: center; padding: 2rem 0;">
                <div class="row justify-content-center w-100">
                    <div class="col-md-8 col-lg-6">
                        <div class="card fade-in">
                            <div class="card-header text-center">
                                <h4><i class="fas fa-user-plus me-2"></i>Dealer Registration</h4>
                            </div>
                            <div class="card-body p-4">
                                <form method="POST" action="?action=register">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label"><i class="fas fa-user"></i>Username</label>
                                                <input type="text" class="form-control" name="username" required placeholder="Choose a username">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label"><i class="fas fa-envelope"></i>Email</label>
                                                <input type="email" class="form-control" name="email" required placeholder="Enter your email">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label"><i class="fas fa-lock"></i>Password</label>
                                                <input type="password" class="form-control" name="password" required placeholder="Enter password">
                                                <small class="text-muted">Must be 8+ characters with letter, number & special character</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label"><i class="fas fa-phone"></i>Phone</label>
                                                <input type="tel" class="form-control" name="phone" required placeholder="Enter phone number">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label"><i class="fas fa-building"></i>Company Name</label>
                                        <input type="text" class="form-control" name="company_name" required placeholder="Enter company name">
                                    </div>
                                    
                                    <!-- CAPTCHA -->
                                    <div class="mb-3">
                                        <div class="captcha-container">
                                            <div class="captcha-question">What is <?= $captcha_question ?>?</div>
                                            <div style="font-size: 0.9rem; color: var(--medium-gray);">Please solve to continue</div>
                                        </div>
                                        <input type="number" class="form-control" name="captcha_answer" required placeholder="Enter answer">
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary w-100 mb-3">
                                        <i class="fas fa-user-plus"></i>Register Account
                                    </button>
                                    <div class="text-center">
                                        <a href="?action=login" class="btn btn-outline-primary">
                                            <i class="fas fa-arrow-left"></i>Back to Login
                                        </a>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php
            break;

        case 'dealer_dashboard':
            if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] != 'dealer') {
                ob_end_clean();
                header("Location: ?action=login");
                exit;
            }

            if (($_SESSION['status'] ?? 'active') != 'active') {
                ?>
                <div class="text-center mt-5 fade-in">
                    <div class="card">
                        <div class="card-body py-5">
                            <i class="fas fa-clock fa-4x text-warning mb-3"></i>
                            <h3>Account Status: <?php echo ucfirst($_SESSION['status'] ?? 'pending'); ?></h3>
                            <p class="text-muted">Your account is pending admin approval. Please wait for activation.</p>
                        </div>
                    </div>
                </div>
                <?php
                break;
            }

            // Get dealer's enhanced vehicles with featured status
            $stmt = $pdo->prepare("
                SELECT v.*, c.category_name, c.icon as category_icon, s.subcategory_name
                FROM vehicles v 
                LEFT JOIN categories c ON v.category = c.category_key
                LEFT JOIN subcategories s ON (s.category_id = c.category_id AND v.subcategory = s.subcategory_key)
                WHERE v.dealer_id = ? 
                ORDER BY v.featured DESC, v.premium_listing DESC, v.created_at DESC
            ");
            $stmt->execute([$_SESSION['user_id']]);
            $dealer_vehicles = $stmt->fetchAll();

            // Get stats by listing type
            $stmt = $pdo->prepare("SELECT listing_type, COUNT(*) as count FROM vehicles WHERE dealer_id = ? GROUP BY listing_type");
            $stmt->execute([$_SESSION['user_id']]);
            $listing_type_stats = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

            // Get featured and premium counts
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM vehicles WHERE dealer_id = ? AND featured = 1 AND (featured_until IS NULL OR featured_until >= CURDATE())");
            $stmt->execute([$_SESSION['user_id']]);
            $featured_count = $stmt->fetchColumn();

            $stmt = $pdo->prepare("SELECT COUNT(*) FROM vehicles WHERE dealer_id = ? AND premium_listing = 1 AND (premium_until IS NULL OR premium_until >= CURDATE())");
            $stmt->execute([$_SESSION['user_id']]);
            $premium_count = $stmt->fetchColumn();
            ?>
            
            <div class="page-header fade-in">
                <div class="container">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1><i class="fas fa-tachometer-alt me-2"></i> Dealer Dashboard</h1>
                            <p class="mb-0">Manage your vehicle listings with our advanced platform</p>
                        </div>
                        <div class="btn-group" role="group">
                            <a href="?action=add_vehicle" class="btn btn-light btn-lg">
                                <i class="fas fa-plus"></i>Add New Listing
                            </a>
                            <a href="?action=manage_sales_team" class="btn btn-outline-light">
                                <i class="fas fa-users"></i>Sales Team
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Stats Cards -->
            <div class="row mb-4 slide-in-left">
                <div class="col-md-3">
                    <div class="stats-card">
                        <i class="fas fa-list"></i>
                        <h4><?php echo count($dealer_vehicles); ?></h4>
                        <p>Total Listings</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card bg-success">
                        <i class="fas fa-shopping-cart"></i>
                        <h4><?php echo $listing_type_stats['sale'] ?? 0; ?></h4>
                        <p>For Sale</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card bg-warning">
                        <i class="fas fa-calendar-alt"></i>
                        <h4><?php echo ($listing_type_stats['hire'] ?? 0) + ($listing_type_stats['rent-to-own'] ?? 0); ?></h4>
                        <p>Hire/Rent</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card bg-danger">
                        <i class="fas fa-gavel"></i>
                        <h4><?php echo $listing_type_stats['auction'] ?? 0; ?></h4>
                        <p>Auctions</p>
                    </div>
                </div>
            </div>

            <!-- Featured and Premium Stats -->
            <div class="row mb-4 fade-in">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-body text-center">
                            <h3 class="text-danger"><?= $featured_count ?></h3>
                            <p class="text-muted">Featured Listings</p>
                            <small class="text-muted">Enhanced visibility and priority placement</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-body text-center">
                            <h3 class="text-warning"><?= $premium_count ?></h3>
                            <p class="text-muted">Premium Listings</p>
                            <small class="text-muted">Premium features and benefits</small>
                        </div>
                    </div>
                </div>
            </div>

            <?php if (empty($dealer_vehicles)): ?>
                <div class="text-center fade-in">
                    <div class="card">
                        <div class="card-body py-5">
                            <i class="fas fa-list fa-4x text-muted mb-3"></i>
                            <h4>No Listings Yet</h4>
                            <p class="text-muted">Start by adding your first listing .</p>
                            <a href="?action=add_vehicle" class="btn btn-primary btn-lg">
                                <i class="fas fa-plus"></i>Add Your First Listing
                            </a>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <div class="row">
                    <?php foreach ($dealer_vehicles as $index => $v): ?>
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card vehicle-card fade-in" style="animation-delay: <?= $index * 0.1 ?>s">
                            <div class="vehicle-image">
                                <?php
                                // Get primary image
                                $stmt = $pdo->prepare("SELECT image_path FROM vehicle_images WHERE vehicle_id = ? AND is_primary = 1 LIMIT 1");
                                $stmt->execute([$v['vehicle_id']]);
                                $primary_image = $stmt->fetchColumn();
                                ?>
                                <img src="<?php echo $primary_image ?: 'https://via.placeholder.com/300x200?text=No+Image'; ?>" alt="Vehicle Image" style="width: 100%; height: 200px; object-fit: cover;">
                                
                                <!-- Listing type badge -->
                                <div class="listing-type-badge listing-type-<?= str_replace('-', '_', $v['listing_type']) ?>">
                                    <?= strtoupper(str_replace('-', ' ', $v['listing_type'])) ?>
                                </div>
                                
                                <!-- Featured badge if applicable -->
                                <?php if ($v['featured'] && (!$v['featured_until'] || strtotime($v['featured_until']) > time())): ?>
                                    <div class="featured-badge">
                                        <i class="fas fa-fire"></i> FEATURED
                                    </div>
                                <?php endif; ?>
                                
                                <!-- Premium badge if applicable -->
                                <?php if ($v['premium_listing'] && (!$v['premium_until'] || strtotime($v['premium_until']) > time())): ?>
                                    <div class="premium-badge" style="top: <?= $v['featured'] ? '6rem' : '1rem' ?>;">
                                        <i class="fas fa-star"></i> PREMIUM
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="card-body">
                                <h5 class="card-title">
                                    <?php echo htmlspecialchars($v['make'] . ' ' . $v['model']); ?>
                                    <?php if ($v['variant']): ?>
                                        <small class="text-muted"><?= htmlspecialchars($v['variant']) ?></small>
                                    <?php endif; ?>
                                </h5>
                                <p class="card-text">
                                    <?php if ($v['year']): ?>
                                        <strong><i class="fas fa-calendar me-1"></i>Year:</strong> <?php echo $v['year']; ?><br>
                                    <?php endif; ?>
                                    <strong><i class="fas fa-dollar-sign me-1"></i>Price:</strong> R<?php echo number_format($v['price'], 2); ?><br>
                                    
                                    <!-- Enhanced display with hours for machinery -->
                                    <?php if ($v['hours_used'] > 0): ?>
                                        <span class="hours-display">
                                            <i class="fas fa-clock"></i><?= number_format($v['hours_used']) ?> hrs
                                        </span><br>
                                    <?php endif; ?>
                                    
                                    <strong><i class="fas fa-map-marker-alt me-1"></i>Location:</strong> <?php echo htmlspecialchars($v['city'] . ', ' . $v['region']); ?>
                                </p>
                                
                                <!-- Featured Toggle for Dealers -->
                                <div class="featured-toggle mb-3">
                                    <form method="POST" action="?action=dealer_dashboard" class="d-inline">
                                        <input type="hidden" name="action" value="toggle_featured">
                                        <input type="hidden" name="vehicle_id" value="<?= $v['vehicle_id'] ?>">
                                        <div class="row align-items-center">
                                            <div class="col-8">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="featured" <?= $v['featured'] ? 'checked' : '' ?>>
                                                    <label class="form-check-label">
                                                        <strong>Featured Listing</strong>
                                                    </label>
                                                </div>
                                                <input type="date" class="form-control form-control-sm mt-1" name="featured_until" 
                                                       value="<?= $v['featured_until'] ?>" 
                                                       min="<?= date('Y-m-d') ?>" 
                                                       placeholder="Featured until...">
                                            </div>
                                            <div class="col-4">
                                                <button type="submit" class="btn btn-sm btn-outline-danger w-100">
                                                    <i class="fas fa-fire"></i> Update
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                                
                                <div class="btn-group w-100" role="group">
                                    <a href="?action=view_listing&id=<?php echo $v['vehicle_id']; ?>" class="btn btn-info btn-sm">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="?action=edit_vehicle&id=<?php echo $v['vehicle_id']; ?>" class="btn btn-primary btn-sm">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form method="POST" style="flex: 1;" action="?action=dealer_dashboard">
                                        <input type="hidden" name="vehicle_id" value="<?php echo $v['vehicle_id']; ?>">
                                        <input type="hidden" name="action" value="delete_vehicle">
                                        <button type="submit" class="btn btn-danger btn-sm w-100" onclick="return confirm('Delete this listing?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
            <?php
            break;

        case 'add_vehicle':
            if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] != 'dealer' || ($_SESSION['status'] ?? 'active') != 'active') {
                ob_end_clean();
                header("Location: ?action=login");
                exit;
            }

            // Get system years for dropdown
            $stmt = $pdo->query("SELECT year_value FROM system_years WHERE status = 'active' ORDER BY year_value DESC");
            $system_years = $stmt->fetchAll(PDO::FETCH_COLUMN);

            // Get dealer branches
            $stmt = $pdo->prepare("SELECT * FROM dealer_branches WHERE dealer_id = ? AND status = 'active'");
            $stmt->execute([$_SESSION['user_id']]);
            $dealer_branches = $stmt->fetchAll();

            // Get categories for the dropdown - only sale categories
            $stmt = $pdo->query("SELECT category_id, category_key, category_name FROM categories WHERE status = 'active' AND listing_type = 'sale' ORDER BY category_order, category_name");
            $categories = $stmt->fetchAll();
            ?>
           
            <div class="row justify-content-center">
                <div class="col-md-12">
                    <div class="card slide-in-left">
                        <div class="card-header">
                            <h4><i class="fas fa-plus me-2"></i>Enhanced Vehicle Listing Form</h4>
                            <p class="mb-0 text-light">Dynamic cascading dropdowns with real-time data loading</p>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="?action=add_vehicle" enctype="multipart/form-data">
                                
                                <!-- Listing Type Selection -->
                                <div class="row mb-4">
                                    <div class="col-md-12">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6><i class="fas fa-tags me-2"></i>Listing Type</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-3">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="radio" name="listing_type" value="sale" id="listing_sale" checked>
                                                            <label class="form-check-label" for="listing_sale">
                                                                <strong>For Sale</strong><br>
                                                                <small class="text-muted">Standard sale listing</small>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="radio" name="listing_type" value="rent-to-own" id="listing_rent">
                                                            <label class="form-check-label" for="listing_rent">
                                                                <strong>Rent-to-Own</strong><br>
                                                                <small class="text-muted">Rental with ownership option</small>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="radio" name="listing_type" value="hire" id="listing_hire">
                                                            <label class="form-check-label" for="listing_hire">
                                                                <strong>Hire/Rental</strong><br>
                                                                <small class="text-muted">Short-term rental</small>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="radio" name="listing_type" value="auction" id="listing_auction">
                                                            <label class="form-check-label" for="listing_auction">
                                                                <strong>Auction</strong><br>
                                                                <small class="text-muted">Bid-based sale</small>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Category and Vehicle Details -->
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-tags"></i>Category</label>
                                            <select class="form-control" id="category" name="category" required>
                                                <option value="">Select Category</option>
                                                <?php foreach ($categories as $cat): ?>
                                                    <option value="<?= $cat['category_key'] ?>" data-id="<?= $cat['category_id'] ?>">
                                                        <?= htmlspecialchars($cat['category_name']) ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                            <div class="loading-spinner" id="category-spinner"></div>
                                        </div>
                                    </div>
                               
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-industry"></i>Make</label>
                                            <select class="form-control" id="make" name="make" required disabled>
                                                <option value="">Select Make</option>
                                            </select>
                                            <div class="loading-spinner" id="make-spinner"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-car"></i>Model</label>
                                            <select class="form-control" id="model" name="model" required disabled>
                                                <option value="">Select Model</option>
                                            </select>
                                            <div class="loading-spinner" id="model-spinner"></div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Variant and Year Row -->
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-cogs"></i>Variant</label>
                                            <select class="form-control" id="variant" name="variant" disabled>
                                                <option value="">Select Variant</option>
                                            </select>
                                            <div class="loading-spinner" id="variant-spinner"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-calendar"></i>Year</label>
                                            <select class="form-control" name="year">
                                                <option value="">Select Year</option>
                                                <?php foreach ($system_years as $year): ?>
                                                    <option value="<?= $year ?>"><?= $year ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-star"></i>Condition</label>
                                            <select class="form-control" name="condition_type">
                                                <option value="used">Used</option>
                                                <option value="new">New</option>
                                                <option value="refurbished">Refurbished</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- Price and Location -->
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-dollar-sign"></i>Price (R)</label>
                                            <input type="number" class="form-control" name="price" step="0.01" required placeholder="0.00">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-map"></i>Province</label>
                                            <select class="form-control" name="region" required>
                                                <option value="">Select Province</option>
                                                <option value="Eastern Cape">Eastern Cape</option>
                                                <option value="Free State">Free State</option>
                                                <option value="Gauteng">Gauteng</option>
                                                <option value="KwaZulu-Natal">KwaZulu-Natal</option>
                                                <option value="Limpopo">Limpopo</option>
                                                <option value="Mpumalanga">Mpumalanga</option>
                                                <option value="Northern Cape">Northern Cape</option>
                                                <option value="North West">North West</option>
                                                <option value="Western Cape">Western Cape</option>
                                                <option value="Other">Other</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-map-marker-alt"></i>City</label>
                                            <input type="text" class="form-control" name="city" required placeholder="Enter city">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-building"></i>Branch</label>
                                            <select class="form-control" name="branch_id">
                                                <option value="">Select Branch (Optional)</option>
                                                <?php foreach ($dealer_branches as $branch): ?>
                                                    <option value="<?= $branch['branch_id'] ?>"><?= htmlspecialchars($branch['branch_name']) ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- Mileage and Hours Row -->
                                <div class="row">
                                    <div class="col-md-4" id="mileage-field">
                                        <div class="mb-3">
                                            <label class="form-label" id="mileage-label"><i class="fas fa-tachometer-alt"></i>Mileage (KM)</label>
                                            <input type="number" class="form-control" name="mileage" placeholder="0">
                                        </div>
                                    </div>
                                    <div class="col-md-4" id="hours-field" style="display: none;">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-clock"></i>Hours Used</label>
                                            <input type="number" class="form-control" name="hours_used" placeholder="0">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-cog"></i>Engine Type</label>
                                            <input type="text" class="form-control" name="engine_type" placeholder="e.g., V8 Turbo">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-horse"></i>Horsepower</label>
                                            <input type="number" class="form-control" name="horsepower" placeholder="0">
                                        </div>
                                    </div>
                                </div>

                                <!-- Transmission, Fuel, Color -->
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-exchange-alt"></i>Transmission</label>
                                            <select class="form-control" name="transmission">
                                                <option value="">Select Transmission</option>
                                                <option value="manual">Manual</option>
                                                <option value="automatic">Automatic</option>
                                                <option value="semi-automatic">Semi-Automatic</option>
                                                <option value="other">Other</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-gas-pump"></i>Fuel Type</label>
                                            <select class="form-control" name="fuel_type">
                                                <option value="">Select Fuel Type</option>
                                                <option value="diesel">Diesel</option>
                                                <option value="petrol">Petrol</option>
                                                <option value="electric">Electric</option>
                                                <option value="hybrid">Hybrid</option>
                                                <option value="lpg">LPG</option>
                                                <option value="cng">CNG</option>
                                                <option value="other">Other</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-palette"></i>Color</label>
                                            <input type="text" class="form-control" name="color" placeholder="e.g., White">
                                        </div>
                                    </div>
                                </div>

                                <!-- Vehicle Identification -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-barcode"></i>VIN Number</label>
                                            <input type="text" class="form-control" name="vin_number" placeholder="Optional">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label"><i class="fas fa-id-card"></i>Registration Number</label>
                                            <input type="text" class="form-control" name="registration_number" placeholder="Optional">
                                        </div>
                                    </div>
                                </div>

                                <!-- Featured Listing Option -->
                                <div class="featured-toggle mb-4">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="featured" id="featured_checkbox">
                                                <label class="form-check-label" for="featured_checkbox">
                                                    <strong><i class="fas fa-fire me-2"></i>Make this a Featured Listing</strong><br>
                                                    <small class="text-muted">Enhanced visibility and priority placement</small>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">Featured Until (Optional)</label>
                                            <input type="date" class="form-control" name="featured_until" 
                                                   min="<?= date('Y-m-d') ?>" 
                                                   value="<?= date('Y-m-d', strtotime('+30 days')) ?>">
                                            <small class="text-muted">Leave empty for unlimited featured status</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Hire/Auction Specific Fields -->
                                <div id="hire-fields" style="display: none;">
                                    <div class="card mb-4">
                                        <div class="card-header">
                                            <h6><i class="fas fa-calendar-alt me-2"></i>Hire/Rental Rates</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label class="form-label">Daily Rate (R)</label>
                                                        <input type="number" class="form-control" name="daily_rate" step="0.01" placeholder="0.00">
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label class="form-label">Weekly Rate (R)</label>
                                                        <input type="number" class="form-control" name="weekly_rate" step="0.01" placeholder="0.00">
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label class="form-label">Monthly Rate (R)</label>
                                                        <input type="number" class="form-control" name="monthly_rate" step="0.01" placeholder="0.00">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div id="auction-fields" style="display: none;">
                                    <div class="card mb-4">
                                        <div class="card-header">
                                            <h6><i class="fas fa-gavel me-2"></i>Auction Details</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label class="form-label">Auction Start Date</label>
                                                        <input type="datetime-local" class="form-control" name="auction_start_date">
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label class="form-label">Auction End Date</label>
                                                        <input type="datetime-local" class="form-control" name="auction_end_date">
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label class="form-label">Reserve Price (R)</label>
                                                        <input type="number" class="form-control" name="reserve_price" step="0.01" placeholder="0.00">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Description Section -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6><i class="fas fa-align-left me-2"></i>Description & Features</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">Detailed Description</label>
                                                    <textarea class="form-control" id="description" name="description" rows="6" maxlength="3000" oninput="updateCharCounter(this)" placeholder="Describe your vehicle in detail..."></textarea>
                                                    <div id="char-counter" class="char-counter">3000 characters remaining</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">Key Features</label>
                                                    <textarea class="form-control" name="features" rows="6" placeholder="List key features, one per line..."></textarea>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="mb-3">
                                                    <label class="form-label"><i class="fab fa-youtube"></i>YouTube Video URL</label>
                                                    <input type="url" class="form-control" name="youtube_video_url" placeholder="https://www.youtube.com/watch?v=...">
                                                    <small class="text-muted">Add a YouTube link to showcase your vehicle</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Enhanced File Upload Section -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6><i class="fas fa-camera me-2"></i>Photos, Videos & Documents</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="file-upload-area">
                                                    <i class="fas fa-images fa-2x mb-2 text-muted"></i>
                                                    <p><strong>Upload Photos</strong></p>
                                                    <input type="file" class="form-control" id="images" name="images[]" multiple accept="image/*,.webp">
                                                    <div class="file-upload-limits">
                                                        Max: 40 photos, 2MB each<br>
                                                        Formats: JPG, PNG, GIF, WebP
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="file-upload-area">
                                                    <i class="fas fa-video fa-2x mb-2 text-muted"></i>
                                                    <p><strong>Upload Videos</strong></p>
                                                    <input type="file" class="form-control" id="videos" name="videos[]" multiple accept="video/*">
                                                    <div class="file-upload-limits">
                                                        Max: 5 videos, 50MB each<br>
                                                        Formats: MP4, AVI, MOV, WMV, FLV
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="file-upload-area">
                                                    <i class="fas fa-file-pdf fa-2x mb-2 text-muted"></i>
                                                    <p><strong>Upload Documents</strong></p>
                                                    <input type="file" class="form-control" id="documents" name="documents[]" multiple accept=".pdf">
                                                    <div class="file-upload-limits">
                                                        Max: 10MB each<br>
                                                        Format: PDF only
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Additional Features -->
                                <div class="card">
                                    <div class="card-header">
                                        <h6><i class="fas fa-star me-2"></i>Additional Features</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="no_accidents">
                                                    <label class="form-check-label">No Accidents</label>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="warranty">
                                                    <label class="form-check-label">Warranty</label>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="finance_available">
                                                    <label class="form-check-label">Finance Available</label>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="trade_in">
                                                    <label class="form-check-label">Trade In</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-md-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="service_history">
                                                    <label class="form-check-label">Service History</label>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="roadworthy">
                                                    <label class="form-check-label">Roadworthy</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-4">
                                    <button type="submit" class="btn btn-primary me-2">
                                        <i class="fas fa-plus"></i>Add Listing
                                    </button>
                                    <a href="?action=dealer_dashboard" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left"></i>Cancel
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <?php
            break;

        
                                   
        case 'admin_dashboard':
            if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] != 'admin') {
                ob_end_clean();
                header("Location: ?action=login");
                exit;
            }

            // Get comprehensive statistics
            $stats = [];
            
            // Pending dealers
            $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE user_type = 'dealer' AND status = 'pending'");
            $stats['pending_dealers'] = $stmt->fetchColumn();
            
            // Active dealers
            $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE user_type = 'dealer' AND status = 'active'");
            $stats['active_dealers'] = $stmt->fetchColumn();
            
            // Get stats by listing type
            try {
                $stmt = $pdo->query("SELECT listing_type, COUNT(*) as count FROM vehicles WHERE listing_type IS NOT NULL GROUP BY listing_type");
                $listing_type_stats = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
            } catch (PDOException $e) {
                error_log("Error getting listing stats: " . $e->getMessage());
                $listing_type_stats = [];
            }

            // Set default counts for all listing types
            $listing_stats = [
                'sale' => $listing_type_stats['sale'] ?? 0,
                'rent-to-own' => $listing_type_stats['rent-to-own'] ?? 0,
                'hire' => $listing_type_stats['hire'] ?? 0,
                'auction' => $listing_type_stats['auction'] ?? 0
            ];
            
            // Premium listings
            $stmt = $pdo->query("SELECT COUNT(*) FROM vehicles WHERE premium_listing = 1 AND (premium_until IS NULL OR premium_until >= CURDATE())");
            $stats['premium_listings'] = $stmt->fetchColumn();

            // Featured listings
            $stmt = $pdo->query("SELECT COUNT(*) FROM vehicles WHERE featured = 1 AND (featured_until IS NULL OR featured_until >= CURDATE())");
            $stats['featured_listings'] = $stmt->fetchColumn();
            
            // Get recent activity
            $stmt = $pdo->query("SELECT v.*, u.company_name FROM vehicles v JOIN users u ON v.dealer_id = u.user_id ORDER BY v.created_at DESC LIMIT 5");
            $recent_listings = $stmt->fetchAll();
            
            $stmt = $pdo->query("SELECT * FROM users WHERE user_type = 'dealer' AND status = 'pending' ORDER BY registered_at DESC LIMIT 5");
            $recent_pending = $stmt->fetchAll();
            ?>
            
            <div class="page-header fade-in">
                <div class="container">
                    <h1><i class="fas fa-tachometer-alt me-2"></i> Admin Dashboard</h1>
                    <p class="mb-0">Complete system management and monitoring</p>
                </div>
            </div>

            <div class="row">
                <div class="col-md-3">
                    <div class="admin-sidebar slide-in-left">
                        <h6 class="text-muted text-uppercase">ADMIN NAVIGATION</h6>
                        <nav class="nav flex-column mt-3">
                            <a class="nav-link active" href="?action=admin_dashboard">
                                <i class="fas fa-tachometer-alt"></i>Dashboard
                            </a>
                            <a class="nav-link" href="?action=manage_categories">
                                <i class="fas fa-tags"></i>Categories
                            </a>
                            <a class="nav-link" href="?action=manage_makes">
                                <i class="fas fa-industry"></i>Makes & Models
                            </a>
                            <a class="nav-link" href="?action=manage_years">
                                <i class="fas fa-calendar"></i>System Years
                            </a>
                            <a class="nav-link" href="?action=pending_dealers">
                                <i class="fas fa-clock"></i>Pending Dealers
                                <?php if ($stats['pending_dealers'] > 0): ?>
                                    <span class="badge bg-danger"><?= $stats['pending_dealers'] ?></span>
                                <?php endif; ?>
                            </a>
                            <a class="nav-link" href="?action=active_dealers">
                                <i class="fas fa-users"></i>Active Dealers
                            </a>
                            <a class="nav-link" href="?action=all_listings">
                                <i class="fas fa-list"></i>All Listings
                            </a>
                            <a class="nav-link" href="?action=featured_management">
                                <i class="fas fa-fire"></i>Featured Management
                            </a>
                            <a class="nav-link" href="?action=premium_management">
                                <i class="fas fa-star"></i>Premium Management
                            </a>
                        </nav>
                    </div>
                </div>
                
                <div class="col-md-9">
                    <!-- Stats Overview -->
                    <div class="row mb-4 fade-in">
                        <div class="col-md-3">
                            <div class="stats-card bg-warning">
                                <i class="fas fa-clock"></i>
                                <h4><?= $stats['pending_dealers'] ?></h4>
                                <p>Pending Dealers</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card bg-success">
                                <i class="fas fa-users"></i>
                                <h4><?= $stats['active_dealers'] ?></h4>
                                <p>Active Dealers</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card bg-danger">
                                <i class="fas fa-fire"></i>
                                <h4><?= $stats['featured_listings'] ?></h4>
                                <p>Featured Listings</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card bg-warning">
                                <i class="fas fa-star"></i>
                                <h4><?= $stats['premium_listings'] ?></h4>
                                <p>Premium Listings</p>
                            </div>
                        </div>
                    </div>

                    <!-- Listing Types Stats -->
                    <div class="card mb-4 slide-in-left">
                        <div class="card-header">
                            <h5><i class="fas fa-chart-pie me-2"></i>Listings by Type</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h3 class="text-primary"><?= $listing_stats['sale'] ?? 0 ?></h3>
                                        <p class="text-muted">For Sale</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h3 class="text-success"><?= $listing_stats['rent-to-own'] ?? 0 ?></h3>
                                        <p class="text-muted">Rent-to-Own</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h3 class="text-warning"><?= $listing_stats['hire'] ?? 0 ?></h3>
                                        <p class="text-muted">Hire/Rental</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h3 class="text-danger"><?= $listing_stats['auction'] ?? 0 ?></h3>
                                        <p class="text-muted">Auctions</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="row fade-in">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6><i class="fas fa-plus me-2"></i>Recent Listings</h6>
                                </div>
                                <div class="card-body">
                                    <?php if (empty($recent_listings)): ?>
                                        <p class="text-muted text-center">No listings yet</p>
                                    <?php else: ?>
                                        <?php foreach ($recent_listings as $listing): ?>
                                        <div class="d-flex justify-content-between align-items-center mb-2 p-2 border-bottom">
                                            <div>
                                                <strong><?= htmlspecialchars($listing['make'] . ' ' . $listing['model']) ?></strong>
                                                <small class="d-block text-muted"><?= htmlspecialchars($listing['company_name']) ?></small>
                                                <div>
                                                    <span class="badge listing-type-<?= str_replace('-', '_', $listing['listing_type']) ?>"><?= strtoupper($listing['listing_type']) ?></span>
                                                    <?php if ($listing['featured']): ?>
                                                        <span class="badge bg-danger">FEATURED</span>
                                                    <?php endif; ?>
                                                    <?php if ($listing['premium_listing']): ?>
                                                        <span class="badge bg-warning">PREMIUM</span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            <div class="text-end">
                                                <div>R<?= number_format($listing['price'], 0) ?></div>
                                                <small class="text-muted"><?= date('M j', strtotime($listing['created_at'])) ?></small>
                                            </div>
                                        </div>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6><i class="fas fa-clock me-2"></i>Pending Approvals</h6>
                                </div>
                                <div class="card-body">
                                    <?php if (empty($recent_pending)): ?>
                                        <p class="text-muted text-center">No pending dealers</p>
                                    <?php else: ?>
                                        <?php foreach ($recent_pending as $dealer): ?>
                                        <div class="d-flex justify-content-between align-items-center mb-2 p-2 border-bottom">
                                            <div>
                                                <strong><?= htmlspecialchars($dealer['company_name']) ?></strong>
                                                <small class="d-block text-muted"><?= htmlspecialchars($dealer['username']) ?></small>
                                            </div>
                                            <div>
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="action" value="approve_dealer">
                                                    <input type="hidden" name="dealer_id" value="<?= $dealer['user_id'] ?>">
                                                    <button type="submit" class="btn btn-success btn-sm">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php
            break;

        case 'featured_management':
            if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] != 'admin') {
                ob_end_clean();
                header("Location: ?action=login");
                exit;
            }

            // Get featured listings
            $stmt = $pdo->query("
                SELECT v.*, u.company_name, u.username
                FROM vehicles v 
                JOIN users u ON v.dealer_id = u.user_id 
                WHERE v.featured = 1
                ORDER BY v.featured_until DESC, v.created_at DESC
            ");
            $featured_listings = $stmt->fetchAll();

            // Get potential featured candidates
            $stmt = $pdo->query("
                SELECT v.*, u.company_name, u.username
                FROM vehicles v 
                JOIN users u ON v.dealer_id = u.user_id 
                WHERE v.featured = 0 AND v.status = 'available'
                ORDER BY v.created_at DESC
                LIMIT 20
            ");
            $featured_candidates = $stmt->fetchAll();
            ?>
            
            <div class="page-header fade-in">
                <div class="container">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1><i class="fas fa-fire me-2"></i>Featured Listings Management</h1>
                            <p class="mb-0">Manage featured listings for enhanced visibility</p>
                        </div>
                        <a href="?action=admin_dashboard" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <div class="card fade-in">
                        <div class="card-header">
                            <h5><i class="fas fa-fire me-2"></i>Current Featured Listings (<?= count($featured_listings) ?>)</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($featured_listings)): ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-fire fa-3x text-muted mb-3"></i>
                                    <h5>No Featured Listings</h5>
                                    <p class="text-muted">No featured listings are currently active.</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Vehicle</th>
                                                <th>Dealer</th>
                                                <th>Price</th>
                                                <th>Featured Until</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($featured_listings as $listing): ?>
                                            <tr>
                                                <td>
                                                    <strong><?= htmlspecialchars($listing['make'] . ' ' . $listing['model']) ?></strong>
                                                    <?php if ($listing['year']): ?>
                                                        <br><small class="text-muted"><?= $listing['year'] ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <strong><?= htmlspecialchars($listing['company_name']) ?></strong>
                                                    <br><small class="text-muted"><?= htmlspecialchars($listing['username']) ?></small>
                                                </td>
                                                <td>R<?= number_format($listing['price'], 0) ?></td>
                                                <td>
                                                    <?php if ($listing['featured_until']): ?>
                                                        <?= date('M j, Y', strtotime($listing['featured_until'])) ?>
                                                        <?php if (strtotime($listing['featured_until']) < time()): ?>
                                                            <span class="badge bg-danger">Expired</span>
                                                        <?php endif; ?>
                                                    <?php else: ?>
                                                        <span class="badge bg-success">Unlimited</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <button class="btn btn-warning btn-sm" onclick="editFeatured(<?= $listing['vehicle_id'] ?>)">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn btn-danger btn-sm" onclick="removeFeatured(<?= $listing['vehicle_id'] ?>)">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card slide-in-left">
                        <div class="card-header">
                            <h5><i class="fas fa-plus me-2"></i>Add Featured Listing</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="?action=featured_management">
                                <input type="hidden" name="action" value="set_featured">
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-car"></i>Select Vehicle</label>
                                    <select class="form-control" name="vehicle_id" required>
                                        <option value="">Choose a vehicle...</option>
                                        <?php foreach ($featured_candidates as $candidate): ?>
                                            <option value="<?= $candidate['vehicle_id'] ?>">
                                                <?= htmlspecialchars($candidate['make'] . ' ' . $candidate['model']) ?> - 
                                                <?= htmlspecialchars($candidate['company_name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-calendar"></i>Featured Until</label>
                                    <input type="date" class="form-control" name="featured_until" 
                                           min="<?= date('Y-m-d') ?>" 
                                           value="<?= date('Y-m-d', strtotime('+30 days')) ?>">
                                    <small class="text-muted">Leave empty for unlimited featured</small>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="featured" checked>
                                        <label class="form-check-label">Enable Featured Listing</label>
                                    </div>
                                </div>
                                
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-fire"></i>Make Featured
                                </button>
                            </form>
                        </div>
                    </div>
                    
                    <div class="card mt-4">
                        <div class="card-header">
                            <h6><i class="fas fa-info-circle me-2"></i>Featured Benefits</h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Priority placement</li>
                                <li><i class="fas fa-check text-success me-2"></i>Enhanced visibility</li>
                                <li><i class="fas fa-check text-success me-2"></i>Featured badge</li>
                                <li><i class="fas fa-check text-success me-2"></i>Top search results</li>
                                <li><i class="fas fa-check text-success me-2"></i>Homepage display</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <?php
            break;

        // Continue with remaining cases...
        
        case 'admin_dashboard':
            if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] != 'admin') {
                ob_end_clean();
                header("Location: ?action=login");
                exit;
            }

            // Get comprehensive statistics
            $stats = [];
            
            // Pending dealers
            $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE user_type = 'dealer' AND status = 'pending'");
            $stats['pending_dealers'] = $stmt->fetchColumn();
            
            // Active dealers
            $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE user_type = 'dealer' AND status = 'active'");
            $stats['active_dealers'] = $stmt->fetchColumn();
            
            // Get stats by listing type
            try {
                $stmt = $pdo->query("SELECT listing_type, COUNT(*) as count FROM vehicles WHERE listing_type IS NOT NULL GROUP BY listing_type");
                $listing_type_stats = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
            } catch (PDOException $e) {
                error_log("Error getting listing stats: " . $e->getMessage());
                $listing_type_stats = [];
            }

            // Set default counts for all listing types
            $listing_stats = [
                'sale' => $listing_type_stats['sale'] ?? 0,
                'rent-to-own' => $listing_type_stats['rent-to-own'] ?? 0,
                'hire' => $listing_type_stats['hire'] ?? 0,
                'auction' => $listing_type_stats['auction'] ?? 0
            ];
            
            // Premium listings
            $stmt = $pdo->query("SELECT COUNT(*) FROM vehicles WHERE premium_listing = 1 AND (premium_until IS NULL OR premium_until >= CURDATE())");
            $stats['premium_listings'] = $stmt->fetchColumn();
            
            // Get recent activity
            $stmt = $pdo->query("SELECT v.*, u.company_name FROM vehicles v JOIN users u ON v.dealer_id = u.user_id ORDER BY v.created_at DESC LIMIT 5");
            $recent_listings = $stmt->fetchAll();
            
            $stmt = $pdo->query("SELECT * FROM users WHERE user_type = 'dealer' AND status = 'pending' ORDER BY registered_at DESC LIMIT 5");
            $recent_pending = $stmt->fetchAll();
            ?>
            
            <div class="page-header fade-in">
                <div class="container">
                    <h1><i class="fas fa-tachometer-alt me-2"></i>Professional Admin Dashboard</h1>
                    <p class="mb-0">Complete system management and monitoring</p>
                </div>
            </div>

            <div class="row">
                <div class="col-md-3">
                    <div class="admin-sidebar slide-in-left">
                        <h6 class="text-muted text-uppercase">ADMIN NAVIGATION</h6>
                        <nav class="nav flex-column mt-3">
                            <a class="nav-link active" href="?action=admin_dashboard">
                                <i class="fas fa-tachometer-alt"></i>Dashboard
                            </a>
                            <a class="nav-link" href="?action=manage_categories">
                                <i class="fas fa-tags"></i>Categories
                            </a>
                            <a class="nav-link" href="?action=manage_makes">
                                <i class="fas fa-industry"></i>Makes & Models
                            </a>
                            <a class="nav-link" href="?action=manage_years">
                                <i class="fas fa-calendar"></i>System Years
                            </a>
                            <a class="nav-link" href="?action=pending_dealers">
                                <i class="fas fa-clock"></i>Pending Dealers
                                <?php if ($stats['pending_dealers'] > 0): ?>
                                    <span class="badge bg-danger"><?= $stats['pending_dealers'] ?></span>
                                <?php endif; ?>
                            </a>
                            <a class="nav-link" href="?action=active_dealers">
                                <i class="fas fa-users"></i>Active Dealers
                            </a>
                            <a class="nav-link" href="?action=all_listings">
                                <i class="fas fa-list"></i>All Listings
                            </a>
                            <a class="nav-link" href="?action=premium_management">
                                <i class="fas fa-star"></i>Premium Management
                            </a>
                        </nav>
                    </div>
                </div>
                
                <div class="col-md-9">
                    <!-- Stats Overview -->
                    <div class="row mb-4 fade-in">
                        <div class="col-md-3">
                            <div class="stats-card bg-warning">
                                <i class="fas fa-clock"></i>
                                <h4><?= $stats['pending_dealers'] ?></h4>
                                <p>Pending Dealers</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card bg-success">
                                <i class="fas fa-users"></i>
                                <h4><?= $stats['active_dealers'] ?></h4>
                                <p>Active Dealers</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <i class="fas fa-list"></i>
                                <h4><?= array_sum($listing_stats) ?></h4>
                                <p>Total Listings</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card bg-warning">
                                <i class="fas fa-star"></i>
                                <h4><?= $stats['premium_listings'] ?></h4>
                                <p>Premium Listings</p>
                            </div>
                        </div>
                    </div>

                    <!-- Listing Types Stats -->
                    <div class="card mb-4 slide-in-left">
                        <div class="card-header">
                            <h5><i class="fas fa-chart-pie me-2"></i>Listings by Type</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h3 class="text-primary"><?= $listing_stats['sale'] ?? 0 ?></h3>
                                        <p class="text-muted">For Sale</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h3 class="text-success"><?= $listing_stats['rent-to-own'] ?? 0 ?></h3>
                                        <p class="text-muted">Rent-to-Own</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h3 class="text-warning"><?= $listing_stats['hire'] ?? 0 ?></h3>
                                        <p class="text-muted">Hire/Rental</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h3 class="text-danger"><?= $listing_stats['auction'] ?? 0 ?></h3>
                                        <p class="text-muted">Auctions</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="row fade-in">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6><i class="fas fa-plus me-2"></i>Recent Listings</h6>
                                </div>
                                <div class="card-body">
                                    <?php if (empty($recent_listings)): ?>
                                        <p class="text-muted text-center">No listings yet</p>
                                    <?php else: ?>
                                        <?php foreach ($recent_listings as $listing): ?>
                                        <div class="d-flex justify-content-between align-items-center mb-2 p-2 border-bottom">
                                            <div>
                                                <strong><?= htmlspecialchars($listing['make'] . ' ' . $listing['model']) ?></strong>
                                                <small class="d-block text-muted"><?= htmlspecialchars($listing['company_name']) ?></small>
                                                <span class="badge listing-type-<?= str_replace('-', '_', $listing['listing_type']) ?>"><?= strtoupper($listing['listing_type']) ?></span>
                                            </div>
                                            <div class="text-end">
                                                <div>R<?= number_format($listing['price'], 0) ?></div>
                                                <small class="text-muted"><?= date('M j', strtotime($listing['created_at'])) ?></small>
                                            </div>
                                        </div>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6><i class="fas fa-clock me-2"></i>Pending Approvals</h6>
                                </div>
                                <div class="card-body">
                                    <?php if (empty($recent_pending)): ?>
                                        <p class="text-muted text-center">No pending dealers</p>
                                    <?php else: ?>
                                        <?php foreach ($recent_pending as $dealer): ?>
                                        <div class="d-flex justify-content-between align-items-center mb-2 p-2 border-bottom">
                                            <div>
                                                <strong><?= htmlspecialchars($dealer['company_name']) ?></strong>
                                                <small class="d-block text-muted"><?= htmlspecialchars($dealer['username']) ?></small>
                                            </div>
                                            <div>
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="action" value="approve_dealer">
                                                    <input type="hidden" name="dealer_id" value="<?= $dealer['user_id'] ?>">
                                                    <button type="submit" class="btn btn-success btn-sm">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php
            break;

        case 'pending_dealers':
            if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] != 'admin') {
                ob_end_clean();
                header("Location: ?action=login");
                exit;
            }

            $stmt = $pdo->query("SELECT * FROM users WHERE user_type = 'dealer' AND status = 'pending' ORDER BY registered_at DESC");
            $pending_dealers = $stmt->fetchAll();
            ?>
            
            <div class="page-header fade-in">
                <div class="container">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1><i class="fas fa-clock me-2"></i>Pending Dealer Approvals</h1>
                            <p class="mb-0">Review and approve new dealer registrations</p>
                        </div>
                        <a href="?action=admin_dashboard" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <div class="data-table-wrapper fade-in">
                <div class="table-filters">
                    <div class="row">
                        <div class="col-md-6">
                            <h5><i class="fas fa-users me-2"></i>Dealer Approval Queue (<?= count($pending_dealers) ?>)</h5>
                        </div>
                        <div class="col-md-6 text-end">
                            <span class="badge bg-warning fs-6"><?= count($pending_dealers) ?> Pending</span>
                        </div>
                    </div>
                </div>
                
                <?php if (empty($pending_dealers)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
                        <h4>All Caught Up!</h4>
                        <p class="text-muted">No dealers pending approval at this time.</p>
                    </div>
                <?php else: ?>
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Company Name</th>
                                <th>Contact Person</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>Registration Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($pending_dealers as $dealer): ?>
                            <tr>
                                <td>
                                    <strong><?= htmlspecialchars($dealer['company_name']) ?></strong>
                                </td>
                                <td><?= htmlspecialchars($dealer['username']) ?></td>
                                <td>
                                    <a href="mailto:<?= htmlspecialchars($dealer['email']) ?>">
                                        <?= htmlspecialchars($dealer['email']) ?>
                                    </a>
                                </td>
                                <td>
                                    <a href="tel:<?= htmlspecialchars($dealer['phone']) ?>">
                                        <?= htmlspecialchars($dealer['phone']) ?>
                                    </a>
                                </td>
                                <td><?= date('M j, Y g:i A', strtotime($dealer['registered_at'])) ?></td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="approve_dealer">
                                            <input type="hidden" name="dealer_id" value="<?= $dealer['user_id'] ?>">
                                            <button type="submit" class="btn btn-success btn-sm" onclick="return confirm('Approve this dealer?')">
                                                <i class="fas fa-check"></i> Approve
                                            </button>
                                        </form>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="reject_dealer">
                                            <input type="hidden" name="dealer_id" value="<?= $dealer['user_id'] ?>">
                                            <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Reject this dealer?')">
                                                <i class="fas fa-times"></i> Reject
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
            <?php
            break;

        case 'manage_makes':
            if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] != 'admin') {
                ob_end_clean();
                header("Location: ?action=login");
                exit;
            }

            // Get all categories for dropdown
            $stmt = $pdo->query("SELECT category_id, category_name FROM categories WHERE status = 'active' AND listing_type = 'sale' ORDER BY category_name");
            $categories = $stmt->fetchAll();

            // Get makes with category info
            $stmt = $pdo->query("
                SELECT m.*, c.category_name 
                FROM category_makes m 
                JOIN categories c ON m.category_id = c.category_id 
                WHERE c.listing_type = 'sale'
                ORDER BY c.category_name, m.make_name
            ");
            $makes = $stmt->fetchAll();
            ?>
            
            <div class="page-header fade-in">
                <div class="container">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1><i class="fas fa-industry me-2"></i>Makes & Models Management</h1>
                            <p class="mb-0">Manage vehicle makes and models for all categories</p>
                        </div>
                        <a href="?action=admin_dashboard" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="card slide-in-left">
                        <div class="card-header">
                            <h5><i class="fas fa-plus me-2"></i>Add New Make</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="?action=manage_makes">
                                <input type="hidden" name="action" value="add_make">
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-tags"></i>Category</label>
                                    <select class="form-control" name="category_id" required>
                                        <option value="">Select Category</option>
                                        <?php foreach ($categories as $cat): ?>
                                            <option value="<?= $cat['category_id'] ?>"><?= htmlspecialchars($cat['category_name']) ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-industry"></i>Make Name</label>
                                    <input type="text" class="form-control" name="make_name" required placeholder="e.g., Mercedes Benz">
                                </div>
                                
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-plus"></i>Add Make
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-8">
                    <div class="data-table-wrapper fade-in">
                        <div class="table-filters">
                            <h5><i class="fas fa-list me-2"></i>Current Makes (<?= count($makes) ?>)</h5>
                        </div>
                        
                        <?php if (empty($makes)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-industry fa-4x text-muted mb-3"></i>
                                <h4>No Makes Yet</h4>
                                <p class="text-muted">Add your first vehicle make using the form on the left.</p>
                            </div>
                        <?php else: ?>
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Category</th>
                                        <th>Make Name</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($makes as $make): ?>
                                    <tr>
                                        <td>
                                            <span class="badge bg-primary"><?= htmlspecialchars($make['category_name']) ?></span>
                                        </td>
                                        <td>
                                            <strong><?= htmlspecialchars($make['make_name']) ?></strong>
                                        </td>
                                        <td><?= date('M j, Y', strtotime($make['created_at'])) ?></td>
                                        <td>
                                            <a href="?action=manage_models&make_id=<?= $make['make_id'] ?>" class="btn btn-info btn-sm">
                                                <i class="fas fa-car"></i> Manage Models
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php
            break;

        case 'manage_models':
            if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] != 'admin') {
                ob_end_clean();
                header("Location: ?action=login");
                exit;
            }

            $make_id = $_GET['make_id'] ?? 0;
            
            // Get make info
            $stmt = $pdo->prepare("
                SELECT m.*, c.category_name 
                FROM category_makes m 
                JOIN categories c ON m.category_id = c.category_id 
                WHERE m.make_id = ?
            ");
            $stmt->execute([$make_id]);
            $make_info = $stmt->fetch();

            if (!$make_info) {
                ob_end_clean();
                header("Location: ?action=manage_makes");
                exit;
            }

            // Get models for this make
            $stmt = $pdo->prepare("SELECT * FROM category_models WHERE make_id = ? ORDER BY model_name");
            $stmt->execute([$make_id]);
            $models = $stmt->fetchAll();
            ?>
            
            <div class="page-header fade-in">
                <div class="container">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1><i class="fas fa-car me-2"></i>Models for <?= htmlspecialchars($make_info['make_name']) ?></h1>
                            <p class="mb-0">Category: <?= htmlspecialchars($make_info['category_name']) ?></p>
                        </div>
                        <a href="?action=manage_makes" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left"></i>Back to Makes
                        </a>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="card slide-in-left">
                        <div class="card-header">
                            <h5><i class="fas fa-plus me-2"></i>Add New Model</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="?action=manage_models">
                                <input type="hidden" name="action" value="add_model">
                                <input type="hidden" name="make_id" value="<?= $make_id ?>">
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-car"></i>Model Name</label>
                                    <input type="text" class="form-control" name="model_name" required placeholder="e.g., Actros">
                                </div>
                                
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-plus"></i>Add Model
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-8">
                    <div class="data-table-wrapper fade-in">
                        <div class="table-filters">
                            <h5><i class="fas fa-list me-2"></i>Current Models (<?= count($models) ?>)</h5>
                        </div>
                        
                        <?php if (empty($models)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-car fa-4x text-muted mb-3"></i>
                                <h4>No Models Yet</h4>
                                <p class="text-muted">Add your first model for this make using the form on the left.</p>
                            </div>
                        <?php else: ?>
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Model Name</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($models as $model): ?>
                                    <tr>
                                        <td>
                                            <strong><?= htmlspecialchars($model['model_name']) ?></strong>
                                        </td>
                                      <td><?= date('M j, Y', strtotime($variant['created_at'] ?? 'now')) ?></td>
                                        <td>
                                            <a href="?action=manage_variants&model_id=<?= $model['model_id'] ?>" class="btn btn-info btn-sm">
                                                <i class="fas fa-cogs"></i> Manage Variants
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php
            break;

        case 'manage_variants':
            if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] != 'admin') {
                ob_end_clean();
                header("Location: ?action=login");
                exit;
            }

            $model_id = $_GET['model_id'] ?? 0;
            
            // Get model info
            $stmt = $pdo->prepare("
                SELECT mo.*, ma.make_name, c.category_name 
                FROM category_models mo
                JOIN category_makes ma ON mo.make_id = ma.make_id
                JOIN categories c ON ma.category_id = c.category_id 
                WHERE mo.model_id = ?
            ");
            $stmt->execute([$model_id]);
            $model_info = $stmt->fetch();

            if (!$model_info) {
                ob_end_clean();
                header("Location: ?action=manage_makes");
                exit;
            }

            // Get variants for this model
            $stmt = $pdo->prepare("SELECT * FROM category_variants WHERE model_id = ? ORDER BY variant_name");
            $stmt->execute([$model_id]);
            $variants = $stmt->fetchAll();
            ?>
            
            <div class="page-header fade-in">
                <div class="container">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1><i class="fas fa-cogs me-2"></i>Variants for <?= htmlspecialchars($model_info['make_name'] . ' ' . $model_info['model_name']) ?></h1>
                            <p class="mb-0">Category: <?= htmlspecialchars($model_info['category_name']) ?></p>
                        </div>
                        <a href="?action=manage_models&make_id=<?= $model_info['make_id'] ?>" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left"></i>Back to Models
                        </a>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="card slide-in-left">
                        <div class="card-header">
                            <h5><i class="fas fa-plus me-2"></i>Add New Variant</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="?action=manage_variants">
                                <input type="hidden" name="action" value="add_variant">
                                <input type="hidden" name="model_id" value="<?= $model_id ?>">
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-cogs"></i>Variant Name</label>
                                    <input type="text" class="form-control" name="variant_name" required placeholder="e.g., Standard">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-align-left"></i>Description</label>
                                    <textarea class="form-control" name="variant_description" rows="3" placeholder="Optional description"></textarea>
                                </div>
                                
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-plus"></i>Add Variant
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-8">
                    <div class="data-table-wrapper fade-in">
                        <div class="table-filters">
                            <h5><i class="fas fa-list me-2"></i>Current Variants (<?= count($variants) ?>)</h5>
                        </div>
                        
                        <?php if (empty($variants)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-cogs fa-4x text-muted mb-3"></i>
                                <h4>No Variants Yet</h4>
                                <p class="text-muted">Add your first variant for this model using the form on the left.</p>
                            </div>
                        <?php else: ?>
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Variant Name</th>
                                        <th>Description</th>
                                        <th>Created</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($variants as $variant): ?>
                                    <tr>
                                        <td>
                                            <strong><?= htmlspecialchars($variant['variant_name']) ?></strong>
                                        </td>
                                        <td>
                                            <?= htmlspecialchars($variant['variant_description']) ?>
                                        </td>
                                        <td><?= date('M j, Y', strtotime($variant['created_at'] ?? 'now')) ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php
            break;

        case 'manage_years':
            if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] != 'admin') {
                ob_end_clean();
                header("Location: ?action=login");
                exit;
            }

            // Get all years
            $stmt = $pdo->query("SELECT * FROM system_years ORDER BY year_value DESC");
            $years = $stmt->fetchAll();
            ?>
            
            <div class="page-header fade-in">
                <div class="container">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1><i class="fas fa-calendar me-2"></i>System Years Management</h1>
                            <p class="mb-0">Manage available years for vehicle listings</p>
                        </div>
                        <a href="?action=admin_dashboard" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="card slide-in-left">
                        <div class="card-header">
                            <h5><i class="fas fa-plus me-2"></i>Add New Year</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="?action=manage_years">
                                <input type="hidden" name="action" value="add_year">
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-calendar"></i>Year</label>
                                    <input type="number" class="form-control" name="year_value" required min="1900" max="<?= date('Y') + 5 ?>" placeholder="<?= date('Y') + 1 ?>">
                                    <small class="text-muted">Add future years for new models</small>
                                </div>
                                
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-plus"></i>Add Year
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-8">
                    <div class="data-table-wrapper fade-in">
                        <div class="table-filters">
                            <h5><i class="fas fa-list me-2"></i>Available Years (<?= count($years) ?>)</h5>
                        </div>
                        
                        <div style="max-height: 600px; overflow-y: auto;">
                            <table class="table table-hover">
                                <thead class="sticky-top">
                                    <tr>
                                        <th>Year</th>
                                        <th>Status</th>
                                        <th>Added</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($years as $year): ?>
                                    <tr>
                                        <td>
                                            <strong><?= $year['year_value'] ?></strong>
                                            <?php if ($year['year_value'] > date('Y')): ?>
                                                <span class="badge bg-info ms-2">Future</span>
                                            <?php elseif ($year['year_value'] == date('Y')): ?>
                                                <span class="badge bg-success ms-2">Current</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?= $year['status'] == 'active' ? 'success' : 'secondary' ?>">
                                                <?= ucfirst($year['status']) ?>
                                            </span>
                                        </td>
                                        <td><?= date('M j, Y', strtotime($year['created_at'])) ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <?php
            break;

        case 'active_dealers':
            if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] != 'admin') {
                ob_end_clean();
                header("Location: ?action=login");
                exit;
            }

            $stmt = $pdo->query("SELECT u.*, COUNT(v.vehicle_id) as vehicle_count FROM users u LEFT JOIN vehicles v ON u.user_id = v.dealer_id WHERE u.user_type = 'dealer' AND u.status = 'active' GROUP BY u.user_id ORDER BY u.registered_at DESC");
            $active_dealers = $stmt->fetchAll();
            ?>
            
            <div class="page-header fade-in">
                <div class="container">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1><i class="fas fa-users me-2"></i>Active Dealers</h1>
                            <p class="mb-0">Manage active dealer accounts and performance</p>
                        </div>
                        <a href="?action=admin_dashboard" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <div class="data-table-wrapper fade-in">
                <div class="table-filters">
                    <div class="row">
                        <div class="col-md-6">
                            <h5><i class="fas fa-check-circle me-2"></i>Active Dealers (<?= count($active_dealers) ?>)</h5>
                        </div>
                        <div class="col-md-6 text-end">
                            <span class="badge bg-success fs-6"><?= count($active_dealers) ?> Active</span>
                        </div>
                    </div>
                </div>
                
                <?php if (empty($active_dealers)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-4x text-muted mb-3"></i>
                        <h4>No Active Dealers</h4>
                        <p class="text-muted">No active dealers in the system yet.</p>
                    </div>
                <?php else: ?>
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Company Name</th>
                                <th>Contact Person</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>Listings</th>
                                <th>Joined</th>
                                <th>Last Login</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($active_dealers as $dealer): ?>
                            <tr>
                                <td>
                                    <strong><?= htmlspecialchars($dealer['company_name']) ?></strong>
                                </td>
                                <td><?= htmlspecialchars($dealer['username']) ?></td>
                                <td>
                                    <a href="mailto:<?= htmlspecialchars($dealer['email']) ?>">
                                        <?= htmlspecialchars($dealer['email']) ?>
                                    </a>
                                </td>
                                <td>
                                    <a href="tel:<?= htmlspecialchars($dealer['phone']) ?>">
                                        <?= htmlspecialchars($dealer['phone']) ?>
                                    </a>
                                </td>
                                <td>
                                    <span class="badge bg-primary"><?= $dealer['vehicle_count'] ?> vehicles</span>
                                </td>
                                <td><?= date('M j, Y', strtotime($dealer['registered_at'])) ?></td>
                                <td>
                                    <?= $dealer['last_login'] ? date('M j, Y g:i A', strtotime($dealer['last_login'])) : 'Never' ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="?action=dealer_details&dealer_id=<?= $dealer['user_id'] ?>" class="btn btn-info btn-sm">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                        <button class="btn btn-warning btn-sm" onclick="suspendDealer(<?= $dealer['user_id'] ?>)">
                                            <i class="fas fa-pause"></i> Suspend
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
            <?php
            break;

        case 'all_listings':
            if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] != 'admin') {
                ob_end_clean();
                header("Location: ?action=login");
                exit;
            }

            // Get all listings with dealer info
            $stmt = $pdo->query("
                SELECT v.*, u.company_name, u.username, c.category_name
                FROM vehicles v 
                JOIN users u ON v.dealer_id = u.user_id 
                LEFT JOIN categories c ON v.category = c.category_key AND c.listing_type = v.listing_type
                ORDER BY v.created_at DESC
                LIMIT 50
            ");
            $all_listings = $stmt->fetchAll();

            // Get counts by status
            $stmt = $pdo->query("SELECT status, COUNT(*) as count FROM vehicles GROUP BY status");
            $status_counts = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
            ?>
            
            <div class="page-header fade-in">
                <div class="container">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1><i class="fas fa-list me-2"></i>All Vehicle Listings</h1>
                            <p class="mb-0">Monitor and manage all vehicle listings in the system</p>
                        </div>
                        <a href="?action=admin_dashboard" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <!-- Status Summary -->
            <div class="row mb-4 fade-in">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-warning"><?= $status_counts['pending_approval'] ?? 0 ?></h3>
                            <p class="text-muted">Pending Approval</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-success"><?= $status_counts['available'] ?? 0 ?></h3>
                            <p class="text-muted">Available</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-info"><?= $status_counts['sold'] ?? 0 ?></h3>
                            <p class="text-muted">Sold</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-secondary"><?= $status_counts['draft'] ?? 0 ?></h3>
                            <p class="text-muted">Drafts</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="data-table-wrapper fade-in">
                <div class="table-filters">
                    <h5><i class="fas fa-table me-2"></i>Recent Listings (Last 50)</h5>
                </div>
                
                <?php if (empty($all_listings)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-list fa-4x text-muted mb-3"></i>
                        <h4>No Listings</h4>
                        <p class="text-muted">No vehicle listings in the system yet.</p>
                    </div>
                <?php else: ?>
                    <div style="overflow-x: auto;">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Vehicle</th>
                                    <th>Dealer</th>
                                    <th>Category</th>
                                    <th>Type</th>
                                    <th>Price</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($all_listings as $listing): ?>
                                <tr>
                                    <td>
                                        <strong><?= htmlspecialchars($listing['make'] . ' ' . $listing['model']) ?></strong>
                                        <?php if ($listing['year']): ?>
                                            <br><small class="text-muted"><?= $listing['year'] ?></small>
                                        <?php endif; ?>
                                        <?php if ($listing['premium_listing']): ?>
                                            <span class="badge bg-warning">Premium</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <strong><?= htmlspecialchars($listing['company_name']) ?></strong>
                                        <br><small class="text-muted"><?= htmlspecialchars($listing['username']) ?></small>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary"><?= htmlspecialchars($listing['category_name'] ?? $listing['category']) ?></span>
                                    </td>
                                    <td>
                                        <span class="badge listing-type-<?= str_replace('-', '_', $listing['listing_type']) ?>">
                                            <?= strtoupper($listing['listing_type']) ?>
                                        </span>
                                    </td>
                                    <td>R<?= number_format($listing['price'], 0) ?></td>
                                    <td>
                                        <span class="badge bg-<?= $listing['status'] == 'available' ? 'success' : ($listing['status'] == 'pending_approval' ? 'warning' : 'secondary') ?>">
                                            <?= ucwords(str_replace('_', ' ', $listing['status'])) ?>
                                        </span>
                                    </td>
                                    <td><?= date('M j, Y', strtotime($listing['created_at'])) ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="?action=view_listing&id=<?= $listing['vehicle_id'] ?>" class="btn btn-info btn-sm">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php if ($listing['status'] == 'pending_approval'): ?>
                                                <button class="btn btn-success btn-sm" onclick="approveListing(<?= $listing['vehicle_id'] ?>)">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
            <?php
            break;

        case 'premium_management':
            if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] != 'admin') {
                ob_end_clean();
                header("Location: ?action=login");
                exit;
            }

            // Get premium listings
            $stmt = $pdo->query("
                SELECT v.*, u.company_name, u.username
                FROM vehicles v 
                JOIN users u ON v.dealer_id = u.user_id 
                WHERE v.premium_listing = 1
                ORDER BY v.premium_until DESC, v.created_at DESC
            ");
            $premium_listings = $stmt->fetchAll();

            // Get potential premium candidates
            $stmt = $pdo->query("
                SELECT v.*, u.company_name, u.username
                FROM vehicles v 
                JOIN users u ON v.dealer_id = u.user_id 
                WHERE v.premium_listing = 0 AND v.status = 'available'
                ORDER BY v.created_at DESC
                LIMIT 20
            ");
            $premium_candidates = $stmt->fetchAll();
            ?>
            
            <div class="page-header fade-in">
                <div class="container">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1><i class="fas fa-star me-2"></i>Premium Listings Management</h1>
                            <p class="mb-0">Manage premium listings and featured vehicles</p>
                        </div>
                        <a href="?action=admin_dashboard" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <div class="card fade-in">
                        <div class="card-header">
                            <h5><i class="fas fa-crown me-2"></i>Current Premium Listings (<?= count($premium_listings) ?>)</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($premium_listings)): ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-star fa-3x text-muted mb-3"></i>
                                    <h5>No Premium Listings</h5>
                                    <p class="text-muted">No premium listings are currently active.</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Vehicle</th>
                                                <th>Dealer</th>
                                                <th>Price</th>
                                                <th>Premium Until</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($premium_listings as $listing): ?>
                                            <tr>
                                                <td>
                                                    <strong><?= htmlspecialchars($listing['make'] . ' ' . $listing['model']) ?></strong>
                                                    <?php if ($listing['year']): ?>
                                                        <br><small class="text-muted"><?= $listing['year'] ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <strong><?= htmlspecialchars($listing['company_name']) ?></strong>
                                                    <br><small class="text-muted"><?= htmlspecialchars($listing['username']) ?></small>
                                                </td>
                                                <td>R<?= number_format($listing['price'], 0) ?></td>
                                                <td>
                                                    <?php if ($listing['premium_until']): ?>
                                                        <?= date('M j, Y', strtotime($listing['premium_until'])) ?>
                                                        <?php if (strtotime($listing['premium_until']) < time()): ?>
                                                            <span class="badge bg-danger">Expired</span>
                                                        <?php endif; ?>
                                                    <?php else: ?>
                                                        <span class="badge bg-success">Unlimited</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <button class="btn btn-warning btn-sm" onclick="editPremium(<?= $listing['vehicle_id'] ?>)">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn btn-danger btn-sm" onclick="removePremium(<?= $listing['vehicle_id'] ?>)">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card slide-in-left">
                        <div class="card-header">
                            <h5><i class="fas fa-plus me-2"></i>Add Premium Listing</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="?action=premium_management">
                                <input type="hidden" name="action" value="set_premium">
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-car"></i>Select Vehicle</label>
                                    <select class="form-control" name="vehicle_id" required>
                                        <option value="">Choose a vehicle...</option>
                                        <?php foreach ($premium_candidates as $candidate): ?>
                                            <option value="<?= $candidate['vehicle_id'] ?>">
                                                <?= htmlspecialchars($candidate['make'] . ' ' . $candidate['model']) ?> - 
                                                <?= htmlspecialchars($candidate['company_name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-calendar"></i>Premium Until</label>
                                    <input type="date" class="form-control" name="premium_until" 
                                           min="<?= date('Y-m-d') ?>" 
                                           value="<?= date('Y-m-d', strtotime('+30 days')) ?>">
                                    <small class="text-muted">Leave empty for unlimited premium</small>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="premium_listing" checked>
                                        <label class="form-check-label">Enable Premium Listing</label>
                                    </div>
                                </div>
                                
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-star"></i>Make Premium
                                </button>
                            </form>
                        </div>
                    </div>
                    
                    <div class="card mt-4">
                        <div class="card-header">
                            <h6><i class="fas fa-info-circle me-2"></i>Premium Benefits</h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Priority placement</li>
                                <li><i class="fas fa-check text-success me-2"></i>Enhanced visibility</li>
                                <li><i class="fas fa-check text-success me-2"></i>Premium badge</li>
                                <li><i class="fas fa-check text-success me-2"></i>Featured in listings</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <?php
            break;

        case 'view_listing':
            $vehicle_id = $_GET['id'] ?? 0;
            
            // Get vehicle details with dealer info
            $stmt = $pdo->prepare("
                SELECT v.*, u.company_name, u.username, u.phone, u.email, c.category_name
                FROM vehicles v 
                JOIN users u ON v.dealer_id = u.user_id 
                LEFT JOIN categories c ON v.category = c.category_key AND c.listing_type = v.listing_type
                WHERE v.vehicle_id = ?
            ");
            $stmt->execute([$vehicle_id]);
            $vehicle = $stmt->fetch();

            if (!$vehicle) {
                $error = "Vehicle not found.";
                break;
            }

            // Get vehicle images
            $stmt = $pdo->prepare("SELECT * FROM vehicle_images WHERE vehicle_id = ? ORDER BY is_primary DESC, image_order");
            $stmt->execute([$vehicle_id]);
            $images = $stmt->fetchAll();

            // Get vehicle videos
            $stmt = $pdo->prepare("SELECT * FROM vehicle_videos WHERE vehicle_id = ? ORDER BY uploaded_at");
            $stmt->execute([$vehicle_id]);
            $videos = $stmt->fetchAll();

            // Get vehicle documents
            $stmt = $pdo->prepare("SELECT * FROM vehicle_documents WHERE vehicle_id = ? ORDER BY uploaded_at");
            $stmt->execute([$vehicle_id]);
            $documents = $stmt->fetchAll();

            // Get dealer's sales team
            $stmt = $pdo->prepare("SELECT * FROM dealer_sales_team WHERE dealer_id = ? AND status = 'active' ORDER BY created_at");
            $stmt->execute([$vehicle['dealer_id']]);
            $sales_team = $stmt->fetchAll();

            // Increment view count
            $pdo->prepare("UPDATE vehicles SET views = views + 1 WHERE vehicle_id = ?")->execute([$vehicle_id]);
            ?>
            
            <div class="container mt-4">
                <nav aria-label="breadcrumb" class="fade-in">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <?php if (isset($_SESSION['user_type']) && $_SESSION['user_type'] == 'admin'): ?>
                                <a href="?action=admin_dashboard">Admin Dashboard</a>
                            <?php elseif (isset($_SESSION['user_type']) && $_SESSION['user_type'] == 'dealer'): ?>
                                <a href="?action=dealer_dashboard">Dealer Dashboard</a>
                            <?php else: ?>
                                <a href="?action=home">Home</a>
                            <?php endif; ?>
                        </li>
                        <li class="breadcrumb-item active"><?= htmlspecialchars($vehicle['make'] . ' ' . $vehicle['model']) ?></li>
                    </ol>
                </nav>

                <div class="row">
                    <div class="col-md-8">
                        <!-- Vehicle Images -->
                        <div class="card mb-4 fade-in">
                            <div class="card-body p-0">
                                <?php if (!empty($images)): ?>
                                    <div id="vehicleCarousel" class="carousel slide" data-bs-ride="carousel">
                                        <div class="carousel-inner">
                                            <?php foreach ($images as $index => $image): ?>
                                            <div class="carousel-item <?= $index === 0 ? 'active' : '' ?>">
                                                <img src="<?= htmlspecialchars($image['image_path']) ?>" 
                                                     class="d-block w-100" 
                                                     style="height: 400px; object-fit: cover;"
                                                     alt="Vehicle Image">
                                            </div>
                                            <?php endforeach; ?>
                                        </div>
                                        
                                        <?php if (count($images) > 1): ?>
                                            <button class="carousel-control-prev" type="button" data-bs-target="#vehicleCarousel" data-bs-slide="prev">
                                                <span class="carousel-control-prev-icon"></span>
                                            </button>
                                            <button class="carousel-control-next" type="button" data-bs-target="#vehicleCarousel" data-bs-slide="next">
                                                <span class="carousel-control-next-icon"></span>
                                            </button>
                                            
                                            <div class="carousel-indicators">
                                                <?php foreach ($images as $index => $image): ?>
                                                    <button type="button" data-bs-target="#vehicleCarousel" data-bs-slide-to="<?= $index ?>" 
                                                            <?= $index === 0 ? 'class="active"' : '' ?>></button>
                                                <?php endforeach; ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php else: ?>
                                    <img src="https://via.placeholder.com/800x400?text=No+Images+Available" 
                                         class="w-100" style="height: 400px; object-fit: cover;" alt="No Image">
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Vehicle Details -->
                        <div class="card mb-4 slide-in-left">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h4>
                                        <?= htmlspecialchars($vehicle['make'] . ' ' . $vehicle['model']) ?>
                                        <?php if ($vehicle['variant']): ?>
                                            <small class="text-muted"><?= htmlspecialchars($vehicle['variant']) ?></small>
                                        <?php endif; ?>
                                    </h4>
                                    <div>
                                        <span class="badge listing-type-<?= str_replace('-', '_', $vehicle['listing_type']) ?> fs-6">
                                            <?= strtoupper(str_replace('-', ' ', $vehicle['listing_type'])) ?>
                                        </span>
                                        <?php if ($vehicle['premium_listing'] && (!$vehicle['premium_until'] || strtotime($vehicle['premium_until']) > time())): ?>
                                            <span class="badge bg-warning fs-6 ms-2">
                                                <i class="fas fa-star"></i> PREMIUM
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h2 class="text-primary mb-4">R<?= number_format($vehicle['price'], 2) ?></h2>
                                        
                                        <div class="row mb-3">
                                            <?php if ($vehicle['year']): ?>
                                            <div class="col-6 mb-3">
                                                <div class="feature-icon d-inline-flex">
                                                    <i class="fas fa-calendar"></i>
                                                </div>
                                                <div class="d-inline-block ms-2">
                                                    <strong>Year</strong><br>
                                                    <span class="text-muted"><?= $vehicle['year'] ?></span>
                                                </div>
                                            </div>
                                            <?php endif; ?>
                                            
                                            <?php if ($vehicle['mileage']): ?>
                                            <div class="col-6 mb-3">
                                                <div class="feature-icon d-inline-flex">
                                                    <i class="fas fa-tachometer-alt"></i>
                                                </div>
                                                <div class="d-inline-block ms-2">
                                                    <strong>Mileage</strong><br>
                                                    <span class="text-muted"><?= number_format($vehicle['mileage']) ?> KM</span>
                                                </div>
                                            </div>
                                            <?php endif; ?>
                                            
                                            <?php if ($vehicle['hours_used']): ?>
                                            <div class="col-6 mb-3">
                                                <div class="feature-icon d-inline-flex">
                                                    <i class="fas fa-clock"></i>
                                                </div>
                                                <div class="d-inline-block ms-2">
                                                    <strong>Hours</strong><br>
                                                    <span class="text-muted"><?= number_format($vehicle['hours_used']) ?> hrs</span>
                                                </div>
                                            </div>
                                            <?php endif; ?>
                                            
                                            <?php if ($vehicle['engine_type']): ?>
                                            <div class="col-6 mb-3">
                                                <div class="feature-icon d-inline-flex">
                                                    <i class="fas fa-cog"></i>
                                                </div>
                                                <div class="d-inline-block ms-2">
                                                    <strong>Engine</strong><br>
                                                    <span class="text-muted"><?= htmlspecialchars($vehicle['engine_type']) ?></span>
                                                </div>
                                            </div>
                                            <?php endif; ?>
                                            
                                            <?php if ($vehicle['transmission']): ?>
                                            <div class="col-6 mb-3">
                                                <div class="feature-icon d-inline-flex">
                                                    <i class="fas fa-exchange-alt"></i>
                                                </div>
                                                <div class="d-inline-block ms-2">
                                                    <strong>Transmission</strong><br>
                                                    <span class="text-muted"><?= ucfirst($vehicle['transmission']) ?></span>
                                                </div>
                                            </div>
                                            <?php endif; ?>
                                            
                                            <?php if ($vehicle['fuel_type']): ?>
                                            <div class="col-6 mb-3">
                                                <div class="feature-icon d-inline-flex">
                                                    <i class="fas fa-gas-pump"></i>
                                                </div>
                                                <div class="d-inline-block ms-2">
                                                    <strong>Fuel Type</strong><br>
                                                    <span class="text-muted"><?= ucfirst($vehicle['fuel_type']) ?></span>
                                                </div>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <h6 class="mb-3">Vehicle Features</h6>
                                        <div class="row">
                                            <?php if ($vehicle['no_accidents']): ?>
                                                <div class="col-6 mb-2">
                                                    <i class="fas fa-check-circle text-success me-2"></i>No Accidents
                                                </div>
                                            <?php endif; ?>
                                            <?php if ($vehicle['warranty']): ?>
                                                <div class="col-6 mb-2">
                                                    <i class="fas fa-shield-alt text-success me-2"></i>Warranty
                                                </div>
                                            <?php endif; ?>
                                            <?php if ($vehicle['finance_available']): ?>
                                                <div class="col-6 mb-2">
                                                    <i class="fas fa-credit-card text-success me-2"></i>Finance Available
                                                </div>
                                            <?php endif; ?>
                                            <?php if ($vehicle['trade_in']): ?>
                                                <div class="col-6 mb-2">
                                                    <i class="fas fa-exchange-alt text-success me-2"></i>Trade In
                                                </div>
                                            <?php endif; ?>
                                            <?php if ($vehicle['service_history']): ?>
                                                <div class="col-6 mb-2">
                                                    <i class="fas fa-clipboard-list text-success me-2"></i>Service History
                                                </div>
                                            <?php endif; ?>
                                            <?php if ($vehicle['roadworthy']): ?>
                                                <div class="col-6 mb-2">
                                                    <i class="fas fa-certificate text-success me-2"></i>Roadworthy
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <div class="mt-4">
                                            <p><strong><i class="fas fa-map-marker-alt me-2"></i>Location:</strong> 
                                               <?= htmlspecialchars($vehicle['city'] . ', ' . $vehicle['region']) ?></p>
                                            <p><strong><i class="fas fa-eye me-2"></i>Views:</strong> <?= number_format($vehicle['views']) ?></p>
                                        </div>
                                    </div>
                                </div>
                                
                                <?php if ($vehicle['description']): ?>
                                    <hr>
                                    <h6>Description</h6>
                                    <p><?= nl2br(htmlspecialchars($vehicle['description'])) ?></p>
                                <?php endif; ?>
                                
                                <?php if ($vehicle['features']): ?>
                                    <hr>
                                    <h6>Additional Features</h6>
                                    <p><?= nl2br(htmlspecialchars($vehicle['features'])) ?></p>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Videos and Documents -->
                        <?php if (!empty($videos) || !empty($documents) || $vehicle['youtube_video_url']): ?>
                        <div class="card mb-4 fade-in">
                            <div class="card-header">
                                <h5><i class="fas fa-play-circle me-2"></i>Media & Documents</h5>
                            </div>
                            <div class="card-body">
                                <?php if ($vehicle['youtube_video_url']): ?>
                                    <div class="mb-4">
                                        <h6>Video Tour</h6>
                                        <div class="ratio ratio-16x9">
                                            <iframe src="<?= str_replace('watch?v=', 'embed/', $vehicle['youtube_video_url']) ?>" 
                                                    allowfullscreen></iframe>
                                        </div>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if (!empty($videos)): ?>
                                    <div class="mb-4">
                                        <h6>Vehicle Videos</h6>
                                        <div class="row">
                                            <?php foreach ($videos as $video): ?>
                                            <div class="col-md-6 mb-3">
                                                <video controls class="w-100" style="max-height: 200px;">
                                                    <source src="<?= htmlspecialchars($video['video_path']) ?>" type="video/mp4">
                                                    Your browser does not support the video tag.
                                                </video>
                                                <?php if ($video['video_title']): ?>
                                                    <p class="mt-2"><small><?= htmlspecialchars($video['video_title']) ?></small></p>
                                                <?php endif; ?>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if (!empty($documents)): ?>
                                    <div>
                                        <h6>Documents</h6>
                                        <div class="list-group">
                                            <?php foreach ($documents as $doc): ?>
                                            <a href="<?= htmlspecialchars($doc['document_path']) ?>" 
                                               class="list-group-item list-group-item-action" 
                                               target="_blank">
                                                <i class="fas fa-file-pdf text-danger me-2"></i>
                                                <?= htmlspecialchars($doc['document_name']) ?>
                                                <small class="text-muted ms-2">(<?= round($doc['file_size'] / 1024) ?> KB)</small>
                                            </a>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="col-md-4">
                        <!-- Contact Dealer -->
                        <div class="card mb-4 slide-in-left">
                            <div class="card-header">
                                <h5><i class="fas fa-building me-2"></i>Contact Dealer</h5>
                            </div>
                            <div class="card-body">
                                <h6><?= htmlspecialchars($vehicle['company_name']) ?></h6>
                                <p class="text-muted"><?= htmlspecialchars($vehicle['username']) ?></p>
                                
                                <div class="d-grid gap-2">
                                    <a href="tel:<?= htmlspecialchars($vehicle['phone']) ?>" class="btn btn-primary">
                                        <i class="fas fa-phone me-2"></i>Call Now
                                    </a>
                                    <a href="mailto:<?= htmlspecialchars($vehicle['email']) ?>" class="btn btn-outline-primary">
                                        <i class="fas fa-envelope me-2"></i>Send Email
                                    </a>
                                    <button class="btn btn-success" onclick="openInquiryForm()">
                                        <i class="fas fa-comment me-2"></i>Send Inquiry
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Sales Team -->
                        <?php if (!empty($sales_team)): ?>
                        <div class="card mb-4 fade-in">
                            <div class="card-header">
                                <h6><i class="fas fa-users me-2"></i>Sales Team</h6>
                            </div>
                            <div class="card-body">
                                <?php foreach ($sales_team as $member): ?>
                                <div class="d-flex align-items-center mb-3">
                                    <?php if ($member['photo']): ?>
                                        <img src="<?= htmlspecialchars($member['photo']) ?>" 
                                             alt="Photo" class="rounded-circle me-3" 
                                             style="width: 50px; height: 50px; object-fit: cover;">
                                    <?php else: ?>
                                        <div class="bg-light rounded-circle me-3 d-flex align-items-center justify-content-center" 
                                             style="width: 50px; height: 50px;">
                                            <i class="fas fa-user text-muted"></i>
                                        </div>
                                    <?php endif; ?>
                                    <div>
                                        <h6 class="mb-1"><?= htmlspecialchars($member['name']) ?></h6>
                                        <?php if ($member['position']): ?>
                                            <small class="text-muted"><?= htmlspecialchars($member['position']) ?></small>
                                        <?php endif; ?>
                                        <div class="mt-1">
                                            <?php if ($member['phone']): ?>
                                                <a href="tel:<?= htmlspecialchars($member['phone']) ?>" class="btn btn-sm btn-outline-primary me-1">
                                                    <i class="fas fa-phone"></i>
                                                </a>
                                            <?php endif; ?>
                                            <?php if ($member['whatsapp']): ?>
                                                <a href="https://wa.me/<?= htmlspecialchars($member['whatsapp']) ?>" 
                                                   class="btn btn-sm btn-outline-success" target="_blank">
                                                    <i class="fab fa-whatsapp"></i>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Quick Stats -->
                        <div class="card fade-in">
                            <div class="card-header">
                                <h6><i class="fas fa-chart-bar me-2"></i>Quick Stats</h6>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <h5 class="text-primary"><?= number_format($vehicle['views']) ?></h5>
                                        <small class="text-muted">Views</small>
                                    </div>
                                    <div class="col-6">
                                        <h5 class="text-success"><?= number_format($vehicle['leads_count']) ?></h5>
                                        <small class="text-muted">Inquiries</small>
                                    </div>
                                </div>
                                <hr>
                                <p class="small text-muted mb-0">
                                    <i class="fas fa-calendar me-1"></i>
                                    Listed on <?= date('M j, Y', strtotime($vehicle['created_at'])) ?>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Inquiry Modal -->
            <div class="modal fade" id="inquiryModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Send Inquiry</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="inquiryForm">
                                <input type="hidden" name="vehicle_id" value="<?= $vehicle_id ?>">
                                <input type="hidden" name="dealer_id" value="<?= $vehicle['dealer_id'] ?>">
                                
                                <div class="mb-3">
                                    <label class="form-label">Inquiry Type</label>
                                    <select class="form-control" name="inquiry_type" required>
                                        <option value="general">General Inquiry</option>
                                        <option value="finance">Finance Options</option>
                                        <option value="trade_in">Trade-in Value</option>
                                        <option value="inspection">Vehicle Inspection</option>
                                        <option value="test_drive">Test Drive</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Full Name</label>
                                    <input type="text" class="form-control" name="full_name" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Email</label>
                                    <input type="email" class="form-control" name="email" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Phone</label>
                                    <input type="tel" class="form-control" name="phone" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Message</label>
                                    <textarea class="form-control" name="message" rows="4" required
                                              placeholder="I'm interested in this <?= htmlspecialchars($vehicle['make'] . ' ' . $vehicle['model']) ?>..."></textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Preferred Contact Method</label>
                                    <select class="form-control" name="preferred_contact">
                                        <option value="phone">Phone</option>
                                        <option value="email">Email</option>
                                        <option value="whatsapp">WhatsApp</option>
                                    </select>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" onclick="submitInquiry()">Send Inquiry</button>
                        </div>
                    </div>
                </div>
            </div>

            <script>
            function openInquiryForm() {
                new bootstrap.Modal(document.getElementById('inquiryModal')).show();
            }

            function submitInquiry() {
                const form = document.getElementById('inquiryForm');
                const formData = new FormData(form);
                formData.append('action', 'submit_inquiry');
                
                fetch('?action=submit_inquiry', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Inquiry sent successfully!');
                        bootstrap.Modal.getInstance(document.getElementById('inquiryModal')).hide();
                    } else {
                        alert('Error sending inquiry: ' + data.error);
                    }
                })
                .catch(error => {
                    alert('Error sending inquiry. Please try again.');
                });
            }
            </script>
            <?php
            break;

        default:
            if (isset($_SESSION['user_type'])) {
                ob_end_clean();
                header("Location: ?action=" . $_SESSION['user_type'] . "_dashboard");
            } else {
                ob_end_clean();
                header("Location: ?action=home");
            }
            exit;
    }
    ?>
</div>

<!-- Enhanced AJAX JavaScript -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<script>
// Enhanced AJAX System for TrucksONSale - FIXED VERSION
class VehicleFormManager {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupFormValidation();
        this.setupListingTypeToggle();
        this.setupCharacterCounter();
        console.log(' TrucksONSale Professional AJAX System Initialized - FIXED VERSION');
    }

    setupEventListeners() {
        // Category change event
        const categorySelect = document.getElementById('category');
        if (categorySelect) {
            categorySelect.addEventListener('change', (e) => {
                const selectedOption = e.target.selectedOptions[0];
                const categoryId = selectedOption?.dataset.id;
                console.log('Category changed:', e.target.value, 'ID:', categoryId);
                this.onCategoryChange(e.target.value, categoryId);
            });
        }

        // Make change event
        const makeSelect = document.getElementById('make');
        if (makeSelect) {
            makeSelect.addEventListener('change', (e) => {
                const selectedOption = e.target.selectedOptions[0];
                const makeId = selectedOption?.dataset.id;
                console.log('Make changed:', e.target.value, 'ID:', makeId);
                this.onMakeChange(e.target.value, makeId);
            });
        }

        // Model change event
        const modelSelect = document.getElementById('model');
        if (modelSelect) {
            modelSelect.addEventListener('change', (e) => {
                const selectedOption = e.target.selectedOptions[0];
                const modelId = selectedOption?.dataset.id;
                console.log('Model changed:', e.target.value, 'ID:', modelId);
                this.onModelChange(modelId);
            });
        }

        // File upload validation
        this.setupFileValidation();
    }

    async onCategoryChange(categoryKey, categoryId) {
        console.log(' Category changed:', categoryKey, 'ID:', categoryId);
        
        // Reset dependent dropdowns
        this.resetSelect('subcategory', 'Select Subcategory');
        this.resetSelect('make', 'Select Make');
        this.resetSelect('model', 'Select Model');
        this.resetSelect('variant', 'Select Variant');

        if (!categoryId) return;

        // Load subcategories
        await this.loadSubcategories(categoryId);
        
        // Load makes
        await this.loadMakes(categoryId);
        
        // Load category-specific settings
        await this.loadCategorySettings(categoryId);
    }

    async onMakeChange(makeName, makeId) {
        console.log('🏭 Make changed:', makeName, 'ID:', makeId);
        
        // Reset dependent dropdowns
        this.resetSelect('model', 'Select Model');
        this.resetSelect('variant', 'Select Variant');

        if (!makeId) return;

        // Load models for this make
        await this.loadModels(makeId);
    }

    async onModelChange(modelId) {
        console.log('🚗 Model changed:', 'ID:', modelId);
        
        // Reset variant dropdown
        this.resetSelect('variant', 'Select Variant');

        if (!modelId) return;

        // Load variants for this model
        await this.loadVariants(modelId);
    }

    async loadSubcategories(categoryId) {
        this.showLoading('subcategory-spinner');
        
        try {
            const response = await fetch(`?ajax=get_subcategories&category_id=${categoryId}`);
            const data = await response.json();
            
            if (data.success) {
                const select = document.getElementById('subcategory');
                data.data.forEach(sub => {
                    select.appendChild(this.createOption(sub.subcategory_key, sub.subcategory_name));
                });
                select.disabled = false;
                
                // Add "Other" option
                select.appendChild(this.createOption('other', 'Other'));
                
                console.log('✅ Subcategories loaded:', data.data.length);
            } else {
                console.error('❌ Error loading subcategories:', data.error);
            }
        } catch (error) {
            console.error('❌ Error loading subcategories:', error);
            this.showError('Failed to load subcategories');
        } finally {
            this.hideLoading('subcategory-spinner');
        }
    }

    async loadMakes(categoryId) {
        this.showLoading('make-spinner');
        
        try {
            const response = await fetch(`?ajax=get_makes&category_id=${categoryId}`);
            const data = await response.json();
            
            if (data.success) {
                const select = document.getElementById('make');
                data.data.forEach(make => {
                    const option = this.createOption(make.make_name, make.make_name);
                    option.dataset.id = make.make_id;
                    select.appendChild(option);
                });
                select.disabled = false;
                
                console.log('✅ Makes loaded:', data.data.length);
            } else {
                console.error(' Error loading makes:', data.error);
            }
        } catch (error) {
            console.error('❌ Error loading makes:', error);
            this.showError('Failed to load makes');
        } finally {
            this.hideLoading('make-spinner');
        }
    }

    async loadModels(makeId) {
        this.showLoading('model-spinner');
        
        try {
            const response = await fetch(`?ajax=get_models&make_id=${makeId}`);
            const data = await response.json();
            
            if (data.success) {
                const select = document.getElementById('model');
                data.data.forEach(model => {
                    const option = this.createOption(model.model_name, model.model_name);
                    option.dataset.id = model.model_id;
                    select.appendChild(option);
                });
                select.disabled = false;
                
                console.log(' Models loaded:', data.data.length);
            } else {
                console.error('❌ Error loading models:', data.error);
            }
        } catch (error) {
            console.error('❌ Error loading models:', error);
            this.showError('Failed to load models');
        } finally {
            this.hideLoading('model-spinner');
        }
    }

    async loadVariants(modelId) {
        this.showLoading('variant-spinner');
        
        try {
            const response = await fetch(`?ajax=get_variants&model_id=${modelId}`);
            const data = await response.json();
            
            if (data.success) {
                const select = document.getElementById('variant');
                data.data.forEach(variant => {
                    select.appendChild(this.createOption(variant.variant_name, variant.variant_name));
                });
                
                // Always add "Other" option
                select.appendChild(this.createOption('other', 'Other'));
                
                select.disabled = false;
                
                console.log('✅ Variants loaded:', data.data.length);
            } else {
                console.error('❌ Error loading variants:', data.error);
            }
        } catch (error) {
            console.error('❌ Error loading variants:', error);
            this.showError('Failed to load variants');
        } finally {
            this.hideLoading('variant-spinner');
        }
    }

    async loadCategorySettings(categoryId) {
        try {
            const response = await fetch(`?ajax=get_category_info&category_id=${categoryId}`);
            const data = await response.json();
            
            if (data.success && data.data) {
                const info = data.data;
                
                // Show/hide hours field
                const hoursField = document.getElementById('hours-field');
                const mileageField = document.getElementById('mileage-field');
                const mileageLabel = document.getElementById('mileage-label');
                
                if (hoursField && mileageField && mileageLabel) {
                    if (info.show_hours === '1' || info.show_hours === 1) {
                        hoursField.style.display = 'block';
                        mileageLabel.innerHTML = '<i class="fas fa-tachometer-alt me-2"></i>Mileage (Optional)';
                    } else {
                        hoursField.style.display = 'none';
                        mileageLabel.innerHTML = `<i class="fas fa-tachometer-alt me-2"></i>${info.mileage_label || 'Mileage (KM)'}`;
                    }
                }
                
                console.log('✅ Category settings loaded:', info);
            }
        } catch (error) {
            console.error('❌ Error loading category settings:', error);
        }
    }

    resetSelect(selectId, placeholder = '') {
        const select = document.getElementById(selectId);
        if (select) {
            const placeholderText = placeholder || `Select ${selectId.charAt(0).toUpperCase() + selectId.slice(1)}`;
            select.innerHTML = `<option value="">${placeholderText}</option>`;
            select.disabled = true;
        }
    }

    createOption(value, text) {
        const option = document.createElement('option');
        option.value = value;
        option.textContent = text;
        return option;
    }

    showLoading(spinnerId) {
        const spinner = document.getElementById(spinnerId);
        if (spinner) {
            spinner.style.display = 'inline-block';
        }
    }

    hideLoading(spinnerId) {
        const spinner = document.getElementById(spinnerId);
        if (spinner) {
            spinner.style.display = 'none';
        }
    }

    showError(message) {
        console.error(' Error:', message);
        // Could implement toast notifications here
    }

    setupCharacterCounter() {
        const descriptionTextarea = document.getElementById('description');
        if (descriptionTextarea) {
            descriptionTextarea.addEventListener('input', (e) => {
                this.updateCharCounter(e.target);
            });
            this.updateCharCounter(descriptionTextarea);
        }
    }

    updateCharCounter(textarea) {
        const maxLength = 3000;
        const currentLength = textarea.value.length;
        const remaining = maxLength - currentLength;
        const counter = document.getElementById('char-counter');
        
        if (counter) {
            counter.textContent = `${remaining} characters remaining`;
            counter.className = 'char-counter';
            
            if (remaining < 300) {
                counter.className += ' warning';
            }
            if (remaining < 100) {
                counter.className += ' danger';
            }
        }
    }

    setupListingTypeToggle() {
        const listingTypeRadios = document.querySelectorAll('input[name="listing_type"]');
        const hireFields = document.getElementById('hire-fields');
        const auctionFields = document.getElementById('auction-fields');
        
        listingTypeRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                if (hireFields) hireFields.style.display = 'none';
                if (auctionFields) auctionFields.style.display = 'none';
                
                if (this.value === 'hire' || this.value === 'rent-to-own') {
                    if (hireFields) hireFields.style.display = 'block';
                } else if (this.value === 'auction') {
                    if (auctionFields) auctionFields.style.display = 'block';
                }
            });
        });
    }

    setupFileValidation() {
        const imageInput = document.getElementById('images');
        const videoInput = document.getElementById('videos');
        const documentInput = document.getElementById('documents');
        
        if (imageInput) {
            imageInput.addEventListener('change', function() {
                vehicleForm.validateFileUpload(this, 40, 2 * 1024 * 1024, ['jpg', 'jpeg', 'png', 'gif', 'webp']);
            });
        }
        
        if (videoInput) {
            videoInput.addEventListener('change', function() {
                vehicleForm.validateFileUpload(this, 5, 50 * 1024 * 1024, ['mp4', 'avi', 'mov', 'wmv', 'flv']);
            });
        }
        
        if (documentInput) {
            documentInput.addEventListener('change', function() {
                vehicleForm.validateFileUpload(this, 10, 10 * 1024 * 1024, ['pdf']);
            });
        }
    }

    validateFileUpload(input, maxFiles, maxSize, allowedTypes) {
        const files = input.files;
        
        if (files.length > maxFiles) {
            alert(`Maximum ${maxFiles} files allowed.`);
            input.value = '';
            return false;
        }
        
        for (let i = 0; i < files.length; i++) {
            if (files[i].size > maxSize) {
                alert(`File size must be less than ${Math.round(maxSize / 1024 / 1024)}MB.`);
                input.value = '';
                return false;
            }
            
            const fileExtension = files[i].name.split('.').pop().toLowerCase();
            if (!allowedTypes.includes(fileExtension)) {
                alert(`Only ${allowedTypes.join(', ')} files are allowed.`);
                input.value = '';
                return false;
            }
        }
        
        console.log('✅ File validation passed:', files.length, 'files');
        return true;
    }

    setupFormValidation() {
        const form = document.querySelector('form[action*="add_vehicle"]');
        if (form) {
            form.addEventListener('submit', (e) => {
                if (!this.validateForm()) {
                    e.preventDefault();
                    return false;
                }
            });
        }
    }

    validateForm() {
        const requiredFields = ['category', 'make', 'model', 'price', 'region', 'city'];
        let isValid = true;
        
        requiredFields.forEach(fieldName => {
            const field = document.querySelector(`[name="${fieldName}"]`);
            if (field && !field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else if (field) {
                field.classList.remove('is-invalid');
            }
        });
        
        if (!isValid) {
            alert('Please fill in all required fields.');
        }
        
        return isValid;
    }
}

// Admin Functions for Featured Management
function editFeatured(vehicleId) {
    const newDate = prompt('Enter new featured expiry date (YYYY-MM-DD) or leave empty for unlimited:');
    if (newDate !== null) {
        const formData = new FormData();
        formData.append('action', 'set_featured');
        formData.append('vehicle_id', vehicleId);
        formData.append('featured_until', newDate);
        formData.append('featured', 'on');
        
        fetch(window.location.href, {
            method: 'POST',
            body: formData
        })
        .then(response => response.text())
        .then(() => {
            location.reload();
        })
        .catch(error => {
            alert('Error updating featured: ' + error.message);
        });
    }
}

function removeFeatured(vehicleId) {
    if (confirm('Remove featured status from this listing?')) {
        const formData = new FormData();
        formData.append('action', 'set_featured');
        formData.append('vehicle_id', vehicleId);
        // Don't add featured checkbox to disable it
        
        fetch(window.location.href, {
            method: 'POST',
            body: formData
        })
        .then(response => response.text())
        .then(() => {
            location.reload();
        })
        .catch(error => {
            alert('Error removing featured: ' + error.message);
        });
    }
}

// Other Admin Functions
function suspendDealer(dealerId) {
    if (confirm('Are you sure you want to suspend this dealer?')) {
        const formData = new FormData();
        formData.append('action', 'suspend_dealer');
        formData.append('dealer_id', dealerId);
        
        fetch(window.location.href, {
            method: 'POST',
            body: formData
        })
        .then(response => response.text())
        .then(() => {
            location.reload();
        })
        .catch(error => {
            alert('Error suspending dealer: ' + error.message);
        });
    }
}

function approveListing(vehicleId) {
    if (confirm('Approve this listing?')) {
        const formData = new FormData();
        formData.append('action', 'approve_listing');
        formData.append('vehicle_id', vehicleId);
        
        fetch(window.location.href, {
            method: 'POST',
            body: formData
        })
        .then(response => response.text())
        .then(() => {
            location.reload();
        })
        .catch(error => {
            alert('Error approving listing: ' + error.message);
        });
    }
}

function editPremium(vehicleId) {
    const newDate = prompt('Enter new premium expiry date (YYYY-MM-DD) or leave empty for unlimited:');
    if (newDate !== null) {
        const formData = new FormData();
        formData.append('action', 'edit_premium');
        formData.append('vehicle_id', vehicleId);
        formData.append('premium_until', newDate);
        
        fetch(window.location.href, {
            method: 'POST',
            body: formData
        })
        .then(response => response.text())
        .then(() => {
            location.reload();
        })
        .catch(error => {
            alert('Error updating premium: ' + error.message);
        });
    }
}

function removePremium(vehicleId) {
    if (confirm('Remove premium status from this listing?')) {
        const formData = new FormData();
        formData.append('action', 'remove_premium');
        formData.append('vehicle_id', vehicleId);
        
        fetch(window.location.href, {
            method: 'POST',
            body: formData
        })
        .then(response => response.text())
        .then(() => {
            location.reload();
        })
        .catch(error => {
            alert('Error removing premium: ' + error.message);
        });
    }
}

// Global character counter function
function updateCharCounter(textarea) {
    if (window.vehicleForm) {
        window.vehicleForm.updateCharCounter(textarea);
    }
}

// Initialize the system when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.vehicleForm = new VehicleFormManager();
    
    // Add visual feedback for better UX
    const selectElements = document.querySelectorAll('select');
    selectElements.forEach(select => {
        select.addEventListener('focus', function() {
            this.parentElement.classList.add('select-focused');
        });
        
        select.addEventListener('blur', function() {
            this.parentElement.classList.remove('select-focused');
        });
    });
    
    // Animate elements on page load
    const fadeElements = document.querySelectorAll('.fade-in');
    fadeElements.forEach((element, index) => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            element.style.transition = 'all 0.6s ease';
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }, index * 100);
    });

    const slideElements = document.querySelectorAll('.slide-in-left');
    slideElements.forEach((element, index) => {
        element.style.opacity = '0';
        element.style.transform = 'translateX(-30px)';
        
        setTimeout(() => {
            element.style.transition = 'all 0.6s ease';
            element.style.opacity = '1';
            element.style.transform = 'translateX(0)';
        }, index * 150);
    });
    
    console.log(' TrucksONSale Professional Enhanced System Ready with Featured Listings!');
});

// Add ripple effect to buttons
document.addEventListener('DOMContentLoaded', function() {
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            const ripple = document.createElement('div');
            ripple.className = 'ripple';
            this.appendChild(ripple);
            
            setTimeout(() => {
                if (ripple.parentNode) {
                    ripple.remove();
                }
            }, 600);
        });
    });
});
</script>

<style>
/* Additional Professional Enhancements */
.select-focused {
    transform: translateY(-2px);
    transition: all 0.3s ease;
}

.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255,255,255,0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

.team-member-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: var(--shadow-light);
}

.team-member-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.contact-info .btn, .social-links .btn {
    margin: 0.25rem;
}

/* Table enhancements */
.table-hover tbody tr:hover {
    background-color: var(--primary-navy-light);
    transform: translateX(5px);
    transition: all 0.3s ease;
}

.sticky-top {
    background: var(--primary-navy) !important;
    color: white !important;
}

/* Badge enhancements */
.badge {
    font-weight: 600;
    letter-spacing: 0.5px;
}

/* Featured specific styles */
.featured-toggle {
    border-color: #ff6b6b !important;
}

.featured-toggle:hover {
    background: #fff5f5 !important;
}
</style>

</body>
</html>

<?php
// Clean output buffer and flush
ob_end_flush();
?>