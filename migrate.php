<?php
// =============================================
// DATABASE MIGRATION SCRIPT FOR TRUCKSONSALE
// This script will add all missing columns to bring your database up to date
// =============================================

// Database Configuration - UPDATE THESE WITH YOUR ACTUAL VALUES
$host = 'localhost';
$db_name = 'truc_tos';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$db_name;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>TrucksONSale Database Migration</h2>\n";
    echo "<p>Checking and adding missing columns...</p>\n";
    
    // Function to check if column exists
    function columnExists($pdo, $table, $column) {
        $stmt = $pdo->prepare("SHOW COLUMNS FROM `$table` LIKE ?");
        $stmt->execute([$column]);
        return $stmt->fetch() !== false;
    }
    
    // Function to safely add column
    function addColumn($pdo, $table, $column, $definition) {
        try {
            if (!columnExists($pdo, $table, $column)) {
                $sql = "ALTER TABLE `$table` ADD COLUMN `$column` $definition";
                $pdo->exec($sql);
                echo "✅ Added column '$column' to '$table' table<br>\n";
                return true;
            } else {
                echo "ℹ️ Column '$column' already exists in '$table' table<br>\n";
                return false;
            }
        } catch (PDOException $e) {
            echo "❌ Error adding column '$column' to '$table': " . $e->getMessage() . "<br>\n";
            return false;
        }
    }
    
    // Check and add missing columns to vehicles table
    echo "<h3>Updating vehicles table...</h3>\n";
    
    $vehicleColumns = [
        'listing_type' => "ENUM('sale', 'rent-to-own', 'hire', 'auction') DEFAULT 'sale'",
        'condition_type' => "ENUM('new', 'used', 'refurbished') DEFAULT 'used'",
        'variant' => "VARCHAR(100)",
        'hours_used' => "INT DEFAULT 0",
        'engine_capacity' => "VARCHAR(20)",
        'horsepower' => "INT",
        'vin_number' => "VARCHAR(50)",
        'registration_number' => "VARCHAR(20)",
        'branch_id' => "INT NULL",
        'condition_rating' => "ENUM('excellent', 'very_good', 'good', 'fair', 'poor') DEFAULT 'good'",
        'no_accidents' => "BOOLEAN DEFAULT FALSE",
        'warranty' => "BOOLEAN DEFAULT FALSE",
        'warranty_details' => "TEXT",
        'finance_available' => "BOOLEAN DEFAULT FALSE",
        'trade_in' => "BOOLEAN DEFAULT FALSE",
        'service_history' => "BOOLEAN DEFAULT FALSE",
        'roadworthy' => "BOOLEAN DEFAULT FALSE",
        'youtube_video_url' => "VARCHAR(255)",
        'daily_rate' => "DECIMAL(8,2) NULL",
        'weekly_rate' => "DECIMAL(10,2) NULL",
        'monthly_rate' => "DECIMAL(12,2) NULL",
        'auction_start_date' => "DATETIME NULL",
        'auction_end_date' => "DATETIME NULL",
        'reserve_price' => "DECIMAL(12,2) NULL",
        'current_bid' => "DECIMAL(12,2) NULL",
        'featured' => "BOOLEAN DEFAULT FALSE",
        'featured_until' => "DATE NULL",
        'premium_listing' => "BOOLEAN DEFAULT FALSE",
        'premium_until' => "DATE NULL",
        'views' => "INT DEFAULT 0",
        'leads_count' => "INT DEFAULT 0",
        'approved_at' => "TIMESTAMP NULL"
    ];
    
    foreach ($vehicleColumns as $column => $definition) {
        addColumn($pdo, 'vehicles', $column, $definition);
    }
    
    // Check and add missing columns to users table
    echo "<h3>Updating users table...</h3>\n";
    
    $userColumns = [
        'company_registration' => "VARCHAR(50)",
        'vat_number' => "VARCHAR(20)",
        'physical_address' => "TEXT",
        'billing_address' => "TEXT",
        'profile_image' => "VARCHAR(255)",
        'email_verified' => "BOOLEAN DEFAULT FALSE",
        'phone_verified' => "BOOLEAN DEFAULT FALSE",
        'password_reset_token' => "VARCHAR(255) NULL",
        'password_reset_expires' => "TIMESTAMP NULL",
        'listing_limit' => "INT DEFAULT 10",
        'premium_until' => "DATE NULL",
        'failed_login_attempts' => "INT DEFAULT 0",
        'locked_until' => "TIMESTAMP NULL"
    ];
    
    foreach ($userColumns as $column => $definition) {
        addColumn($pdo, 'users', $column, $definition);
    }
    
    // Create missing tables if they don't exist
    echo "<h3>Creating missing tables...</h3>\n";
    
    // Categories table
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'categories'");
    $stmt->execute();
    if (!$stmt->fetch()) {
        $pdo->exec("CREATE TABLE categories (
            category_id INT AUTO_INCREMENT PRIMARY KEY,
            category_key VARCHAR(50) UNIQUE NOT NULL,
            category_name VARCHAR(100) NOT NULL,
            parent_category VARCHAR(50) NULL,
            listing_type ENUM('sale', 'rent-to-own', 'hire', 'auction') DEFAULT 'sale',
            icon VARCHAR(100) NOT NULL DEFAULT 'fas fa-tag',
            listing_label VARCHAR(100) NOT NULL,
            mileage_label VARCHAR(50) DEFAULT 'Mileage (KM)',
            engine_label VARCHAR(50) DEFAULT 'Engine Type',
            show_transmission BOOLEAN DEFAULT TRUE,
            show_fuel_type BOOLEAN DEFAULT TRUE,
            show_year BOOLEAN DEFAULT TRUE,
            show_hours BOOLEAN DEFAULT FALSE,
            transmission_options TEXT,
            fuel_options TEXT,
            additional_fields TEXT,
            category_order INT DEFAULT 0,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        echo "✅ Created categories table<br>\n";
    }
    
    // Subcategories table
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'subcategories'");
    $stmt->execute();
    if (!$stmt->fetch()) {
        $pdo->exec("CREATE TABLE subcategories (
            subcategory_id INT AUTO_INCREMENT PRIMARY KEY,
            category_id INT NOT NULL,
            subcategory_key VARCHAR(50) NOT NULL,
            subcategory_name VARCHAR(100) NOT NULL,
            subcategory_order INT DEFAULT 0,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (category_id) REFERENCES categories(category_id) ON DELETE CASCADE,
            UNIQUE KEY unique_subcategory (category_id, subcategory_key)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        echo "✅ Created subcategories table<br>\n";
    }
    
    // Category makes table
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'category_makes'");
    $stmt->execute();
    if (!$stmt->fetch()) {
        $pdo->exec("CREATE TABLE category_makes (
            make_id INT AUTO_INCREMENT PRIMARY KEY,
            category_id INT NOT NULL,
            make_name VARCHAR(100) NOT NULL,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (category_id) REFERENCES categories(category_id) ON DELETE CASCADE,
            UNIQUE KEY unique_make (category_id, make_name)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        echo "✅ Created category_makes table<br>\n";
    }
    
    // Category models table
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'category_models'");
    $stmt->execute();
    if (!$stmt->fetch()) {
        $pdo->exec("CREATE TABLE category_models (
            model_id INT AUTO_INCREMENT PRIMARY KEY,
            make_id INT NOT NULL,
            model_name VARCHAR(100) NOT NULL,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (make_id) REFERENCES category_makes(make_id) ON DELETE CASCADE,
            UNIQUE KEY unique_model (make_id, model_name)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        echo "✅ Created category_models table<br>\n";
    }
    
    // Category variants table
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'category_variants'");
    $stmt->execute();
    if (!$stmt->fetch()) {
        $pdo->exec("CREATE TABLE category_variants (
            variant_id INT AUTO_INCREMENT PRIMARY KEY,
            model_id INT NOT NULL,
            variant_name VARCHAR(100) NOT NULL,
            variant_description TEXT,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (model_id) REFERENCES category_models(model_id) ON DELETE CASCADE,
            UNIQUE KEY unique_variant (model_id, variant_name)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        echo "✅ Created category_variants table<br>\n";
    }
    
    // System years table
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'system_years'");
    $stmt->execute();
    if (!$stmt->fetch()) {
        $pdo->exec("CREATE TABLE system_years (
            year_id INT AUTO_INCREMENT PRIMARY KEY,
            year_value INT NOT NULL UNIQUE,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        echo "✅ Created system_years table<br>\n";
        
        // Insert years
        $current_year = date('Y');
        for ($year = 1970; $year <= $current_year + 1; $year++) {
            $pdo->exec("INSERT IGNORE INTO system_years (year_value) VALUES ($year)");
        }
        echo "✅ Inserted system years<br>\n";
    }
    
    // Dealer sales team table
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'dealer_sales_team'");
    $stmt->execute();
    if (!$stmt->fetch()) {
        $pdo->exec("CREATE TABLE dealer_sales_team (
            team_id INT AUTO_INCREMENT PRIMARY KEY,
            dealer_id INT NOT NULL,
            name VARCHAR(100) NOT NULL,
            position VARCHAR(100),
            phone VARCHAR(15),
            email VARCHAR(100),
            whatsapp VARCHAR(15),
            photo VARCHAR(255),
            facebook_url VARCHAR(255),
            twitter_url VARCHAR(255),
            instagram_url VARCHAR(255),
            linkedin_url VARCHAR(255),
            youtube_url VARCHAR(255),
            tiktok_url VARCHAR(255),
            website_url VARCHAR(255),
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (dealer_id) REFERENCES users(user_id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        echo "✅ Created dealer_sales_team table<br>\n";
    }
    
    // Dealer branches table
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'dealer_branches'");
    $stmt->execute();
    if (!$stmt->fetch()) {
        $pdo->exec("CREATE TABLE dealer_branches (
            branch_id INT AUTO_INCREMENT PRIMARY KEY,
            dealer_id INT NOT NULL,
            branch_name VARCHAR(100) NOT NULL,
            address TEXT NOT NULL,
            city VARCHAR(50) NOT NULL,
            region VARCHAR(50) NOT NULL,
            postal_code VARCHAR(10),
            phone VARCHAR(15),
            email VARCHAR(100),
            is_main_branch BOOLEAN DEFAULT FALSE,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (dealer_id) REFERENCES users(user_id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        echo "✅ Created dealer_branches table<br>\n";
    }
    
    // Vehicle images table
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'vehicle_images'");
    $stmt->execute();
    if (!$stmt->fetch()) {
        $pdo->exec("CREATE TABLE vehicle_images (
            image_id INT AUTO_INCREMENT PRIMARY KEY,
            vehicle_id INT NOT NULL,
            image_path VARCHAR(255) NOT NULL,
            image_name VARCHAR(255),
            file_size INT,
            image_order INT DEFAULT 0,
            is_primary BOOLEAN DEFAULT FALSE,
            uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (vehicle_id) REFERENCES vehicles(vehicle_id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        echo "✅ Created vehicle_images table<br>\n";
    }
    
    // Vehicle videos table
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'vehicle_videos'");
    $stmt->execute();
    if (!$stmt->fetch()) {
        $pdo->exec("CREATE TABLE vehicle_videos (
            video_id INT AUTO_INCREMENT PRIMARY KEY,
            vehicle_id INT NOT NULL,
            video_path VARCHAR(255) NOT NULL,
            video_title VARCHAR(100),
            file_size BIGINT,
            duration_seconds INT,
            uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (vehicle_id) REFERENCES vehicles(vehicle_id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        echo "✅ Created vehicle_videos table<br>\n";
    }
    
    // Vehicle documents table
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'vehicle_documents'");
    $stmt->execute();
    if (!$stmt->fetch()) {
        $pdo->exec("CREATE TABLE vehicle_documents (
            document_id INT AUTO_INCREMENT PRIMARY KEY,
            vehicle_id INT NOT NULL,
            document_path VARCHAR(255) NOT NULL,
            document_name VARCHAR(100) NOT NULL,
            document_type ENUM('pdf') NOT NULL DEFAULT 'pdf',
            file_size INT,
            uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (vehicle_id) REFERENCES vehicles(vehicle_id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        echo "✅ Created vehicle_documents table<br>\n";
    }
    
    // Auction bids table
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'auction_bids'");
    $stmt->execute();
    if (!$stmt->fetch()) {
        $pdo->exec("CREATE TABLE auction_bids (
            bid_id INT AUTO_INCREMENT PRIMARY KEY,
            vehicle_id INT NOT NULL,
            bidder_name VARCHAR(100) NOT NULL,
            bidder_email VARCHAR(100) NOT NULL,
            bidder_phone VARCHAR(15) NOT NULL,
            bid_amount DECIMAL(12,2) NOT NULL,
            bid_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status ENUM('active', 'outbid', 'winning', 'withdrawn') DEFAULT 'active',
            FOREIGN KEY (vehicle_id) REFERENCES vehicles(vehicle_id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        echo "✅ Created auction_bids table<br>\n";
    }
    
    // Hire bookings table
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'hire_bookings'");
    $stmt->execute();
    if (!$stmt->fetch()) {
        $pdo->exec("CREATE TABLE hire_bookings (
            booking_id INT AUTO_INCREMENT PRIMARY KEY,
            vehicle_id INT NOT NULL,
            customer_name VARCHAR(100) NOT NULL,
            email VARCHAR(100) NOT NULL,
            phone VARCHAR(15) NOT NULL,
            start_date DATE NOT NULL,
            end_date DATE NOT NULL,
            pickup_location VARCHAR(255),
            daily_rate DECIMAL(8,2),
            total_cost DECIMAL(12,2),
            status ENUM('pending', 'confirmed', 'active', 'completed', 'cancelled') DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (vehicle_id) REFERENCES vehicles(vehicle_id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        echo "✅ Created hire_bookings table<br>\n";
    }
    
    // Enhanced inquiries table (check if it exists and add missing columns)
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'inquiries'");
    $stmt->execute();
    if ($stmt->fetch()) {
        // Table exists, check for missing columns
        echo "<h3>Updating inquiries table...</h3>\n";
        $inquiryColumns = [
            'inquiry_type' => "ENUM('general', 'finance', 'trade_in', 'inspection', 'test_drive') DEFAULT 'general'",
            'preferred_contact' => "ENUM('phone', 'email', 'whatsapp') DEFAULT 'phone'",
            'priority' => "ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium'",
            'follow_up_date' => "DATE NULL",
            'notes' => "TEXT",
            'responded_at' => "TIMESTAMP NULL"
        ];
        
        foreach ($inquiryColumns as $column => $definition) {
            addColumn($pdo, 'inquiries', $column, $definition);
        }
    }
    
    // Insert sample categories if none exist
    $stmt = $pdo->query("SELECT COUNT(*) FROM categories");
    $category_count = $stmt->fetchColumn();
    
    if ($category_count == 0) {
        echo "<h3>Creating sample categories...</h3>\n";
        
        $base_categories = [
            ['trucks', 'Trucks', 'fas fa-truck', 'List Your Truck'],
            ['trailers', 'Trailers', 'fas fa-trailer', 'List Your Trailer'],
            ['buses', 'Buses', 'fas fa-bus', 'List Your Bus'],
            ['commercial_vehicles', 'Commercial Vehicles', 'fas fa-van-shuttle', 'List Your Commercial Vehicle'],
            ['farm_equipment', 'Farm Equipment', 'fas fa-tractor', 'List Your Farm Equipment'],
            ['heavy_machinery', 'Heavy Machinery', 'fas fa-cogs', 'List Your Heavy Machinery']
        ];
        
        $listing_types = ['sale', 'rent-to-own', 'hire', 'auction'];
        $order = 1;
        
        foreach ($listing_types as $listing_type) {
            foreach ($base_categories as $category) {
                $prefix = ($listing_type === 'sale') ? '' : $listing_type . '_';
                $suffix = ($listing_type === 'sale') ? '' : ' (' . ucwords(str_replace('-', ' ', $listing_type)) . ')';
                
                $stmt = $pdo->prepare("INSERT INTO categories (category_key, category_name, listing_type, icon, listing_label, category_order, show_hours) VALUES (?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute([
                    $prefix . $category[0],
                    $category[1] . $suffix,
                    $listing_type,
                    $category[2],
                    str_replace('List Your', ($listing_type === 'sale' ? 'List Your' : ucwords(str_replace('-', ' ', $listing_type)) . ':'), $category[3]),
                    $order,
                    in_array($category[0], ['heavy_machinery', 'farm_equipment']) ? 1 : 0
                ]);
                $order++;
            }
        }
        echo "✅ Created sample categories<br>\n";
        
        // Insert sample makes for trucks
        $sample_makes = [
            'Mercedes Benz' => ['Actros', 'Atego', 'Axor'],
            'Volvo' => ['FH', 'FM', 'FE'],
            'Scania' => ['R Series', 'S Series', 'P Series'],
            'MAN' => ['TGX', 'TGS', 'TGM'],
            'Isuzu' => ['NPR', 'NQR', 'FTR']
        ];
        
        foreach ($sample_makes as $make_name => $models) {
            $stmt = $pdo->prepare("INSERT INTO category_makes (category_id, make_name) VALUES (1, ?)");
            $stmt->execute([$make_name]);
            $make_id = $pdo->lastInsertId();
            
            foreach ($models as $model_name) {
                $stmt = $pdo->prepare("INSERT INTO category_models (make_id, model_name) VALUES (?, ?)");
                $stmt->execute([$make_id, $model_name]);
            }
        }
        echo "✅ Created sample makes and models<br>\n";
    }
    
    // Add indexes for better performance
    echo "<h3>Adding database indexes...</h3>\n";
    
    try {
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_listing_type ON vehicles(listing_type)");
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_premium ON vehicles(premium_listing)");
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_status ON vehicles(status)");
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_user_type ON users(user_type)");
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_user_status ON users(status)");
        echo "✅ Added database indexes<br>\n";
    } catch (PDOException $e) {
        echo "ℹ️ Some indexes may already exist<br>\n";
    }
    
    echo "<h3>✅ Migration completed successfully!</h3>\n";
    echo "<p><strong>Your database has been updated with all the latest enhancements.</strong></p>\n";
    echo "<p>You can now safely go back to your admin dashboard.</p>\n";
    echo "<p><a href='listing.php?action=admin_dashboard' class='btn btn-primary'>Go to Admin Dashboard</a></p>\n";
    
} catch(PDOException $e) {
    echo "<h3>❌ Migration failed!</h3>\n";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>\n";
    echo "<p>Please check your database credentials and try again.</p>\n";
}
?>