<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

try {
    // Database connection
    $db = new mysqli('localhost', 'root', '', 'truc_tos');
    
    if ($db->connect_error) {
        throw new Exception("Database connection failed: " . $db->connect_error);
    }
    
    $db->set_charset("utf8mb4");

    // Handle GET request for team members (dealer_sales_team)
    if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['dealer_sales_team'])) {
        $stmt = $db->query("
            SELECT 
                name, 
                position, 
                phone, 
                email, 
                whatsapp, 
                photo, 
                facebook_url, 
                twitter_url, 
                instagram_url, 
                linkedin_url, 
                youtube_url, 
                tiktok_url, 
                website_url, 
                status 
            FROM dealer_sales_team 
            WHERE status = 'active'
            ORDER BY name ASC
        ");
        
        $teamMembers = $stmt->fetch_all(MYSQLI_ASSOC);
        
        // Process photo URLs
        foreach ($teamMembers as &$member) {
            if (!empty($member['photo']) && strpos($member['photo'], 'http') !== 0) {
                $member['photo'] = 'https://trucksonsale.co.za/' . ltrim($member['photo'], '/');
            }
        }
        
        echo json_encode([
            'success' => true,
            'team_members' => $teamMembers,
            'count' => count($teamMembers),
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_PRETTY_PRINT);
        exit;
    }

    // Handle GET request for single vehicle with all images
    if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['vehicle_id'])) {
        $vehicle_id = intval($_GET['vehicle_id']);
        
        // Get vehicle details
        $stmt = $db->prepare("SELECT 
                    v.vehicle_id,
                    v.make,
                    v.model,
                    v.year,
                    v.price,
                    v.mileage,
                    v.engine_type AS engine,
                    v.transmission,
                    v.fuel_type AS fuelType,
                    CONCAT(v.region, ', ', v.city) AS location,
                    v.dealer_id,
                    v.category,
                    v.subcategory,
                    v.color,
                    v.no_accidents,
                    v.warranty,
                    v.finance_available,
                    v.trade_in,
                    v.service_history,
                    v.roadworthy,
                    v.description,
                    v.variant,
                    v.hours_used,
                    v.engine_capacity,
                    v.horsepower,
                    v.vin_number,
                    v.registration_number,
                    v.condition_rating,
                    v.warranty_details,
                    v.youtube_video_url,
                    v.listing_type,
                    v.condition_type,
                    v.daily_rate,
                    v.weekly_rate,
                    v.monthly_rate,
                    v.status,
                    v.created_at
                FROM vehicles v
                WHERE v.vehicle_id = ? AND v.dealer_id = 3");
        $stmt->bind_param("i", $vehicle_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 0) {
            throw new Exception("Vehicle not found");
        }
        
        $vehicle = $result->fetch_assoc();
        
        // Get ALL images for this vehicle
        $stmt = $db->prepare("SELECT 
                    image_id, 
                    image_path AS image_url, 
                    is_primary 
                FROM vehicle_images 
                WHERE vehicle_id = ? 
                ORDER BY is_primary DESC, image_id ASC");
        $stmt->bind_param("i", $vehicle_id);
        $stmt->execute();
        $imagesResult = $stmt->get_result();
        $allImages = $imagesResult->fetch_all(MYSQLI_ASSOC);
        
        // Process all image URLs
        foreach ($allImages as &$image) {
            if (!empty($image['image_url']) && strpos($image['image_url'], 'http') !== 0) {
                $image['image_url'] = 'https://trucksonsale.co.za/' . ltrim($image['image_url'], '/');
            }
        }
        
        $vehicle['all_images'] = $allImages;
        
        // Also include primary image separately for backward compatibility
        $primaryImage = array_filter($allImages, function($img) { return $img['is_primary'] == 1; });
        if (!empty($primaryImage)) {
            $primaryImage = reset($primaryImage);
            $vehicle['image_url'] = $primaryImage['image_url'];
        }
        
        if (isset($vehicle['price'])) {
            $vehicle['price'] = number_format(floatval($vehicle['price']), 2);
        }
        
        echo json_encode([
            'success' => true,
            'vehicle' => $vehicle,
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_PRETTY_PRINT);
        exit;
    }

    // Handle POST requests
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
        $action = $_POST['action'];
        
        if ($action === 'check_availability') {
            // Check vehicle availability
            if (empty($_POST['vehicle_id'])) {
                throw new Exception("Vehicle ID is required");
            }
            
            $vehicle_id = intval($_POST['vehicle_id']);
            $stmt = $db->prepare("SELECT status FROM vehicles WHERE vehicle_id = ? AND dealer_id = 3");
            $stmt->bind_param("i", $vehicle_id);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows === 0) {
                echo json_encode(['available' => false]);
            } else {
                $row = $result->fetch_assoc();
                echo json_encode(['available' => $row['status'] === 'available']);
            }
            exit;
            
        } elseif ($action === 'submit_inquiry') {
            // Validate required fields
            $required = ['vehicle_id', 'full_name', 'email', 'phone', 'message'];
            foreach ($required as $field) {
                if (empty($_POST[$field])) {
                    throw new Exception("Missing required field: $field");
                }
            }
            
            // Sanitize input
            $vehicle_id = intval($_POST['vehicle_id']);
            $full_name = $db->real_escape_string(trim($_POST['full_name']));
            $email = $db->real_escape_string(trim($_POST['email']));
            $phone = $db->real_escape_string(trim($_POST['phone']));
            $message = $db->real_escape_string(trim($_POST['message']));
            $preferred_contact = $db->real_escape_string(trim($_POST['preferred_contact'] ?? 'phone'));
            
            // First verify vehicle is available and belongs to Shacman TNT
            $stmt = $db->prepare("SELECT status FROM vehicles WHERE vehicle_id = ? AND dealer_id = 3");
            $stmt->bind_param("i", $vehicle_id);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows === 0) {
                throw new Exception("Vehicle not found or doesn't belong to Shacman TNT");
            }
            
            $vehicle = $result->fetch_assoc();
            if ($vehicle['status'] !== 'available') {
                throw new Exception("Vehicle is no longer available");
            }
            
            // Insert inquiry - always use dealer_id = 3 for Shacman TNT
            $stmt = $db->prepare("INSERT INTO inquiries (
                vehicle_id, dealer_id, full_name, email, phone, message,
                preferred_contact, status, created_at
            ) VALUES (?, 3, ?, ?, ?, ?, ?, 'new', NOW())");
            
            if (!$stmt) {
                throw new Exception("Prepare failed: " . $db->error);
            }
            
            $stmt->bind_param("isssss", $vehicle_id, $full_name, 
                $email, $phone, $message, $preferred_contact);
                
            if (!$stmt->execute()) {
                throw new Exception("Execute failed: " . $stmt->error);
            }
            
            echo json_encode([
                'success' => true,
                'message' => 'Inquiry submitted successfully',
                'inquiry_id' => $stmt->insert_id,
                'dealer_id' => 3,
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            exit;
        }
    }

    // Fetch all vehicles with all images - only for Shacman TNT (dealer_id = 3)
    function fetchAllVehiclesWithImages($db, $isFeatured = false) {
        // First get all vehicles
        $sql = "SELECT 
                    v.vehicle_id,
                    v.make,
                    v.model,
                    v.year,
                    v.price,
                    v.mileage,
                    v.engine_type AS engine,
                    v.transmission,
                    v.fuel_type AS fuelType,
                    CONCAT(v.region, ', ', v.city) AS location,
                    v.dealer_id,
                    v.category,
                    v.subcategory,
                    v.color,
                    v.no_accidents,
                    v.warranty,
                    v.finance_available,
                    v.trade_in,
                    v.service_history,
                    v.roadworthy,
                    v.description,
                    v.variant,
                    v.hours_used,
                    v.engine_capacity,
                    v.horsepower,
                    v.vin_number,
                    v.registration_number,
                    v.condition_rating,
                    v.warranty_details,
                    v.youtube_video_url,
                    v.listing_type,
                    v.condition_type,
                    v.daily_rate,
                    v.weekly_rate,
                    v.monthly_rate,
                    v.status,
                    v.created_at,
                    (SELECT image_path FROM vehicle_images WHERE vehicle_id = v.vehicle_id AND is_primary = 1 LIMIT 1) AS primary_image_url
                FROM vehicles v
                WHERE v.status = 'available' AND v.dealer_id = 3";
        
        if ($isFeatured) {
            $sql .= " AND v.featured = 1";
        }
        
        $sql .= " ORDER BY v.created_at DESC";
        
        $stmt = $db->prepare($sql);
        if (!$stmt) {
            throw new Exception("Prepare failed: " . $db->error);
        }
        
        if (!$stmt->execute()) {
            throw new Exception("Execute failed: " . $stmt->error);
        }
        
        $result = $stmt->get_result();
        $vehicles = $result->fetch_all(MYSQLI_ASSOC);
        
        // Now get all images for each vehicle
        foreach ($vehicles as &$vehicle) {
            // Process primary image URL
            if (!empty($vehicle['primary_image_url']) && strpos($vehicle['primary_image_url'], 'http') !== 0) {
                $vehicle['image_url'] = 'https://trucksonsale.co.za/' . ltrim($vehicle['primary_image_url'], '/');
            }
            unset($vehicle['primary_image_url']);
            
            if (isset($vehicle['price'])) {
                $vehicle['price'] = number_format(floatval($vehicle['price']), 2);
            }
            
            // Get ALL images for this vehicle
            $imagesStmt = $db->prepare("SELECT 
                        image_id, 
                        image_path AS image_url, 
                        is_primary 
                    FROM vehicle_images 
                    WHERE vehicle_id = ? 
                    ORDER BY is_primary DESC, image_id ASC");
            $imagesStmt->bind_param("i", $vehicle['vehicle_id']);
            $imagesStmt->execute();
            $imagesResult = $imagesStmt->get_result();
            $allImages = $imagesResult->fetch_all(MYSQLI_ASSOC);
            
            // Process all image URLs
            foreach ($allImages as &$image) {
                if (!empty($image['image_url']) && strpos($image['image_url'], 'http') !== 0) {
                    $image['image_url'] = 'https://trucksonsale.co.za/' . ltrim($image['image_url'], '/');
                }
            }
            
            $vehicle['all_images'] = $allImages;
        }
        
        return $vehicles;
    }

    // Default response - get all vehicle listings with all images
    $featured = fetchAllVehiclesWithImages($db, true);
    $recent = fetchAllVehiclesWithImages($db, false);

    echo json_encode([
        'success' => true,
        'featured' => $featured,
        'recent' => $recent,
        'count' => count($recent),
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_PRETTY_PRINT);
    
    $db->close();

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_PRETTY_PRINT);
}
?>