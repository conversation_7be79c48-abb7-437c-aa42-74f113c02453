// =============================================
// VEHICLE API CLIENT - REAL-TIME DATABASE
// =============================================

class VehicleAPI {
    constructor(baseURL = 'api.php') {
        this.baseURL = baseURL;
        this.cache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
    }

    // Generic API call method
    async makeRequest(endpoint, params = {}) {
        try {
            const url = new URL(this.baseURL, window.location.origin);
            url.searchParams.append('action', endpoint);
            
            Object.keys(params).forEach(key => {
                if (params[key] !== '' && params[key] !== null && params[key] !== undefined) {
                    url.searchParams.append(key, params[key]);
                }
            });

            const cacheKey = url.toString();
            const cached = this.cache.get(cacheKey);
            
            if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
                return cached.data;
            }

            const response = await fetch(url);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            
            // Cache successful responses
            if (data.success) {
                this.cache.set(cacheKey, {
                    data: data,
                    timestamp: Date.now()
                });
            }
            
            return data;
            
        } catch (error) {
            console.error('API Request failed:', error);
            return {
                success: false,
                error: error.message,
                data: []
            };
        }
    }

    // Get featured vehicles
    async getFeaturedVehicles() {
        return await this.makeRequest('featured');
    }

    // Get all vehicles with filters
    async getVehicles(filters = {}) {
        return await this.makeRequest('vehicles', filters);
    }

    // Get vehicle details by ID
    async getVehicleDetails(vehicleId) {
        return await this.makeRequest('vehicle', { id: vehicleId });
    }

    // Get all categories with counts
    async getCategories() {
        return await this.makeRequest('categories');
    }

    // Get statistics
    async getStats() {
        return await this.makeRequest('stats');
    }

    // Search vehicles
    async searchVehicles(searchParams) {
        return await this.makeRequest('search', searchParams);
    }

    // Clear cache
    clearCache() {
        this.cache.clear();
    }

    // Get cached data count
    getCacheSize() {
        return this.cache.size;
    }
}

// =============================================
// REAL-TIME VEHICLE DISPLAY FUNCTIONS
// =============================================

// Initialize API client
const vehicleAPI = new VehicleAPI();

// Global variables for real-time data
let currentPage = 1;
let currentFilters = {};
let isLoading = false;

// Replace the mock data functions with real API calls

// Load featured vehicles from database
async function loadFeaturedVehicles() {
    try {
        showLoadingSpinner('featured-vehicles');
        
        const response = await vehicleAPI.getFeaturedVehicles();
        
        if (response.success && response.data) {
            const featuredContainer = document.getElementById('featured-vehicles');
            
            if (response.data.length === 0) {
                featuredContainer.innerHTML = `
                    <div class="col-12">
                        <div class="alert alert-info text-center py-4">
                            <i class="bi bi-info-circle fs-4"></i>
                            <p class="mb-0 mt-2">No featured vehicles available at this time</p>
                        </div>
                    </div>
                `;
                return;
            }
            
            featuredContainer.innerHTML = response.data.map(vehicle => createVehicleCard(vehicle)).join('');
            animateCards();
        } else {
            showError('featured-vehicles', 'Failed to load featured vehicles');
        }
    } catch (error) {
        console.error('Error loading featured vehicles:', error);
        showError('featured-vehicles', 'Failed to load featured vehicles');
    }
}

// Load all vehicles with real-time filtering
async function loadVehicles(page = 1) {
    if (isLoading) return;
    
    try {
        isLoading = true;
        currentPage = page;
        
        // Show sections
        document.getElementById('all-vehicles-section').classList.remove('d-none');
        document.getElementById('filters-section').classList.remove('d-none');
        
        // Scroll to section
        if (page === 1) {
            document.getElementById('all-vehicles-section').scrollIntoView({ behavior: 'smooth' });
        }
        
        showLoadingSpinner('all-vehicles');
        
        const filters = {
            ...currentFilters,
            page: page,
            limit: 12
        };
        
        const response = await vehicleAPI.getVehicles(filters);
        
        if (response.success && response.data) {
            const container = document.getElementById('all-vehicles');
            
            if (response.data.length === 0 && page === 1) {
                container.innerHTML = `
                    <div class="col-12">
                        <div class="alert alert-info text-center py-4">
                            <i class="bi bi-search fs-4"></i>
                            <p class="mb-0 mt-2">No vehicles found matching your criteria</p>
                            <button class="btn btn-outline-primary mt-2" onclick="clearFilters()">Clear Filters</button>
                        </div>
                    </div>
                `;
                document.getElementById('vehicle-count').textContent = '0 vehicles found';
                document.getElementById('pagination').innerHTML = '';
                return;
            }
            
            container.innerHTML = response.data.map(vehicle => createVehicleCard(vehicle)).join('');
            
            // Update count
            const totalCount = response.pagination ? response.pagination.total : response.data.length;
            document.getElementById('vehicle-count').textContent = `${totalCount.toLocaleString()} vehicles found`;
            
            // Generate pagination
            if (response.pagination) {
                generatePagination(response.pagination);
            }
            
            animateCards();
        } else {
            showError('all-vehicles', response.error || 'Failed to load vehicles');
        }
    } catch (error) {
        console.error('Error loading vehicles:', error);
        showError('all-vehicles', 'Failed to load vehicles');
    } finally {
        isLoading = false;
    }
}

// Load categories from database
async function loadCategories() {
    try {
        const response = await vehicleAPI.getCategories();
        
        if (response.success && response.data) {
            const container = document.getElementById('category-grid');
            
            container.innerHTML = response.data.map(category => `
                <a href="#" class="category-card loading-animation" onclick="filterByCategory('${category.key}')">
                    <div class="category-icon">
                        <i class="bi ${category.icon}"></i>
                    </div>
                    <h3 class="category-title">${category.name}</h3>
                    <p class="category-count">${category.count.toLocaleString()} vehicles available</p>
                </a>
            `).join('');
            
            setTimeout(() => {
                document.querySelectorAll('#category-grid .loading-animation').forEach((el, index) => {
                    setTimeout(() => {
                        el.style.opacity = '1';
                        el.style.transform = 'translateY(0)';
                    }, index * 100);
                });
            }, 100);
        }
    } catch (error) {
        console.error('Error loading categories:', error);
    }
}

// Update vehicle count in hero section
async function updateVehicleCount() {
    try {
        const response = await vehicleAPI.getStats();
        
        if (response.success && response.data) {
            const stats = response.data;
            
            // Animate counter
            animateCounter(document.getElementById('total-vehicles'), stats.total_vehicles);
            
            // Update other stats if elements exist
            const dealerElement = document.getElementById('total-dealers');
            const customerElement = document.getElementById('total-customers');
            
            if (dealerElement) animateCounter(dealerElement, stats.total_dealers);
            if (customerElement) animateCounter(customerElement, stats.total_customers);
        }
    } catch (error) {
        console.error('Error updating vehicle count:', error);
    }
}

// Real-time search function
async function searchVehicles() {
    currentFilters = {
        category: document.getElementById('search-category').value,
        region: document.getElementById('search-region').value,
        search: document.getElementById('search-query').value
    };
    
    await loadVehicles(1);
}

// Apply advanced filters
async function applyFilters() {
    currentFilters = {
        ...currentFilters,
        min_price: document.getElementById('min-price').value || null,
        max_price: document.getElementById('max-price').value || null,
        make: document.getElementById('filter-make').value,
        sort: document.getElementById('sort-by').value
    };
    
    await loadVehicles(1);
}

// Clear all filters
async function clearFilters() {
    currentFilters = {};
    
    // Clear form fields
    const fields = ['search-category', 'search-region', 'search-query', 'min-price', 'max-price', 'filter-make'];
    fields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) field.value = '';
    });
    
    const sortField = document.getElementById('sort-by');
    if (sortField) sortField.value = 'newest';
    
    await loadVehicles(1);
}

// Filter by category
async function filterByCategory(category) {
    currentFilters = { category: category };
    document.getElementById('search-category').value = category;
    await loadVehicles(1);
}

// View vehicle details with real-time data
async function viewVehicleDetails(vehicleId) {
    try {
        // Show loading in modal
        const modal = new bootstrap.Modal(document.getElementById('vehicleModal'));
        document.getElementById('vehicleModalTitle').textContent = 'Loading...';
        document.getElementById('vehicleModalBody').innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-danger" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Loading vehicle details...</p>
            </div>
        `;
        modal.show();
        
        const response = await vehicleAPI.getVehicleDetails(vehicleId);
        
        if (response.success && response.data) {
            const { vehicle, images, videos } = response.data;
            
            document.getElementById('vehicleModalTitle').textContent = `${vehicle.make} ${vehicle.model} (${vehicle.year})`;
            
            // Create image carousel if multiple images
            let imageHTML = '';
            if (images && images.length > 0) {
                if (images.length === 1) {
                    imageHTML = `<img src="${images[0].image_path}" class="img-fluid rounded mb-3" alt="${vehicle.make} ${vehicle.model}">`;
                } else {
                    imageHTML = `
                        <div id="vehicleCarousel" class="carousel slide mb-3" data-bs-ride="carousel">
                            <div class="carousel-inner rounded">
                                ${images.map((image, index) => `
                                    <div class="carousel-item ${index === 0 ? 'active' : ''}">
                                        <img src="${image.image_path}" class="d-block w-100" style="height: 300px; object-fit: cover;" alt="Vehicle Image">
                                    </div>
                                `).join('')}
                            </div>
                            ${images.length > 1 ? `
                                <button class="carousel-control-prev" type="button" data-bs-target="#vehicleCarousel" data-bs-slide="prev">
                                    <span class="carousel-control-prev-icon"></span>
                                </button>
                                <button class="carousel-control-next" type="button" data-bs-target="#vehicleCarousel" data-bs-slide="next">
                                    <span class="carousel-control-next-icon"></span>
                                </button>
                            ` : ''}
                        </div>
                    `;
                }
            } else {
                imageHTML = `
                    <div class="bg-light d-flex align-items-center justify-content-center rounded mb-3" style="height: 300px;">
                        <i class="bi bi-image text-muted" style="font-size: 4rem;"></i>
                    </div>
                `;
            }
            
            document.getElementById('vehicleModalBody').innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        ${imageHTML}
                        ${videos && videos.length > 0 ? `
                            <div class="mb-3">
                                <h6><i class="bi bi-play-circle me-2"></i>Videos Available</h6>
                                <p class="text-muted small">${videos.length} video(s) available</p>
                            </div>
                        ` : ''}
                    </div>
                    <div class="col-md-6">
                        <h4 class="text-danger mb-3">${vehicle.formatted_price}</h4>
                        <table class="table table-borderless">
                            <tr><td><strong>Make:</strong></td><td>${vehicle.make}</td></tr>
                            <tr><td><strong>Model:</strong></td><td>${vehicle.model}</td></tr>
                            <tr><td><strong>Year:</strong></td><td>${vehicle.year}</td></tr>
                            <tr><td><strong>Mileage:</strong></td><td>${vehicle.formatted_mileage}</td></tr>
                            <tr><td><strong>Fuel Type:</strong></td><td>${vehicle.fuel_type}</td></tr>
                            <tr><td><strong>Transmission:</strong></td><td>${vehicle.transmission}</td></tr>
                            ${vehicle.color ? `<tr><td><strong>Color:</strong></td><td>${vehicle.color}</td></tr>` : ''}
                            ${vehicle.engine_type ? `<tr><td><strong>Engine:</strong></td><td>${vehicle.engine_type}</td></tr>` : ''}
                            <tr><td><strong>Location:</strong></td><td>${vehicle.city}, ${vehicle.region}</td></tr>
                            <tr><td><strong>Dealer:</strong></td><td>${vehicle.company_name}</td></tr>
                        </table>
                        ${vehicle.description ? `
                            <div class="mt-3">
                                <h6>Description:</h6>
                                <p class="text-muted">${vehicle.description}</p>
                            </div>
                        ` : ''}
                        <div class="mt-4">
                            ${vehicle.dealer_phone ? `
                                <a href="tel:${vehicle.dealer_phone}" class="btn btn-danger me-2">
                                    <i class="bi bi-telephone me-2"></i>Call Dealer
                                </a>
                            ` : ''}
                            ${vehicle.dealer_email ? `
                                <a href="mailto:${vehicle.dealer_email}" class="btn btn-outline-danger">
                                    <i class="bi bi-envelope me-2"></i>Email Dealer
                                </a>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `;
        } else {
            document.getElementById('vehicleModalBody').innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    Failed to load vehicle details: ${response.error || 'Unknown error'}
                </div>
            `;
        }
    } catch (error) {
        console.error('Error loading vehicle details:', error);
        document.getElementById('vehicleModalBody').innerHTML = `
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle me-2"></i>
                Failed to load vehicle details. Please try again.
            </div>
        `;
    }
}

// =============================================
// UTILITY FUNCTIONS
// =============================================

// Show loading spinner
function showLoadingSpinner(containerId) {
    const container = document.getElementById(containerId);
    container.innerHTML = `
        <div class="col-12">
            <div class="text-center py-5">
                <div class="spinner-border text-danger" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-3 text-muted">Loading vehicles...</p>
            </div>
        </div>
    `;
}

// Show error message
function showError(containerId, message) {
    const container = document.getElementById(containerId);
    container.innerHTML = `
        <div class="col-12">
            <div class="alert alert-danger text-center py-4">
                <i class="bi bi-exclamation-triangle fs-4"></i>
                <p class="mb-2 mt-2">${message}</p>
                <button class="btn btn-outline-danger btn-sm" onclick="location.reload()">
                    <i class="bi bi-arrow-clockwise me-2"></i>Retry
                </button>
            </div>
        </div>
    `;
}

// Generate pagination with real data
function generatePagination(paginationData) {
    const { page, pages, total } = paginationData;
    const pagination = document.getElementById('pagination');
    
    if (pages <= 1) {
        pagination.innerHTML = '';
        return;
    }
    
    let paginationHTML = '';
    
    // Previous button
    if (page > 1) {
        paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="loadVehicles(${page - 1})">Previous</a></li>`;
    }
    
    // Page numbers with smart truncation
    const maxVisible = 5;
    let startPage = Math.max(1, page - Math.floor(maxVisible / 2));
    let endPage = Math.min(pages, startPage + maxVisible - 1);
    
    if (endPage - startPage + 1 < maxVisible) {
        startPage = Math.max(1, endPage - maxVisible + 1);
    }
    
    // First page
    if (startPage > 1) {
        paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="loadVehicles(1)">1</a></li>`;
        if (startPage > 2) {
            paginationHTML += `<li class="page-item disabled"><a class="page-link" href="#">...</a></li>`;
        }
    }
    
    // Page numbers
    for (let i = startPage; i <= endPage; i++) {
        if (i === page) {
            paginationHTML += `<li class="page-item active"><a class="page-link" href="#">${i}</a></li>`;
        } else {
            paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="loadVehicles(${i})">${i}</a></li>`;
        }
    }
    
    // Last page
    if (endPage < pages) {
        if (endPage < pages - 1) {
            paginationHTML += `<li class="page-item disabled"><a class="page-link" href="#">...</a></li>`;
        }
        paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="loadVehicles(${pages})">${pages}</a></li>`;
    }
    
    // Next button
    if (page < pages) {
        paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="loadVehicles(${page + 1})">Next</a></li>`;
    }
    
    pagination.innerHTML = paginationHTML;
}

// Animate counter numbers
function animateCounter(element, target) {
    if (!element) return;
    
    let count = 0;
    const increment = Math.ceil(target / 100);
    const timer = setInterval(() => {
        count += increment;
        if (count >= target) {
            count = target;
            clearInterval(timer);
        }
        element.textContent = count.toLocaleString();
    }, 20);
}

// Animate cards appearance
function animateCards() {
    setTimeout(() => {
        document.querySelectorAll('.loading-animation').forEach((el, index) => {
            setTimeout(() => {
                el.style.opacity = '1';
                el.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }, 100);
}

// Auto-refresh data every 5 minutes
setInterval(() => {
    vehicleAPI.clearCache();
    if (document.getElementById('featured-vehicles').children.length > 0) {
        loadFeaturedVehicles();
    }
    if (!document.getElementById('all-vehicles-section').classList.contains('d-none')) {
        loadVehicles(currentPage);
    }
    updateVehicleCount();
}, 5 * 60 * 1000);

// Initialize real-time data loading
document.addEventListener('DOMContentLoaded', function() {
    // Replace the existing initialization with real API calls
    setTimeout(() => {
        loadFeaturedVehicles();
        loadCategories();
        updateVehicleCount();
    }, 100);
});

// Export for global use
window.vehicleAPI = vehicleAPI;
window.loadFeaturedVehicles = loadFeaturedVehicles;
window.loadVehicles = loadVehicles;
window.searchVehicles = searchVehicles;
window.applyFilters = applyFilters;
window.clearFilters = clearFilters;
window.filterByCategory = filterByCategory;
window.viewVehicleDetails = viewVehicleDetails;