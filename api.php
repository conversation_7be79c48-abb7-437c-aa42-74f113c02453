<?php
// =============================================
// VEHICLE API ENDPOINT
// =============================================

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Include configuration
require_once 'config.php';

// Get request method and action
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

try {
    switch ($action) {
        case 'featured':
            echo json_encode(getFeaturedVehicles());
            break;
            
        case 'vehicles':
            echo json_encode(getVehicles());
            break;
            
        case 'vehicle':
            $id = (int)($_GET['id'] ?? 0);
            echo json_encode(getVehicleDetails($id));
            break;
            
        case 'categories':
            echo json_encode(getCategories());
            break;
            
        case 'stats':
            echo json_encode(getStats());
            break;
            
        case 'search':
            echo json_encode(searchVehicles());
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Server error: ' . $e->getMessage()]);
}

// =============================================
// API FUNCTIONS
// =============================================

function getFeaturedVehicles() {
    global $db;
    
    $query = "
        SELECT v.*, u.company_name, u.username,
        (SELECT image_path FROM vehicle_images WHERE vehicle_id = v.vehicle_id AND is_primary = 1 LIMIT 1) as primary_image
        FROM vehicles v
        JOIN users u ON v.dealer_id = u.user_id
        WHERE v.status = 'available' AND v.featured = 1
        ORDER BY v.created_at DESC
        LIMIT 6
    ";
    
    $result = $db->query($query);
    $vehicles = [];
    
    while ($row = $result->fetch_assoc()) {
        $vehicles[] = formatVehicleData($row);
    }
    
    return [
        'success' => true,
        'data' => $vehicles,
        'count' => count($vehicles)
    ];
}

function getVehicles() {
    global $db;
    
    // Get filters from request
    $filters = [
        'category' => $_GET['category'] ?? '',
        'region' => $_GET['region'] ?? '',
        'make' => $_GET['make'] ?? '',
        'min_price' => $_GET['min_price'] ?? '',
        'max_price' => $_GET['max_price'] ?? '',
        'search' => $_GET['search'] ?? '',
        'sort' => $_GET['sort'] ?? 'newest',
        'page' => (int)($_GET['page'] ?? 1),
        'limit' => (int)($_GET['limit'] ?? 12)
    ];
    
    // Build query
    $sql = "
        SELECT v.*, u.company_name, u.username,
        (SELECT image_path FROM vehicle_images WHERE vehicle_id = v.vehicle_id AND is_primary = 1 LIMIT 1) as primary_image
        FROM vehicles v
        JOIN users u ON v.dealer_id = u.user_id
        WHERE v.status = 'available'
    ";
    
    $countSql = "SELECT COUNT(*) as total FROM vehicles v WHERE v.status = 'available'";
    
    $params = [];
    $types = "";
    
    // Apply filters
    if (!empty($filters['category'])) {
        $sql .= " AND v.category = ?";
        $countSql .= " AND v.category = ?";
        $params[] = $filters['category'];
        $types .= "s";
    }
    
    if (!empty($filters['region'])) {
        $sql .= " AND v.region = ?";
        $countSql .= " AND v.region = ?";
        $params[] = $filters['region'];
        $types .= "s";
    }
    
    if (!empty($filters['make'])) {
        $sql .= " AND v.make LIKE ?";
        $countSql .= " AND v.make LIKE ?";
        $params[] = "%" . $filters['make'] . "%";
        $types .= "s";
    }
    
    if (!empty($filters['min_price'])) {
        $sql .= " AND v.price >= ?";
        $countSql .= " AND v.price >= ?";
        $params[] = $filters['min_price'];
        $types .= "d";
    }
    
    if (!empty($filters['max_price'])) {
        $sql .= " AND v.price <= ?";
        $countSql .= " AND v.price <= ?";
        $params[] = $filters['max_price'];
        $types .= "d";
    }
    
    if (!empty($filters['search'])) {
        $sql .= " AND (v.make LIKE ? OR v.model LIKE ? OR v.description LIKE ?)";
        $countSql .= " AND (v.make LIKE ? OR v.model LIKE ? OR v.description LIKE ?)";
        $searchTerm = "%" . $filters['search'] . "%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $types .= "sss";
    }
    
    // Get total count
    $countStmt = $db->prepare($countSql);
    if (!empty($params)) {
        $countStmt->bind_param($types, ...$params);
    }
    $countStmt->execute();
    $totalCount = $countStmt->get_result()->fetch_assoc()['total'];
    
    // Apply sorting
    switch ($filters['sort']) {
        case 'price_asc':
            $sql .= " ORDER BY v.price ASC";
            break;
        case 'price_desc':
            $sql .= " ORDER BY v.price DESC";
            break;
        case 'year_desc':
            $sql .= " ORDER BY v.year DESC";
            break;
        case 'year_asc':
            $sql .= " ORDER BY v.year ASC";
            break;
        default:
            $sql .= " ORDER BY v.featured DESC, v.created_at DESC";
            break;
    }
    
    // Apply pagination
    $offset = ($filters['page'] - 1) * $filters['limit'];
    $sql .= " LIMIT ? OFFSET ?";
    $params[] = $filters['limit'];
    $params[] = $offset;
    $types .= "ii";
    
    // Execute main query
    $stmt = $db->prepare($sql);
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();
    
    $vehicles = [];
    while ($row = $result->fetch_assoc()) {
        $vehicles[] = formatVehicleData($row);
    }
    
    return [
        'success' => true,
        'data' => $vehicles,
        'pagination' => [
            'page' => $filters['page'],
            'limit' => $filters['limit'],
            'total' => (int)$totalCount,
            'pages' => ceil($totalCount / $filters['limit'])
        ],
        'filters' => $filters
    ];
}

function getVehicleDetails($vehicleId) {
    global $db;
    
    if (!$vehicleId) {
        return ['success' => false, 'error' => 'Vehicle ID required'];
    }
    
    // Get vehicle details
    $stmt = $db->prepare("
        SELECT v.*, u.username, u.company_name, u.phone, u.email 
        FROM vehicles v
        JOIN users u ON v.dealer_id = u.user_id
        WHERE v.vehicle_id = ?
    ");
    
    $stmt->bind_param("i", $vehicleId);
    $stmt->execute();
    $vehicle = $stmt->get_result()->fetch_assoc();
    
    if (!$vehicle) {
        return ['success' => false, 'error' => 'Vehicle not found'];
    }
    
    // Get vehicle images
    $stmt = $db->prepare("
        SELECT * FROM vehicle_images 
        WHERE vehicle_id = ? 
        ORDER BY is_primary DESC, uploaded_at ASC
    ");
    $stmt->bind_param("i", $vehicleId);
    $stmt->execute();
    $imagesResult = $stmt->get_result();
    
    $images = [];
    while ($image = $imagesResult->fetch_assoc()) {
        $images[] = [
            'image_id' => $image['image_id'],
            'image_path' => $image['image_path'],
            'is_primary' => (bool)$image['is_primary']
        ];
    }
    
    // Get vehicle videos if any
    $stmt = $db->prepare("SELECT * FROM vehicle_videos WHERE vehicle_id = ?");
    $stmt->bind_param("i", $vehicleId);
    $stmt->execute();
    $videosResult = $stmt->get_result();
    
    $videos = [];
    while ($video = $videosResult->fetch_assoc()) {
        $videos[] = [
            'video_id' => $video['video_id'],
            'video_path' => $video['video_path'],
            'thumbnail_path' => $video['thumbnail_path']
        ];
    }
    
    return [
        'success' => true,
        'data' => [
            'vehicle' => formatVehicleData($vehicle),
            'images' => $images,
            'videos' => $videos
        ]
    ];
}

function getCategories() {
    global $db;
    
    $categories = get_categories();
    $categoryData = [];
    
    foreach ($categories as $key => $name) {
        $count = get_vehicle_count($key);
        $categoryData[] = [
            'key' => $key,
            'name' => $name,
            'count' => (int)$count,
            'icon' => getCategoryIcon($key)
        ];
    }
    
    return [
        'success' => true,
        'data' => $categoryData
    ];
}

function getStats() {
    global $db;
    
    $totalVehicles = $db->query("SELECT COUNT(*) FROM vehicles WHERE status = 'available'")->fetch_row()[0];
    $totalDealers = $db->query("SELECT COUNT(*) FROM users WHERE user_type = 'dealer'")->fetch_row()[0];
    $totalCustomers = $db->query("SELECT COUNT(*) FROM users WHERE user_type = 'customer'")->fetch_row()[0];
    $featuredCount = $db->query("SELECT COUNT(*) FROM vehicles WHERE status = 'available' AND featured = 1")->fetch_row()[0];
    
    return [
        'success' => true,
        'data' => [
            'total_vehicles' => (int)$totalVehicles,
            'total_dealers' => (int)$totalDealers,
            'total_customers' => (int)$totalCustomers,
            'featured_vehicles' => (int)$featuredCount
        ]
    ];
}

function searchVehicles() {
    // This is similar to getVehicles but specifically for search
    return getVehicles();
}

// =============================================
// HELPER FUNCTIONS
// =============================================

function formatVehicleData($vehicle) {
    return [
        'vehicle_id' => (int)$vehicle['vehicle_id'],
        'make' => $vehicle['make'],
        'model' => $vehicle['model'],
        'year' => (int)$vehicle['year'],
        'price' => (float)$vehicle['price'],
        'mileage' => (int)$vehicle['mileage'],
        'category' => $vehicle['category'],
        'region' => $vehicle['region'],
        'city' => $vehicle['city'],
        'fuel_type' => $vehicle['fuel_type'],
        'transmission' => $vehicle['transmission'],
        'color' => $vehicle['color'] ?? '',
        'engine_type' => $vehicle['engine_type'] ?? '',
        'description' => $vehicle['description'] ?? '',
        'featured' => (bool)($vehicle['featured'] ?? false),
        'status' => $vehicle['status'],
        'primary_image' => $vehicle['primary_image'] ?? '/images/no-image.jpg',
        'company_name' => $vehicle['company_name'],
        'username' => $vehicle['username'],
        'dealer_phone' => $vehicle['phone'] ?? '',
        'dealer_email' => $vehicle['email'] ?? '',
        'created_at' => $vehicle['created_at'],
        'formatted_price' => 'R ' . number_format($vehicle['price'], 0),
        'formatted_mileage' => number_format($vehicle['mileage']) . ' km'
    ];
}

function getCategoryIcon($category) {
    $icons = [
        'truck' => 'bi-truck',
        'car' => 'bi-car-front-fill',
        'suv' => 'bi-truck-front-fill',
        'bakkie' => 'bi-truck-flatbed',
        'commercial' => 'bi-box-seam',
        'machinery' => 'bi-gear',
        'trailer' => 'bi-truck',
        'bus' => 'bi-bus-front-fill',
        'spares' => 'bi-tools',
        'farm_equipment' => 'bi-tractor',
        'animal_farming' => 'bi-house',
        'auction' => 'bi-hammer'
    ];
    
    return $icons[$category] ?? 'bi-truck';
}
?>